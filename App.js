import { StyleSheet, Text, View } from 'react-native'
import React, { useEffect } from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AuthStack from './src/Navigations/StackNavigation';
import { NavigationContainer } from '@react-navigation/native';
import DrawerNavigator from './src/Navigations/DrawerNavigator';
import { store } from './src/Redux/store';
import { Provider } from 'react-redux';
import { initializeNotifications, setupNotificationListeners } from './src/Services/notificationServices';
const Stack = createNativeStackNavigator();

const App = () => {
  useEffect(() => {
    initializeNotifications(); // Initialize notifications
  }, []);

  useEffect(() => {
    const unsubscribe = setupNotificationListeners();
    return () => unsubscribe(); // Clean up listener on unmount
  }, []);

  return (
    <Provider store={store}>
    <NavigationContainer>
    <Stack.Navigator
      initialRouteName="AuthStack"
      screenOptions={{
        headerShown: false,
      }}
    >
      {/* Stack for screens without the drawer */}
      <Stack.Screen
        name="AuthStack"
        component={AuthStack}
      />

          <Stack.Screen
            name="AppStack"
            component={DrawerNavigator}
          />
      
    </Stack.Navigator>
  </NavigationContainer>
  </Provider>
  )
}

export default App

const styles = StyleSheet.create({})