{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/drawer": "^7.2.1", "@react-navigation/material-top-tabs": "^7.2.3", "@react-navigation/native-stack": "^7.3.1", "@react-navigation/stack": "^7.2.1", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "libphonenumber-js": "^1.12.6", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.77.1", "react-native-animatable": "^1.4.0", "react-native-animated-spinkit": "^1.5.2", "react-native-calendars": "^1.1310.0", "react-native-chart-kit": "^6.12.0", "react-native-dropdown-picker": "^5.4.6", "react-native-element-dropdown": "^2.12.4", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-image-picker": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.13.1", "react-native-phone-number-input": "^2.1.0", "react-native-progress-steps": "^2.0.3", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "^15.11.2", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}