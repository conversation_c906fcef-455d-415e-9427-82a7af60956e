<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>JohnBarber</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app requires access to the camera to take photos.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires access to the microphone for audio recording.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to your photo library to allow image uploads.</string>
	<key>UIAppFonts</key>
	<array>
		<string>PlayfairDisplay-Black.ttf</string>
		<string>Gilroy-Bold.ttf</string>
		<string>Gilroy-Heavy.ttf</string>
		<string>Gilroy-Light.ttf</string>
		<string>Gilroy-Medium.ttf</string>
		<string>Gilroy-Regular.ttf</string>
		<string>PlusJakartaSans-Bold.ttf</string>
		<string>PlusJakartaSans-BoldItalic.ttf</string>
		<string>PlusJakartaSans-ExtraBold.ttf</string>
		<string>PlusJakartaSans-ExtraBoldItalic.ttf</string>
		<string>PlusJakartaSans-ExtraLight.ttf</string>
		<string>PlusJakartaSans-ExtraLightItalic.ttf</string>
		<string>PlusJakartaSans-Italic-VariableFont_wght.ttf</string>
		<string>PlusJakartaSans-Italic.ttf</string>
		<string>PlusJakartaSans-Light.ttf</string>
		<string>PlusJakartaSans-LightItalic.ttf</string>
		<string>PlusJakartaSans-Medium.ttf</string>
		<string>PlusJakartaSans-MediumItalic.ttf</string>
		<string>PlusJakartaSans-Regular.ttf</string>
		<string>PlusJakartaSans-SemiBold.ttf</string>
		<string>PlusJakartaSans-SemiBoldItalic.ttf</string>
		<string>PlusJakartaSans-VariableFont_wght.ttf</string>
		<string>Poppins-Black.ttf</string>
		<string>Poppins-BlackItalic.ttf</string>
		<string>Poppins-Bold.ttf</string>
		<string>Poppins-BoldItalic.ttf</string>
		<string>Poppins-ExtraBold.ttf</string>
		<string>Poppins-ExtraBoldItalic.ttf</string>
		<string>Poppins-ExtraLight.ttf</string>
		<string>Poppins-ExtraLightItalic.ttf</string>
		<string>Poppins-Italic.ttf</string>
		<string>Poppins-Light.ttf</string>
		<string>Poppins-LightItalic.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-MediumItalic.ttf</string>
		<string>Poppins-Regular.ttf</string>
		<string>Poppins-SemiBold.ttf</string>
		<string>Poppins-SemiBoldItalic.ttf</string>
		<string>Poppins-Thin.ttf</string>
		<string>Poppins-ThinItalic.ttf</string>
		<string>Sen-Bold.ttf</string>
		<string>Sen-ExtraBold.ttf</string>
		<string>Sen-Medium.ttf</string>
		<string>Sen-Regular.ttf</string>
		<string>Sen-SemiBold.ttf</string>
		<string>Sen-VariableFont_wght.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
