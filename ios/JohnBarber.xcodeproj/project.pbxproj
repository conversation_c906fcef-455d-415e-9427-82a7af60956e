// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		074940B315DA44CAA9C073CB /* PlusJakartaSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 724EBCE647304E748BF7A5D5 /* PlusJakartaSans-ExtraBold.ttf */; };
		0A23DE079C6A4F0BB60AB55E /* Sen-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 61A303B11DA3411EAC8C8F56 /* Sen-Regular.ttf */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		15D5EB858595458096F22B4D /* Sen-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0E9D7AC8445C4242A898CF19 /* Sen-SemiBold.ttf */; };
		17C379190962B1B439437AF0 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		1DAD51D36ACA4287B1FF644B /* PlusJakartaSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BDA67F38065E43C581F457CE /* PlusJakartaSans-Regular.ttf */; };
		392D723986E04E9AA78FA190 /* Sen-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 446C6DD653EE43718F337468 /* Sen-ExtraBold.ttf */; };
		3B8F79F93CDB43C2823F427F /* Poppins-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 925511B56E0D40A7B5C9676B /* Poppins-Medium.ttf */; };
		3CD8B7EE1DE246B4A030C819 /* Poppins-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C965181465B44CED981A7705 /* Poppins-ExtraBoldItalic.ttf */; };
		3EF497AE34934848AEB338DC /* PlusJakartaSans-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 90A8B93518564CE38742E74C /* PlusJakartaSans-SemiBoldItalic.ttf */; };
		3FBEFC077BE94AF194D08A77 /* PlusJakartaSans-VariableFont_wght.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B3ACCB8EBEB44CF4AD9D70E6 /* PlusJakartaSans-VariableFont_wght.ttf */; };
		40B2CAF0BF894CDFB1535DF7 /* Poppins-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 163EF812188348B9A532C656 /* Poppins-ExtraLightItalic.ttf */; };
		4873C3E135C94DEE805BBE7A /* Poppins-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4125EC12D044DC2936A8193 /* Poppins-LightItalic.ttf */; };
		4CA1E2FD08324D38ACDF07F2 /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9AA17C823CF947E3B6953265 /* Poppins-SemiBold.ttf */; };
		5105C212313A43B0BA06B839 /* PlusJakartaSans-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 284A7636F5AE4C6295B7CBE3 /* PlusJakartaSans-ExtraLight.ttf */; };
		52E457907A084EA9B44656D9 /* PlusJakartaSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 717B472CEFF54C29AA60C3E0 /* PlusJakartaSans-BoldItalic.ttf */; };
		54E0030DFCD4426E9242E313 /* PlusJakartaSans-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D699EA9170344462A84A34EF /* PlusJakartaSans-Medium.ttf */; };
		551A5354188B49A4816E043A /* Poppins-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C57B82863475419588215E31 /* Poppins-Thin.ttf */; };
		579B40905C7C48A9A930FE88 /* Poppins-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7FAAC72031BD456CA0734B1D /* Poppins-ExtraLight.ttf */; };
		5F738670168141258410DC4B /* Poppins-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 65AEFD86DEBB488E87F9E612 /* Poppins-BlackItalic.ttf */; };
		606EE1549C734A73BFF31DA2 /* Poppins-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C2856A81AAFE436A91DF6CDA /* Poppins-ExtraBold.ttf */; };
		669DD13009784677870E38C5 /* PlusJakartaSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7CFCEF4DA4894AD1B06692AC /* PlusJakartaSans-LightItalic.ttf */; };
		6BFAC11CF06545EC82583BE6 /* Poppins-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E72591512D2F4C5C99601956 /* Poppins-Italic.ttf */; };
		6C768C3CFB8142A3BC793C83 /* Sen-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A2DE2A52A4AB4D6CA62F11B1 /* Sen-Medium.ttf */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		77BE33B4D6794F72A9F7E3A0 /* Gilroy-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5532841FD5B54D38B6A3640B /* Gilroy-Medium.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		83A6003CC6224F6B8851D986 /* PlusJakartaSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C5ABA634ED09480FB4417720 /* PlusJakartaSans-ExtraBoldItalic.ttf */; };
		8BD3A4033B61BAEC43B77ABD /* Pods_JohnBarber.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4C19A2457067FD3FA231E3D1 /* Pods_JohnBarber.framework */; };
		8D76DDA2E75240B1A21CA0D3 /* Gilroy-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A6AABEBD40894056A55F4C65 /* Gilroy-Heavy.ttf */; };
		8E4CD469C813401CAB157F80 /* Poppins-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C32545510046444497E8F8A1 /* Poppins-SemiBoldItalic.ttf */; };
		95A37A7E359C4A58A685D1D5 /* PlusJakartaSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A76E2DFF6E2A4B88A4803789 /* PlusJakartaSans-Light.ttf */; };
		96CE6FC65EB14759B5E94176 /* Gilroy-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A86776E3270C4362867465C7 /* Gilroy-Light.ttf */; };
		A105ABAED11B41AFA3C3CE59 /* Poppins-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A9118108320D46D3B57065F6 /* Poppins-Black.ttf */; };
		A14464BD5C9F40419EACDB1D /* PlusJakartaSans-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7F9E0F9B53F24DBE918E65B0 /* PlusJakartaSans-ExtraLightItalic.ttf */; };
		A577585159BF4F8AA921C047 /* Poppins-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F5A5CA8F1F4452B82A1F804 /* Poppins-BoldItalic.ttf */; };
		A7536F8239084AE0ACAB7D0E /* PlusJakartaSans-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 397CA108D80B4A59B1B623E9 /* PlusJakartaSans-SemiBold.ttf */; };
		B4FFE27F6CE74481A0964F56 /* Gilroy-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 37915E12157A489C9B93A8D6 /* Gilroy-Bold.ttf */; };
		B5DBC6C8E0CF43ADA54EB115 /* Poppins-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 124A10B562A143C8AC9EC03A /* Poppins-ThinItalic.ttf */; };
		BC06B3E0560F4E1EB66231CB /* PlusJakartaSans-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 60A8DBD2127D4A64A2B34086 /* PlusJakartaSans-MediumItalic.ttf */; };
		C49207BECDDF4146B0862B07 /* PlusJakartaSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C425C25997F84DECBEF2C313 /* PlusJakartaSans-Italic.ttf */; };
		C6286AC72B7D4491965A7E2F /* PlusJakartaSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CABC2509A2A04DF1BB755291 /* PlusJakartaSans-Bold.ttf */; };
		D17180DB080F424B88A65529 /* Sen-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 448F5C5FAF9D410E80D1AD5C /* Sen-Bold.ttf */; };
		D3E8C3759EA84EF98636FA59 /* Gilroy-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EF859325A4E946F4A8EC9723 /* Gilroy-Regular.ttf */; };
		D5814A03FF324203B8D7490B /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3A07BB0AE6754C77A447B3B0 /* Poppins-Light.ttf */; };
		DD922C2C9108478E8ED728FF /* Sen-VariableFont_wght.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D1FCB42F387B409ABC6D6497 /* Sen-VariableFont_wght.ttf */; };
		DDF214A487C74E18B7328638 /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A5128F7E86A24350B8A2B135 /* Poppins-Bold.ttf */; };
		E1E42EB4F08F4929B8591F05 /* Poppins-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF09595F5D0848DEAAFF702F /* Poppins-MediumItalic.ttf */; };
		E4BC04EC64F845ED96F2DD01 /* PlayfairDisplay-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1F7CFF0A873440BAB334C42B /* PlayfairDisplay-Black.ttf */; };
		E57EE449D8AF4ACE96D87904 /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D0C5C7EBD6B64735B02DA909 /* Poppins-Regular.ttf */; };
		EC5CDD192DDE6695006585F0 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = EC5CDD182DDE6695006585F0 /* GoogleService-Info.plist */; };
		FC7D522C7FFA4078B8B3AD94 /* PlusJakartaSans-Italic-VariableFont_wght.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 26489718AAD34394B37CF492 /* PlusJakartaSans-Italic-VariableFont_wght.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0E9D7AC8445C4242A898CF19 /* Sen-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-SemiBold.ttf"; path = "../src/Assets/fonts/Sen/Sen-SemiBold.ttf"; sourceTree = "<group>"; };
		124A10B562A143C8AC9EC03A /* Poppins-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ThinItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-ThinItalic.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* JohnBarber.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JohnBarber.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = JohnBarber/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = JohnBarber/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = JohnBarber/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		163EF812188348B9A532C656 /* Poppins-ExtraLightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraLightItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		1F7CFF0A873440BAB334C42B /* PlayfairDisplay-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlayfairDisplay-Black.ttf"; path = "../src/Assets/fonts/PlayfairDisplay-Black.ttf"; sourceTree = "<group>"; };
		26489718AAD34394B37CF492 /* PlusJakartaSans-Italic-VariableFont_wght.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Italic-VariableFont_wght.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Italic-VariableFont_wght.ttf"; sourceTree = "<group>"; };
		284A7636F5AE4C6295B7CBE3 /* PlusJakartaSans-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-ExtraLight.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-ExtraLight.ttf"; sourceTree = "<group>"; };
		37915E12157A489C9B93A8D6 /* Gilroy-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Gilroy-Bold.ttf"; path = "../src/Assets/fonts/Gilroy/Gilroy-Bold.ttf"; sourceTree = "<group>"; };
		397CA108D80B4A59B1B623E9 /* PlusJakartaSans-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-SemiBold.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-SemiBold.ttf"; sourceTree = "<group>"; };
		3A07BB0AE6754C77A447B3B0 /* Poppins-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Light.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Light.ttf"; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-JohnBarber.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JohnBarber.debug.xcconfig"; path = "Target Support Files/Pods-JohnBarber/Pods-JohnBarber.debug.xcconfig"; sourceTree = "<group>"; };
		446C6DD653EE43718F337468 /* Sen-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-ExtraBold.ttf"; path = "../src/Assets/fonts/Sen/Sen-ExtraBold.ttf"; sourceTree = "<group>"; };
		448F5C5FAF9D410E80D1AD5C /* Sen-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-Bold.ttf"; path = "../src/Assets/fonts/Sen/Sen-Bold.ttf"; sourceTree = "<group>"; };
		4C19A2457067FD3FA231E3D1 /* Pods_JohnBarber.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JohnBarber.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5532841FD5B54D38B6A3640B /* Gilroy-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Gilroy-Medium.ttf"; path = "../src/Assets/fonts/Gilroy/Gilroy-Medium.ttf"; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-JohnBarber.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JohnBarber.release.xcconfig"; path = "Target Support Files/Pods-JohnBarber/Pods-JohnBarber.release.xcconfig"; sourceTree = "<group>"; };
		60A8DBD2127D4A64A2B34086 /* PlusJakartaSans-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-MediumItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-MediumItalic.ttf"; sourceTree = "<group>"; };
		61A303B11DA3411EAC8C8F56 /* Sen-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-Regular.ttf"; path = "../src/Assets/fonts/Sen/Sen-Regular.ttf"; sourceTree = "<group>"; };
		65AEFD86DEBB488E87F9E612 /* Poppins-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-BlackItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-BlackItalic.ttf"; sourceTree = "<group>"; };
		717B472CEFF54C29AA60C3E0 /* PlusJakartaSans-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-BoldItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-BoldItalic.ttf"; sourceTree = "<group>"; };
		724EBCE647304E748BF7A5D5 /* PlusJakartaSans-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-ExtraBold.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-ExtraBold.ttf"; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = JohnBarber/AppDelegate.swift; sourceTree = "<group>"; };
		7CFCEF4DA4894AD1B06692AC /* PlusJakartaSans-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-LightItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-LightItalic.ttf"; sourceTree = "<group>"; };
		7F9E0F9B53F24DBE918E65B0 /* PlusJakartaSans-ExtraLightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-ExtraLightItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		7FAAC72031BD456CA0734B1D /* Poppins-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraLight.ttf"; path = "../src/Assets/fonts/Popins/Poppins-ExtraLight.ttf"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = JohnBarber/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8F5A5CA8F1F4452B82A1F804 /* Poppins-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-BoldItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-BoldItalic.ttf"; sourceTree = "<group>"; };
		90A8B93518564CE38742E74C /* PlusJakartaSans-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-SemiBoldItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		925511B56E0D40A7B5C9676B /* Poppins-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Medium.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Medium.ttf"; sourceTree = "<group>"; };
		9AA17C823CF947E3B6953265 /* Poppins-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-SemiBold.ttf"; path = "../src/Assets/fonts/Popins/Poppins-SemiBold.ttf"; sourceTree = "<group>"; };
		A2DE2A52A4AB4D6CA62F11B1 /* Sen-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-Medium.ttf"; path = "../src/Assets/fonts/Sen/Sen-Medium.ttf"; sourceTree = "<group>"; };
		A5128F7E86A24350B8A2B135 /* Poppins-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Bold.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Bold.ttf"; sourceTree = "<group>"; };
		A6AABEBD40894056A55F4C65 /* Gilroy-Heavy.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Gilroy-Heavy.ttf"; path = "../src/Assets/fonts/Gilroy/Gilroy-Heavy.ttf"; sourceTree = "<group>"; };
		A76E2DFF6E2A4B88A4803789 /* PlusJakartaSans-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Light.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Light.ttf"; sourceTree = "<group>"; };
		A86776E3270C4362867465C7 /* Gilroy-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Gilroy-Light.ttf"; path = "../src/Assets/fonts/Gilroy/Gilroy-Light.ttf"; sourceTree = "<group>"; };
		A9118108320D46D3B57065F6 /* Poppins-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Black.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Black.ttf"; sourceTree = "<group>"; };
		AF09595F5D0848DEAAFF702F /* Poppins-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-MediumItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-MediumItalic.ttf"; sourceTree = "<group>"; };
		B3ACCB8EBEB44CF4AD9D70E6 /* PlusJakartaSans-VariableFont_wght.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-VariableFont_wght.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-VariableFont_wght.ttf"; sourceTree = "<group>"; };
		B4125EC12D044DC2936A8193 /* Poppins-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-LightItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-LightItalic.ttf"; sourceTree = "<group>"; };
		BDA67F38065E43C581F457CE /* PlusJakartaSans-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Regular.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Regular.ttf"; sourceTree = "<group>"; };
		C2856A81AAFE436A91DF6CDA /* Poppins-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraBold.ttf"; path = "../src/Assets/fonts/Popins/Poppins-ExtraBold.ttf"; sourceTree = "<group>"; };
		C32545510046444497E8F8A1 /* Poppins-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-SemiBoldItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		C425C25997F84DECBEF2C313 /* PlusJakartaSans-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Italic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Italic.ttf"; sourceTree = "<group>"; };
		C57B82863475419588215E31 /* Poppins-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Thin.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Thin.ttf"; sourceTree = "<group>"; };
		C5ABA634ED09480FB4417720 /* PlusJakartaSans-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-ExtraBoldItalic.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		C965181465B44CED981A7705 /* Poppins-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraBoldItalic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		CABC2509A2A04DF1BB755291 /* PlusJakartaSans-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Bold.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Bold.ttf"; sourceTree = "<group>"; };
		D0C5C7EBD6B64735B02DA909 /* Poppins-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Regular.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Regular.ttf"; sourceTree = "<group>"; };
		D1FCB42F387B409ABC6D6497 /* Sen-VariableFont_wght.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Sen-VariableFont_wght.ttf"; path = "../src/Assets/fonts/Sen/Sen-VariableFont_wght.ttf"; sourceTree = "<group>"; };
		D699EA9170344462A84A34EF /* PlusJakartaSans-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "PlusJakartaSans-Medium.ttf"; path = "../src/Assets/fonts/Plus_Jhatkara/PlusJakartaSans-Medium.ttf"; sourceTree = "<group>"; };
		E72591512D2F4C5C99601956 /* Poppins-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Italic.ttf"; path = "../src/Assets/fonts/Popins/Poppins-Italic.ttf"; sourceTree = "<group>"; };
		EC5CDD182DDE6695006585F0 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		EC5CDD1A2DDE6B94006585F0 /* JohnBarber.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = JohnBarber.entitlements; path = JohnBarber/JohnBarber.entitlements; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EF859325A4E946F4A8EC9723 /* Gilroy-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Gilroy-Regular.ttf"; path = "../src/Assets/fonts/Gilroy/Gilroy-Regular.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8BD3A4033B61BAEC43B77ABD /* Pods_JohnBarber.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* JohnBarber */ = {
			isa = PBXGroup;
			children = (
				EC5CDD1A2DDE6B94006585F0 /* JohnBarber.entitlements */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				EC5CDD182DDE6695006585F0 /* GoogleService-Info.plist */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = JohnBarber;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				4C19A2457067FD3FA231E3D1 /* Pods_JohnBarber.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		50A5CFFC9E7A467C991ADCD7 /* Resources */ = {
			isa = PBXGroup;
			children = (
				1F7CFF0A873440BAB334C42B /* PlayfairDisplay-Black.ttf */,
				37915E12157A489C9B93A8D6 /* Gilroy-Bold.ttf */,
				A6AABEBD40894056A55F4C65 /* Gilroy-Heavy.ttf */,
				A86776E3270C4362867465C7 /* Gilroy-Light.ttf */,
				5532841FD5B54D38B6A3640B /* Gilroy-Medium.ttf */,
				EF859325A4E946F4A8EC9723 /* Gilroy-Regular.ttf */,
				CABC2509A2A04DF1BB755291 /* PlusJakartaSans-Bold.ttf */,
				717B472CEFF54C29AA60C3E0 /* PlusJakartaSans-BoldItalic.ttf */,
				724EBCE647304E748BF7A5D5 /* PlusJakartaSans-ExtraBold.ttf */,
				C5ABA634ED09480FB4417720 /* PlusJakartaSans-ExtraBoldItalic.ttf */,
				284A7636F5AE4C6295B7CBE3 /* PlusJakartaSans-ExtraLight.ttf */,
				7F9E0F9B53F24DBE918E65B0 /* PlusJakartaSans-ExtraLightItalic.ttf */,
				26489718AAD34394B37CF492 /* PlusJakartaSans-Italic-VariableFont_wght.ttf */,
				C425C25997F84DECBEF2C313 /* PlusJakartaSans-Italic.ttf */,
				A76E2DFF6E2A4B88A4803789 /* PlusJakartaSans-Light.ttf */,
				7CFCEF4DA4894AD1B06692AC /* PlusJakartaSans-LightItalic.ttf */,
				D699EA9170344462A84A34EF /* PlusJakartaSans-Medium.ttf */,
				60A8DBD2127D4A64A2B34086 /* PlusJakartaSans-MediumItalic.ttf */,
				BDA67F38065E43C581F457CE /* PlusJakartaSans-Regular.ttf */,
				397CA108D80B4A59B1B623E9 /* PlusJakartaSans-SemiBold.ttf */,
				90A8B93518564CE38742E74C /* PlusJakartaSans-SemiBoldItalic.ttf */,
				B3ACCB8EBEB44CF4AD9D70E6 /* PlusJakartaSans-VariableFont_wght.ttf */,
				A9118108320D46D3B57065F6 /* Poppins-Black.ttf */,
				65AEFD86DEBB488E87F9E612 /* Poppins-BlackItalic.ttf */,
				A5128F7E86A24350B8A2B135 /* Poppins-Bold.ttf */,
				8F5A5CA8F1F4452B82A1F804 /* Poppins-BoldItalic.ttf */,
				C2856A81AAFE436A91DF6CDA /* Poppins-ExtraBold.ttf */,
				C965181465B44CED981A7705 /* Poppins-ExtraBoldItalic.ttf */,
				7FAAC72031BD456CA0734B1D /* Poppins-ExtraLight.ttf */,
				163EF812188348B9A532C656 /* Poppins-ExtraLightItalic.ttf */,
				E72591512D2F4C5C99601956 /* Poppins-Italic.ttf */,
				3A07BB0AE6754C77A447B3B0 /* Poppins-Light.ttf */,
				B4125EC12D044DC2936A8193 /* Poppins-LightItalic.ttf */,
				925511B56E0D40A7B5C9676B /* Poppins-Medium.ttf */,
				AF09595F5D0848DEAAFF702F /* Poppins-MediumItalic.ttf */,
				D0C5C7EBD6B64735B02DA909 /* Poppins-Regular.ttf */,
				9AA17C823CF947E3B6953265 /* Poppins-SemiBold.ttf */,
				C32545510046444497E8F8A1 /* Poppins-SemiBoldItalic.ttf */,
				C57B82863475419588215E31 /* Poppins-Thin.ttf */,
				124A10B562A143C8AC9EC03A /* Poppins-ThinItalic.ttf */,
				448F5C5FAF9D410E80D1AD5C /* Sen-Bold.ttf */,
				446C6DD653EE43718F337468 /* Sen-ExtraBold.ttf */,
				A2DE2A52A4AB4D6CA62F11B1 /* Sen-Medium.ttf */,
				61A303B11DA3411EAC8C8F56 /* Sen-Regular.ttf */,
				0E9D7AC8445C4242A898CF19 /* Sen-SemiBold.ttf */,
				D1FCB42F387B409ABC6D6497 /* Sen-VariableFont_wght.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* JohnBarber */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				50A5CFFC9E7A467C991ADCD7 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* JohnBarber.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-JohnBarber.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-JohnBarber.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* JohnBarber */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "JohnBarber" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				6C12BB6B93DEC407FC706DCC /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = JohnBarber;
			productName = JohnBarber;
			productReference = 13B07F961A680F5B00A75B9A /* JohnBarber.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "JohnBarber" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* JohnBarber */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				17C379190962B1B439437AF0 /* PrivacyInfo.xcprivacy in Resources */,
				E4BC04EC64F845ED96F2DD01 /* PlayfairDisplay-Black.ttf in Resources */,
				B4FFE27F6CE74481A0964F56 /* Gilroy-Bold.ttf in Resources */,
				8D76DDA2E75240B1A21CA0D3 /* Gilroy-Heavy.ttf in Resources */,
				96CE6FC65EB14759B5E94176 /* Gilroy-Light.ttf in Resources */,
				77BE33B4D6794F72A9F7E3A0 /* Gilroy-Medium.ttf in Resources */,
				D3E8C3759EA84EF98636FA59 /* Gilroy-Regular.ttf in Resources */,
				C6286AC72B7D4491965A7E2F /* PlusJakartaSans-Bold.ttf in Resources */,
				52E457907A084EA9B44656D9 /* PlusJakartaSans-BoldItalic.ttf in Resources */,
				074940B315DA44CAA9C073CB /* PlusJakartaSans-ExtraBold.ttf in Resources */,
				83A6003CC6224F6B8851D986 /* PlusJakartaSans-ExtraBoldItalic.ttf in Resources */,
				5105C212313A43B0BA06B839 /* PlusJakartaSans-ExtraLight.ttf in Resources */,
				A14464BD5C9F40419EACDB1D /* PlusJakartaSans-ExtraLightItalic.ttf in Resources */,
				FC7D522C7FFA4078B8B3AD94 /* PlusJakartaSans-Italic-VariableFont_wght.ttf in Resources */,
				C49207BECDDF4146B0862B07 /* PlusJakartaSans-Italic.ttf in Resources */,
				95A37A7E359C4A58A685D1D5 /* PlusJakartaSans-Light.ttf in Resources */,
				669DD13009784677870E38C5 /* PlusJakartaSans-LightItalic.ttf in Resources */,
				54E0030DFCD4426E9242E313 /* PlusJakartaSans-Medium.ttf in Resources */,
				BC06B3E0560F4E1EB66231CB /* PlusJakartaSans-MediumItalic.ttf in Resources */,
				1DAD51D36ACA4287B1FF644B /* PlusJakartaSans-Regular.ttf in Resources */,
				A7536F8239084AE0ACAB7D0E /* PlusJakartaSans-SemiBold.ttf in Resources */,
				3EF497AE34934848AEB338DC /* PlusJakartaSans-SemiBoldItalic.ttf in Resources */,
				3FBEFC077BE94AF194D08A77 /* PlusJakartaSans-VariableFont_wght.ttf in Resources */,
				A105ABAED11B41AFA3C3CE59 /* Poppins-Black.ttf in Resources */,
				5F738670168141258410DC4B /* Poppins-BlackItalic.ttf in Resources */,
				EC5CDD192DDE6695006585F0 /* GoogleService-Info.plist in Resources */,
				DDF214A487C74E18B7328638 /* Poppins-Bold.ttf in Resources */,
				A577585159BF4F8AA921C047 /* Poppins-BoldItalic.ttf in Resources */,
				606EE1549C734A73BFF31DA2 /* Poppins-ExtraBold.ttf in Resources */,
				3CD8B7EE1DE246B4A030C819 /* Poppins-ExtraBoldItalic.ttf in Resources */,
				579B40905C7C48A9A930FE88 /* Poppins-ExtraLight.ttf in Resources */,
				40B2CAF0BF894CDFB1535DF7 /* Poppins-ExtraLightItalic.ttf in Resources */,
				6BFAC11CF06545EC82583BE6 /* Poppins-Italic.ttf in Resources */,
				D5814A03FF324203B8D7490B /* Poppins-Light.ttf in Resources */,
				4873C3E135C94DEE805BBE7A /* Poppins-LightItalic.ttf in Resources */,
				3B8F79F93CDB43C2823F427F /* Poppins-Medium.ttf in Resources */,
				E1E42EB4F08F4929B8591F05 /* Poppins-MediumItalic.ttf in Resources */,
				E57EE449D8AF4ACE96D87904 /* Poppins-Regular.ttf in Resources */,
				4CA1E2FD08324D38ACDF07F2 /* Poppins-SemiBold.ttf in Resources */,
				8E4CD469C813401CAB157F80 /* Poppins-SemiBoldItalic.ttf in Resources */,
				551A5354188B49A4816E043A /* Poppins-Thin.ttf in Resources */,
				B5DBC6C8E0CF43ADA54EB115 /* Poppins-ThinItalic.ttf in Resources */,
				D17180DB080F424B88A65529 /* Sen-Bold.ttf in Resources */,
				392D723986E04E9AA78FA190 /* Sen-ExtraBold.ttf in Resources */,
				6C768C3CFB8142A3BC793C83 /* Sen-Medium.ttf in Resources */,
				0A23DE079C6A4F0BB60AB55E /* Sen-Regular.ttf in Resources */,
				15D5EB858595458096F22B4D /* Sen-SemiBold.ttf in Resources */,
				DD922C2C9108478E8ED728FF /* Sen-VariableFont_wght.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6C12BB6B93DEC407FC706DCC /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JohnBarber-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JohnBarber/Pods-JohnBarber-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-JohnBarber.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = JohnBarber/JohnBarber.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z982ZKW468;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JohnBarber/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = "com.John-Barber";
				PRODUCT_NAME = JohnBarber;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-JohnBarber.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = JohnBarber/JohnBarber.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z982ZKW468;
				INFOPLIST_FILE = JohnBarber/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = "com.John-Barber";
				PRODUCT_NAME = JohnBarber;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "JohnBarber" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "JohnBarber" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
