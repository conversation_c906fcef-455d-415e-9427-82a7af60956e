import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, KeyboardAvoidingView, ActivityIndicator, Alert } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import CustomTextInput from '../CustomTextInput';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { Create_Category , Create_SubCategory } from '../../Services/API/Endpoints/Admin/listCategoary';

const Add_Category_Modal = ({ visible, onClose, onSave, isSubCategory = false, categoryId }) => {
  console.log("categoryId",categoryId)
  const [categoryName, setCategoryName] = useState('');
  const [loading, setLoading] = useState(false);
  const [id, setid] = useState(categoryId);

  const AppText = useAppText();
  const { getDark_Theme, backgroundColor, getTextColor } = useTheme();
  
  const handleSave = () => {
    if (categoryName.trim()) {
      if (isSubCategory) {
        console.log("isSubCategory", isSubCategory)
        handle_Add_Sub_Category();
      } else {
        handleAddCategory();
      }
    }
  };

  const handleAddCategory = async () => {
    if (!categoryName.trim()) return;
  
    setLoading(true);
  
    try {
      const payload = { name: categoryName };
      console.log('👁️ Sending Payload:', payload);
  
      const response = await Create_Category(payload);
      console.log('🟢 Create_Category RESPONSE:', response);
  
      if (response?.id) {
        Alert.alert('Success', 'Category added successfully!', [
          {
            text: 'OK',
            onPress: () => {
              setCategoryName('');
              onSave();
              onClose();
            }
          }
        ]);
      } else {
        console.error('❌ API Request Failed:', response);
        Alert.alert('Error', 'Failed to add category. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error in Creating category:', error);
      Alert.alert('Error', 'Failed to add category. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handle_Add_Sub_Category = async () => {
    if (!categoryName.trim()) return;
      console.log("The caetgrory Id is ", categoryId)
    setLoading(true);

  
    try {
      const payload = { 
        category: categoryId,
        name: categoryName 
      };
      console.log('👁️ Sending SubCategory Payload:', payload);
  
      const response = await Create_SubCategory(payload);
      console.log('🟢 Create_SubCategory RESPONSE:', response);
  
      if (response?.id) {
        Alert.alert('Success', 'Sub-category added successfully!', [
          {
            text: 'OK',
            onPress: () => {
              setCategoryName('');
              onSave();
              onClose();
            }
          }
        ]);
      } else {
        console.error('❌ API Request Failed:', response);
        Alert.alert('Error', 'Failed to add sub-category. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error in Creating sub-category:', error);
      Alert.alert('Error', 'Failed to add sub-category. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex:1}}
      > 
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent,{backgroundColor:backgroundColor}]}>
            <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />

            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Icon source={globalpath.cross} size={wp(5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>

            <CustomTextInput
              label={isSubCategory ? AppText.SUB_CATEGORY_NAME : AppText.CATEGORY_NAME}
              placeholder={isSubCategory ? AppText.ENTER_YOUR_SUB_CATEGORY_NAME : AppText.ENTER_YOUR_CATEGORY_NAME}
              value={categoryName}
              onChangeText={setCategoryName}
            />

            <TouchableOpacity
              style={[
                styles.saveButton,
                {
                  backgroundColor: categoryName.trim() ? colors.Light_theme_maincolour : colors.grey,
                  opacity: categoryName.trim() ? 1 : 0.5,
                },
              ]}
              disabled={!categoryName.trim() || loading}
              onPress={handleSave}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                  {AppText.SAVE}
                </ResponsiveText>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default Add_Category_Modal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    // backgroundColor: colors.white,
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(5),
    paddingBottom: hp(4),
  },
  modalHeader: {
    flexDirection: 'row',
    marginBottom: hp(3),
    alignItems:"flex-end",
    justifyContent:"flex-end",
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
    // marginBottom: hp(1),
  },
  closeButton: {
    padding: wp(2),
    alignItems:"flex-end",
    justifyContent:"flex-end",
  },
  saveButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(2),
    marginBottom:hp(2)
  },
}); 