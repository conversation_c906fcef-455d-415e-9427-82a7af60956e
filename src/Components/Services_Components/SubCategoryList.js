import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import Add_Category_Modal from './Add_Category_Modal';
import useAppText from '../../Custom/AppText';

const SubCategoryList = ({ subCategories, getTextColor, getDark_Theme , categoryId ,  onClose, onSave, }) => {
    const [showAddModal, setShowAddModal] = useState(false);
    const {backgroundColor} = useTheme();
    const AppText = useAppText();
    const handleAddSubCategory = (subCategoryName) => {
        setShowAddModal(false);
      };
      

  return (
    <View style={[styles.container,{backgroundColor:backgroundColor,borderColor:getDark_Theme()}]}>
                <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
          onPress={() => setShowAddModal(true)}
        >
          <ResponsiveText color={colors.white}>{AppText.ADD_SUB_CATEGORY}</ResponsiveText>
        </TouchableOpacity>
        {
  subCategories && subCategories.length > 0 ? (
    subCategories.map((subCategory, index) => (
      <View key={subCategory.id}>
        <View style={styles.subCategoryItem}>
          <ResponsiveText color={getTextColor()} size={4}>
            {subCategory.title}
          </ResponsiveText>
        </View>
        {index < subCategories.length - 1 && (
          <View 
            style={[ 
              styles.borderBottom, 
              { borderBottomColor: getDark_Theme() } 
            ]} 
          />
        )}
      </View>
    ))
  ) : (
    <View style={styles.noSubCategory}>
      <ResponsiveText color={getTextColor()} size={4}>
        No sub-categories records yet
      </ResponsiveText>
    </View>
  )
}

      <Add_Category_Modal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSave={onSave}
        isSubCategory={true}
        categoryId={categoryId}
      />
    </View>
  );
};

export default SubCategoryList;

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
    marginHorizontal: wp(1),
    borderWidth:1,
    borderRadius:wp(1.5),
    padding:wp(4)
  },
  subCategoryItem: {
    paddingVertical: hp(1.5),
    padding:wp(4)
  },
  borderBottom: {
    borderBottomWidth: 1,
    marginHorizontal:wp(-2)
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
    alignSelf:"flex-end"
  },
}); 