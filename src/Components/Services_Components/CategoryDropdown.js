import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { get_all_category, get_sub_category } from '../../../Services/API/Endpoints/Admin/listCategoary';

const DynamicDropdown = ({
  value,
  onChange,
  error,
  title,
  placeholder,
  fetchData,
  dependentOn,
  dependencyAlertMessage,
  isViewMode = false,
  selectedLabel = null,
}) => {
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);

  const selectedItem = options.find(opt => opt.value === value);

  // Determine the title based on view mode
  const getTitle = () => {
    if (isViewMode) {
      return title?.includes('Sub') ? AppText.SUB_CATEGORY_NAME : AppText.CATEGORY_NAME;
    }
    return title || (title?.includes('Sub') ? AppText.CHOOSE_SUB_CATEGORY : AppText.CHOOSE_CATEGORY);
  };

  useEffect(() => {
    if (fetchData && (!dependentOn && dependentOn !== null)) {
      loadOptions();
    }
  }, []);

  useEffect(() => {
    if (dependentOn && fetchData) {
      loadOptions(dependentOn);
    }
  }, [dependentOn]);

  const loadOptions = async (id) => {
    try {
      setLoading(true);
      const response = await fetchData(id);
      const parsed = response.map(item => ({
        label: item.name,
        value: item.id,
      }));
      setOptions(parsed);
    } catch (e) {
      console.error('Failed to load dropdown data', e);
    } finally {
      setLoading(false);
    }
  };

  const handlePress = () => {
    if (isViewMode) return; // Prevent opening dropdown in view mode
    
    if (dependentOn === null && dependencyAlertMessage) {
      Alert.alert('Note', dependencyAlertMessage);
      return;
    }
    setShowDropdown(!showDropdown);
  };

  return (
    <View style={styles.container}>
      <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[0, 0, hp(1), 0]}>
        {getTitle()}
      </ResponsiveText>

      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            borderColor: error ? colors.red : getDark_Theme(),
            backgroundColor: backgroundColor,
            opacity: isViewMode ? 0.7 : 1,
          },
        ]}
        onPress={handlePress}
        disabled={isViewMode}
      >
        <ResponsiveText color={value ? getTextColor() : colors.grey} size={3.8}>
          {selectedLabel || (selectedItem ? selectedItem.label : placeholder)}
        </ResponsiveText>
        {loading ? (
          <ActivityIndicator size="small" color={getTextColor()} />
        ) : (
          !isViewMode && (
            <Icon
              source={showDropdown ? globalpath.arrow_up : globalpath.arrow_down}
              size={wp(5)}
            />
          )
        )}
      </TouchableOpacity>

      {showDropdown && !loading && !isViewMode && (
        <View style={[styles.dropdownContainer, { backgroundColor }]}>
          {options.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.dropdownItem,
                { borderBottomColor: getDark_Theme() }
              ]}
              onPress={() => {
                onChange(option.value, option.label);
                setShowDropdown(false);
              }}
            >
              <ResponsiveText color={getTextColor()}>{option.label}</ResponsiveText>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {error && (
        <ResponsiveText color={colors.red} size={3} margin={[hp(0.5), 0, 0, 0]}>
          {error}
        </ResponsiveText>
      )}
    </View>
  );
};

export default DynamicDropdown;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  dropdownButton: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownContainer: {
    marginTop: hp(0.5),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
    maxHeight: hp(20),
  },
  dropdownItem: {
    padding: wp(4),
    borderBottomWidth: 1,
  },
});
