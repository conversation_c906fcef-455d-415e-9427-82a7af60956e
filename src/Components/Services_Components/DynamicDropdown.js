import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, ActivityIndicator, Alert , ScrollView } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const DynamicDropdown = ({
  value,
  onChange,
  error,
  title,
  placeholder,
  fetchData,
  dependentOn,
  dependencyAlertMessage,
}) => {
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);

  const selectedItem = options.find(opt => opt.value === value);

  useEffect(() => {
    if (fetchData && (!dependentOn && dependentOn !== null)) {
      loadOptions();
    }
  }, []);

  useEffect(() => {
    if (dependentOn && fetchData) {
      loadOptions(dependentOn);
    }
  }, [dependentOn]);

  const loadOptions = async (id) => {
    try {
      setLoading(true);
      const response = await fetchData(id);
      const parsed = response.map(item => ({
        label: item.name,
        value: item.id,
      }));
      setOptions(parsed);
    } catch (e) {
      console.error('Failed to load dropdown data', e);
    } finally {
      setLoading(false);
    }
  };

  const handlePress = () => {
    if (dependentOn === null && dependencyAlertMessage) {
      Alert.alert('Note', dependencyAlertMessage);
      return;
    }
    setShowDropdown(!showDropdown);
  };

  return (
    <View style={styles.container}>
      <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[0, 0, hp(1), 0]}>
        {title}
      </ResponsiveText>

      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            borderColor: error ? colors.red : getDark_Theme(),
            backgroundColor: backgroundColor,
          },
        ]}
        onPress={handlePress}
      >
        <ResponsiveText color={value ? getTextColor() : colors.grey} size={3.8}>
          {selectedItem ? selectedItem.label : placeholder}
        </ResponsiveText>
        {loading ? (
          <ActivityIndicator size="small" color={getTextColor()} />
        ) : (
          <Icon
            source={showDropdown ? globalpath.arrow_up : globalpath.arrow_down}
            size={wp(5)}
          />
        )}
      </TouchableOpacity>

      {showDropdown && !loading && (
  <View style={[styles.dropdownContainer, { backgroundColor }]}>
    <ScrollView nestedScrollEnabled={true}>
      {options.map(option => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.dropdownItem,
            { borderBottomColor: getDark_Theme() }
          ]}
          onPress={() => {
            onChange(option.value);
            setShowDropdown(false);
          }}
        >
          <ResponsiveText color={getTextColor()}>{option.label}</ResponsiveText>
        </TouchableOpacity>
      ))}
    </ScrollView>
  </View>
)}

      {error && (
        <ResponsiveText color={colors.red} size={3} margin={[hp(0.5), 0, 0, 0]}>
          {error}
        </ResponsiveText>
      )}
    </View>
  );
};

export default DynamicDropdown;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  dropdownButton: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownContainer: {
    marginTop: hp(0.5),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
    maxHeight: hp(20),
  },
  dropdownItem: {
    padding: wp(4),
    borderBottomWidth: 1,
  },
});
