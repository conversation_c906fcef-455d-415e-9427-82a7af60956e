import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  ScrollView,
} from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';

// Function to generate a random color
const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

// Function to generate a palette of colors
const generateColorPalette = (count = 40) => {
  const palette = [];

  // Add some predefined colors (excluding Light_theme_maincolour and c_green)
  const predefinedColors = [
    '#FF5252', // Red
    '#FF9800', // Orange
    '#FFEB3B', // Yellow
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#673AB7', // Purple
    '#E91E63', // Pink
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#009688', // Teal
    '#00BCD4', // Cyan
    '#3F51B5', // Indigo
    '#9C27B0', // Deep Purple
    '#FFC107', // Amber
    '#CDDC39', // Lime
    '#8BC34A', // Light Green
    '#F44336', // Red
    '#9E9E9E', // Grey
    '#03A9F4', // Light Blue
    '#D32F2F', // Dark Red
  ];

  // Add predefined colors to palette
  palette.push(...predefinedColors);

  // Generate random colors to fill the rest
  for (let i = predefinedColors.length; i < count; i++) {
    palette.push(generateRandomColor());
  }

  return palette;
};

const ColorPickerModal = ({ visible, onClose, onSelectColor, initialColor }) => {
  const { getTextColor, getDark_Theme, backgroundColor } = useTheme();
  const AppText = useAppText();

  const [colorPalette, setColorPalette] = useState([]);
  const [selectedColor, setSelectedColor] = useState(initialColor || '');

  // Generate a new color palette when the modal becomes visible
  useEffect(() => {
    if (visible) {
      // Always generate new colors when modal opens
      setColorPalette(generateColorPalette());
    }
  }, [visible]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
  };

  const handleConfirm = () => {
    onSelectColor(selectedColor);
    onClose();
  };

  const handleGenerateNewColors = () => {
    setColorPalette(generateColorPalette());
  };

  // Function to determine if a color is light or dark
  const isLightColor = (hexColor) => {
    const hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5;
  };

  const renderColorItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.colorItem,
        { backgroundColor: item },
        selectedColor === item && styles.selectedColorItem,
      ]}
      onPress={() => handleColorSelect(item)}
    >
      {selectedColor === item && (
        <Icon
          source={globalpath.tick}
          size={wp(5)}
          // tintColor={isLightColor(item) ? colors.black : colors.white}
        />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon
                source={globalpath.cross}
                size={wp(5)}
                tintColor={getTextColor()}
              />
            </TouchableOpacity>
            <ResponsiveText
              color={getTextColor()}
              size={4.5}
              weight="bold"
            >
              {AppText.CHOOSE_COLOR || 'Choose Color'}
            </ResponsiveText>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.colorPreviewContainer}>
            <View
              style={[
                styles.colorPreview,
                {
                  backgroundColor: selectedColor || 'transparent',
                  borderStyle: selectedColor ? 'solid' : 'dashed'
                },
              ]}
            />
            <ResponsiveText
              color={getTextColor()}
              size={3.5}
            >
              {selectedColor || AppText.NO_COLOR_SELECTED || 'No color selected'}
            </ResponsiveText>
          </View>
{/*
          <TouchableOpacity
            style={[styles.generateButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={handleGenerateNewColors}
          >
            <ResponsiveText color={colors.white} size={3.5}>
              {AppText.GENERATE_NEW_COLORS || 'Generate New Colors'}
            </ResponsiveText>
          </TouchableOpacity> */}

          <FlatList
            data={colorPalette}
            renderItem={renderColorItem}
            keyExtractor={(_, index) => `color-${index}`}
            numColumns={4}
            contentContainerStyle={styles.colorGrid}
            showsVerticalScrollIndicator={false}
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: getDark_Theme() }]}
              onPress={onClose}
            >
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.CANCEL || 'Cancel'}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={handleConfirm}
            >
              <ResponsiveText color={colors.white} size={4}>
                {AppText.CONFIRM || 'Confirm'}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: wp(90),
    maxHeight: hp(80),
    borderRadius: wp(3),
    paddingHorizontal: wp(3),
    paddingVertical: hp(4),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  closeButton: {
    padding: wp(1),
  },
  placeholder: {
    width: wp(5),
  },
  colorPreviewContainer: {
    alignItems: 'center',
    marginBottom: hp(2),
  },
  colorPreview: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    marginBottom: hp(1),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  generateButton: {
    padding: hp(1.5),
    borderRadius: wp(2),
    alignItems: 'center',
    marginBottom: hp(2),
  },
  colorGrid: {
    paddingVertical: hp(1),
  },
  colorItem: {
    width: wp(18),
    height: wp(18),
    margin: wp(1.5),
    borderRadius: wp(10),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.grey,
  },
  selectedColorItem: {
    borderWidth: 2,
    borderColor: colors.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    flex: 1,
    padding: hp(1.5),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  confirmButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});

export default ColorPickerModal;
