import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, TextInput, Alert, Switch } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const DurationSelector = ({ value, onChange, error, isViewMode = false }) => {
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [customDuration, setCustomDuration] = useState('');
  const [isHours, setIsHours] = useState(false);
  const [customDurations, setCustomDurations] = useState([]);

  const defaultDurations = [
    { label: '15 min', value: '15' },
    { label: '30 min', value: '30' },
    { label: '45 min', value: '45' },
    { label: '1 hour', value: '60' },
  ];

  const allDurations = [...defaultDurations, ...customDurations];

  const handleAddDuration = () => {
    if (!customDuration || isNaN(customDuration) || parseFloat(customDuration) <= 0) {
      Alert.alert('Invalid Duration', 'Please enter a valid duration');
      return;
    }

    let durationValue = customDuration;
    let durationLabel = '';

    if (isHours) {
      // Convert hours to minutes for storage
      durationValue = (parseFloat(customDuration) * 60).toString();
      durationLabel = parseFloat(customDuration) === 1 
        ? `1 ${AppText.DURATION_HOURS}` 
        : `${customDuration} ${AppText.DURATION_HOURS}`;
    } else {
      durationLabel = `${customDuration} ${AppText.DURATION_MINUTES}`;
    }

    const newDuration = {
      label: durationLabel,
      value: durationValue
    };

    // Check if duration already exists
    const durationExists = allDurations.some(d => d.value === durationValue);
    if (durationExists) {
      Alert.alert('Duration Exists', 'This duration already exists');
      return;
    }

    setCustomDurations([...customDurations, newDuration]);
    setCustomDuration('');
    setShowAddModal(false);
  };

  // Format the display value
  const formatDisplayValue = (val) => {
    if (!val) return `30 ${AppText.DURATION_MINUTES}`;
    
    const numVal = parseInt(val);
    if (numVal >= 60) {
      const hours = Math.floor(numVal / 60);
      const minutes = numVal % 60;
      
      if (minutes === 0) {
        return hours === 1 ? `1 ${AppText.DURATION_HOURS}` : `${hours} ${AppText.DURATION_HOURS}`;
      } else {
        return `${hours}${AppText.DURATION_HOURS} ${minutes}${AppText.DURATION_MINUTES}`;
      }
    } else {
      return `${val} ${AppText.DURATION_MINUTES}`;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ResponsiveText
          color={getTextColor()}
          size={4}
          weight="500"
        >
          {AppText.DURATION}
        </ResponsiveText>
        <TouchableOpacity 
          style={[
            styles.durationButton,
            { 
              backgroundColor: colors.lightGrey,
              opacity: isViewMode ? 0.7 : 1,
            }
          ]}
          onPress={() => !isViewMode && setShowDropdown(!showDropdown)}
          disabled={isViewMode}
        >
          <Icon source={globalpath.watch} size={wp(5)} tintColor={colors.greyBlack} />
          <ResponsiveText color={colors.greyBlack} size={3.5} margin={[0, 0, 0, wp(2)]} weight={'bold'}>
            {formatDisplayValue(value)}
          </ResponsiveText>
          {!isViewMode && (
            <Icon
              source={showDropdown ? globalpath.up : globalpath.down}
              size={wp(3)}
              tintColor={colors.greyBlack}
              margin={[0, 0, 0, wp(2)]}
            />
          )}
        </TouchableOpacity>
      </View>

      {showDropdown && !isViewMode && (
        <View style={[styles.dropdownContainer, { backgroundColor: backgroundColor }]}>
          <TouchableOpacity 
            style={styles.addDurationButton}
            onPress={() => setShowAddModal(true)}
          >
            <ResponsiveText color={colors.white} weight="bold">{AppText.ADD_DURATION}</ResponsiveText>
          </TouchableOpacity>
          
          {allDurations.map((duration) => (
            <TouchableOpacity
              key={duration.value}
              style={[
                styles.durationItem,
                { borderBottomColor: getDark_Theme() }
              ]}
              onPress={() => {
                onChange(duration.value);
                setShowDropdown(false);
              }}
            >
              <ResponsiveText color={getTextColor()} weight="600">{duration.label}</ResponsiveText>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Add Duration Modal */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: backgroundColor }]}>
            <ResponsiveText color={getTextColor()} size={5} weight="bold" style={styles.modalTitle}>
              {AppText.ADD_DURATION}
            </ResponsiveText>
            
            <View style={styles.unitToggleContainer}>
              <ResponsiveText color={getTextColor()} size={3.5} weight="500">
                {isHours ? AppText.DURATION_HOURS : AppText.DURATION_MINUTES}
              </ResponsiveText>
              <Switch
                value={isHours}
                onValueChange={setIsHours}
                trackColor={{ false: colors.grey, true: colors.Light_theme_maincolour }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <ResponsiveText color={getTextColor()} size={3.5} weight="500">
                {AppText.DURATION} ({isHours ? AppText.DURATION_HOURS : AppText.DURATION_MINUTES})
              </ResponsiveText>
              <TextInput
                style={[styles.input, { color: getTextColor(), borderColor: colors.grey }]}
                value={customDuration}
                onChangeText={setCustomDuration}
                keyboardType="numeric"
                placeholder={isHours ? AppText.ENTER_DURATION_HOURS : AppText.ENTER_DURATION_MINUTES}
                placeholderTextColor={colors.grey}
              />
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, {backgroundColor: getDark_Theme()}]}
                onPress={() => setShowAddModal(false)}
              >
                <ResponsiveText color={colors.white} weight="bold">{AppText.CANCEL}</ResponsiveText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleAddDuration}
              >
                <ResponsiveText color={colors.white} weight="bold">{AppText.SAVE}</ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default DurationSelector;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  durationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(2),
  },
  dropdownContainer: {
    marginTop: hp(1),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  durationItem: {
    padding: wp(4),
    borderBottomWidth: 1,
    marginHorizontal: wp(4),
  },
  addDurationButton: {
    padding: wp(3),
    alignSelf: "flex-end",
    backgroundColor: colors.Light_theme_maincolour,
    borderRadius: wp(1.5),
    margin: wp(2)
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    borderRadius: wp(3),
    padding: wp(5),
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: hp(3),
  },
  unitToggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  inputContainer: {
    marginBottom: hp(3),
  },
  input: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginTop: hp(1),
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    padding: wp(3),
    borderRadius: wp(2),
    width: '45%',
    alignItems: 'center',
  },
  cancelButton: {
    // backgroundColor: colors.grey,
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
}); 