import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp } from '../../Custom/Responsiveness';

/**
 * A reusable loading indicator component
 * @param {Object} props - Component props
 * @param {string} [props.color] - Color of the loading indicator
 * @param {string} [props.size] - Size of the loading indicator ('small' or 'large')
 * @param {Object} [props.style] - Additional styles for the container
 */
const LoadingIndicator = ({ 
  color = colors.Light_theme_maincolour, 
  size = 'large',
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(4),
  },
});

export default LoadingIndicator;
