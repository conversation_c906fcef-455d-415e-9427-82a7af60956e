import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  Image
} from 'react-native';
import { wp, hp } from '../Custom/Responsiveness';
import { colors } from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';
import useTheme from '../Redux/useTheme';

const { width, height } = Dimensions.get('window');

const CustomAlert = ({ 
  visible, 
  title, 
  message, 
  onClose, 
  type = 'error', // 'error', 'success', 'warning', 'info'
  buttonText = 'OK'
}) => {
  const { backgroundColor, getTextColor } = useTheme();
  const [animation] = useState(new Animated.Value(0));
  
  useEffect(() => {
    if (visible) {
      Animated.spring(animation, {
        toValue: 1,
        friction: 7,
        tension: 40,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(animation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const getIconAndColor = () => {
    switch (type) {
      case 'success':
        return { 
          icon: globalpath.check, 
          color: colors.c_green,
          backgroundColor: 'rgba(46, 204, 113, 0.1)'
        };
      case 'warning':
        return { 
          icon: globalpath.warning, 
          color: '#F39C12',
          backgroundColor: 'rgba(243, 156, 18, 0.1)'
        };
      case 'info':
        return { 
          icon: globalpath.info, 
          color: '#3498DB',
          backgroundColor: 'rgba(52, 152, 219, 0.1)'
        };
      case 'error':
      default:
        return { 
          icon: globalpath.cross, 
          color: '#E74C3C',
          backgroundColor: 'rgba(231, 76, 60, 0.1)'
        };
    }
  };

  const { icon, color, backgroundColor: bgColor } = getIconAndColor();

  const animatedStyle = {
    transform: [
      {
        scale: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1],
        }),
      },
    ],
    opacity: animation,
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackground}>
        <Animated.View 
          style={[
            styles.alertContainer, 
            { backgroundColor: backgroundColor },
            animatedStyle
          ]}
        >
          <View style={[styles.iconContainer, { backgroundColor: bgColor }]}>
            <Icon
              source={icon}
              size={wp(10)}
              tintColor={color}
            />
          </View>
          
          <ResponsiveText 
            size={5} 
            weight="600" 
            color={getTextColor()}
            style={styles.title}
          >
            {title}
          </ResponsiveText>
          
          <ResponsiveText 
            size={3.8} 
            color={getTextColor()}
            style={styles.message}
          >
            {message}
          </ResponsiveText>
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: color }]}
            onPress={onClose}
          >
            <ResponsiveText 
              size={4} 
              weight="600" 
              color={colors.white}
            >
              {buttonText}
            </ResponsiveText>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContainer: {
    width: width * 0.85,
    borderRadius: wp(4),
    padding: wp(5),
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  title: {
    textAlign: 'center',
    marginBottom: hp(1),
  },
  message: {
    textAlign: 'center',
    marginBottom: hp(3),
  },
  button: {
    paddingVertical: hp(1.5),
    paddingHorizontal: wp(8),
    borderRadius: wp(6),
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CustomAlert;
