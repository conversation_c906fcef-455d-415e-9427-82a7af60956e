import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../Custom/Responsiveness';
import { colors } from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';

const ManagementCard = ({ title, onPress, getSecondaryTextColor, getTextColor, getBellBackground, getDark_Theme}) => {
  return (
    <TouchableOpacity 
      style={[styles.cardContainer, {
        borderColor: getDark_Theme(),
        borderWidth: 1.5
      }]}
      activeOpacity={0.7}
      onPress={onPress}
    >
      <View style={[styles.colorStrip, {backgroundColor: colors.Light_theme_maincolour}]} />
      <View style={styles.cardContent}>
        <ResponsiveText color={getTextColor()} size={5.5} weight={'600'} >
          {title}
        </ResponsiveText>
        <TouchableOpacity style={[styles.backicon,{backgroundColor:getBellBackground()}]}>
          <Icon source={globalpath.left} tintColor={getTextColor()} size={wp(4)} resizeMode="contain"/>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default ManagementCard;

const styles = StyleSheet.create({
  cardContainer: {
    marginHorizontal: wp(4),
    marginTop: hp(2),
    borderRadius: wp(3),
    overflow: 'hidden',
    flexDirection: 'row',
    borderTopLeftRadius: wp(3),
    borderBottomLeftRadius: wp(3),
    // backgroundColor:"#292929"
  },
  colorStrip: {
    width: wp(4),
    borderTopLeftRadius: wp(2.5),
    borderBottomLeftRadius: wp(2.5),
  },
  cardContent: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(2.5),
    marginHorizontal: wp(4),
    borderRadius: wp(2),
    alignItems: "center",
    justifyContent: "space-between",
    flexDirection: "row",
    flex: 1
  },
  backicon: {
    padding: wp(2),
    borderRadius: wp(8),
  }
}); 