import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';

const StatusDropdown = ({ onSelect }) => {
  const [value, setValue] = useState(null);
  const { getDark_Theme, backgroundColor, getTextColor } = useTheme();
  const AppText = useAppText();

  // Status options with display label and API value
  const statusOptions = [
    { label: 'Vacations', value: 'vacation' },
    { label: 'Lunch Breaks', value: 'lunch break' },
    { label: 'Meetings', value: 'meetings' },
    { label: 'Commercial Appointment', value: 'commercial appointment' },
    { label: 'Training', value: 'training' },
    { label: 'Recovery Time', value: 'recovery time' },
    { label: 'Illness', value: 'illness' },
    { label: 'Other Saloon', value: 'other saloon' },
  ];

  const handleChange = (item) => {
    setValue(item.value);
    onSelect && onSelect(item.value); // Pass the selected value to parent
  };

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.STATUS}
      </ResponsiveText>
      <Dropdown
        style={[styles.dropdown, { backgroundColor: backgroundColor ,borderColor: getDark_Theme()}]}
        placeholderStyle={[styles.placeholderStyle, { color: getTextColor() }]}
        selectedTextStyle={[styles.selectedTextStyle, { color: getTextColor() }]}
        itemTextStyle={[styles.itemTextStyle, { color: getTextColor() }]}
        containerStyle={{ backgroundColor: backgroundColor }}
        activeColor={colors.lightGrey}
        data={statusOptions}
        labelField="label"
        valueField="value"
        placeholder="Select Status"
        value={value}
        onChange={handleChange}
        dropdownPosition='top'
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
  },
  dropdown: {
    height: hp(6),
    // borderColor: colors.grey,
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(2),
  },
  placeholderStyle: {
    fontSize: wp(3.5),
  },
  selectedTextStyle: {
    fontSize: wp(3.5),
  },
  itemTextStyle: {
    fontSize: wp(3.5),
  },
});

export default StatusDropdown;