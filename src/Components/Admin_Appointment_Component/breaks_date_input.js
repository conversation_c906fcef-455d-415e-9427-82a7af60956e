import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import DatePickerModal from '../Barber_Section_Components/Barber_Home_Components/DatePickerModal';

const BreaksDateInput = ({ value, onChange }) => {
  const AppText = useAppText();
  const { getDark_Theme, getTextColor } = useTheme();
  const [showModal, setShowModal] = useState(false);

  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleSave = (date) => {
    onChange(date);
    setShowModal(false);
  };

  return (
    <View style={styles.container}>
      <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[0, 0, hp(1), 0]}>
        {AppText.BREAK_DATE}
      </ResponsiveText>

      <TouchableOpacity style={[styles.inputContainer, { borderColor: getDark_Theme() }]}
      onPress={() => setShowModal(true)}
      >
        <ResponsiveText color={value ? getTextColor() : colors.grey} size={3.8}>
          {value ? formatDate(value) : AppText.SELECT_DATE}
        </ResponsiveText>
        <View >
          <Icon
            source={globalpath.calendar}
            size={wp(5)}
            tintColor={colors.darkGrey}
          />
        </View>
      </TouchableOpacity>

      <DatePickerModal
        visible={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSave}
        initialDate={value}
        restrictToPresentAndFuture={true}
      />
    </View>
  );
};

export default BreaksDateInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    height: hp(6),
  },
});