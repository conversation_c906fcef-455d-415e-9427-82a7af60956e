import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import useAppText from '../../Custom/AppText';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useTheme from '../../Redux/useTheme';

const Admin_Appointment_Card = ({ item, onPress }) => {
  const AppText = useAppText();
  const {backgroundColor,getTextColor, getDark_Theme} = useTheme()

  return (
    <TouchableOpacity style={[styles.cardWrapper,{backgroundColor:backgroundColor,borderColor:getDark_Theme()}]} onPress={onPress} activeOpacity={0.9}>
      <View style={styles.sideAccent} />

      <View style={styles.cardContent}>
        <View style={styles.headerRow}>
          <Image
            source={
              item.shop_image
                ? { uri: item.shop_image }
                : globalpath.logo
            }
            style={styles.avatar}
          />
          <View style={{ flex: 1 }}>
            <ResponsiveText
              weight="600"
              size={4}
              color={getTextColor()}
              numberOfLines={2}
            >
              {item.name}
            </ResponsiveText>
            <ResponsiveText
              color={colors.grey}
              size={3.5}
              margin={[hp(0.5), 0, 0, 0]}
            >
              {item.address_city ? item.address_city : '-'}
            </ResponsiveText>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Icon
            source={globalpath.watch}
            size={wp(4.2)}
            tintColor={colors.Light_theme_maincolour}
          />
          <ResponsiveText
            color={colors.darkGrey}
            size={3.5}
            weight="500"
            margin={[0, 0, 0, wp(2)]}
          >
            {`${AppText.OPEN_TODAY}: ${
              Array.isArray(item?.working_hours) && item.working_hours.length > 0
                ? item.working_hours[0].open_time
                : ' -- : -- '
            }`}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default Admin_Appointment_Card;

const styles = StyleSheet.create({
  cardWrapper: {
    flexDirection: 'row',
    // backgroundColor: colors.white,
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    marginHorizontal: wp(0.1),
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    borderWidth: 1,
  },
  sideAccent: {
    width: wp(2),
    backgroundColor: colors.Light_theme_maincolour,
    borderTopLeftRadius: wp(1.5),
    borderBottomLeftRadius: wp(1.5),
  },
  cardContent: {
    flex: 1,
    padding: wp(4),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1.5),
    gap: wp(3),
  },
  avatar: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    marginRight: wp(3),
    backgroundColor: colors.grey,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
