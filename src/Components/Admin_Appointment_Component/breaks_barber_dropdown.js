import React, { useState, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { get_shops_barbers } from '../../Services/API/Endpoints/barber/dashboard';

const BreaksBarberDropdown = ({ onSelect, shopId, salonData, initialBarber }) => {
  console.log("salonData in barber dropdown", salonData?.id)
  console.log("initialBarber in barber dropdown", initialBarber)
  const [value, setValue] = useState(initialBarber?.id || null);
  const [barbers, setBarbers] = useState([]);
  const { getDark_Theme, backgroundColor, getTextColor } = useTheme();
  const AppText = useAppText();

  useEffect(() => {
    if (salonData?.id) {
      fetchBarbers();
    }
  }, [salonData?.id]);

  // Update value when initialBarber changes
  useEffect(() => {
    console.log("initialBarber changed in dropdown:", initialBarber);
    if (initialBarber?.id) {
      console.log("Setting dropdown value to:", initialBarber.id);
      setValue(initialBarber.id);

      // If barbers are already loaded, also call onSelect with the barber data
      if (barbers.length > 0) {
        const selectedBarberData = barbers.find(b => b.value === initialBarber.id)?.barberData;
        if (selectedBarberData) {
          console.log("Auto-selecting barber:", selectedBarberData);
          onSelect && onSelect(selectedBarberData);
        }
      }
    }
  }, [initialBarber, barbers]);

  const fetchBarbers = async () => {
    try {
      console.log("Fetching barbers for shop ID:", salonData?.id);
      const response = await get_shops_barbers(salonData?.id);
      console.log("Barbers fetched:", response);

      const uniqueBarbers = [];
      const barberMap = new Map();

      response.forEach(barber => {
        if (!barberMap.has(barber.id)) {
          barberMap.set(barber.id, true);
          uniqueBarbers.push({
            label: barber.full_name,
            value: barber.id,
            barberData: barber // Store full barber data for reference
          });
        }
      });

      console.log("Processed barbers for dropdown:", uniqueBarbers);
      setBarbers(uniqueBarbers);

      // If initialBarber is set, select it in the dropdown
      if (initialBarber?.id) {
        console.log("Setting initial barber after fetch:", initialBarber.id);
        setValue(initialBarber.id);

        // Find the matching barber in the fetched data
        const matchingBarber = response.find(b => b.id === initialBarber.id);
        if (matchingBarber) {
          console.log("Found matching barber in fetched data:", matchingBarber);
          onSelect && onSelect(matchingBarber);
        }
      }
    } catch (error) {
      console.error('Failed to fetch barbers:', error);
    }
  };

  const handleChange = (item) => {
    setValue(item.value);
    // Find the full barber data and pass it to onSelect
    const selectedBarber = barbers.find(b => b.value === item.value);
    onSelect && onSelect(selectedBarber?.barberData);
  };

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.CHOOSE_BARBER}
      </ResponsiveText>
      <Dropdown
        style={[styles.dropdown, { backgroundColor: backgroundColor, borderColor: getDark_Theme() }]}
        placeholderStyle={[styles.placeholderStyle, { color: getTextColor() }]}
        selectedTextStyle={[styles.selectedTextStyle, { color: getTextColor() }]}
        itemTextStyle={[styles.itemTextStyle, { color: getTextColor() }]}
        containerStyle={{ backgroundColor: backgroundColor }}
        activeColor={colors.lightGrey}
        data={barbers}
        labelField="label"
        valueField="value"
        placeholder={AppText.CHOOSE_BARBER}
        value={value}
        onChange={handleChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
  },
  dropdown: {
    height: hp(6),
    // borderColor: colors.grey,
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(2),
  },
  placeholderStyle: {
    fontSize: wp(3.5),
  },
  selectedTextStyle: {
    fontSize: wp(3.5),
  },
  itemTextStyle: {
    fontSize: wp(3.5),
  },
});

export default BreaksBarberDropdown;