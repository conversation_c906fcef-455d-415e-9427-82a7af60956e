import React from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import {colors} from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import useAppText from '../../Custom/AppText';

const SalonDetailsCard = ({onBackPress, name, rating, duration}) => {
  const AppText = useAppText();

  return (
    <ImageBackground
      source={globalpath.back1}
      style={styles.container}
      resizeMode="cover">
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <TouchableOpacity
            onPress={onBackPress}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            style={styles.backButtonContainer}>
            <Icon
              source={globalpath.goback}
            //   style={styles.BackIcon}
              tintColor={colors.white}
              resizeMode={'contain'}
            />
          </TouchableOpacity>
          <ResponsiveText size={7} color={colors.white} margin={[0,0,0,wp(-5)]} weight={'700'}>
            {AppText.DETAILS}
          </ResponsiveText>
        </View>

        <View style={styles.ratingRow}>
          <Icon
            source={globalpath.stars}
            size={15}
            resizeMode={'contain'}
            margin={[0, 0, wp(0.5), 0]}
          />
          <ResponsiveText size={4} weight={'bold'} color={colors.white}>
            {rating}
          </ResponsiveText>
        </View>
        <View style={styles.nameRow}>
        <ResponsiveText
                size={8}
                weight={'bold'}
                color={colors.white}
                // textAlign={'center'}
                >
                {name}
                </ResponsiveText>
                <ResponsiveText
                size={3}
                weight={'bold'}
                color={colors.white}
                // textAlign={'center'}
                >
                {duration}
                </ResponsiveText>
        </View>

      </View>
    </ImageBackground>
  );
};

export default SalonDetailsCard;

const styles = StyleSheet.create({
  container: {
    height: hp(45),
    width: '100%',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: wp(5),
    paddingTop: hp(5),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonContainer: {
    padding: wp(3),
    marginRight: wp(8),
    zIndex: 1,
    backgroundColor:colors.Light_theme_maincolour,
    borderRadius:wp(20)
  },
  BackIcon: {
    height: hp(4),
    width: wp(4),
  },
  ratingRow: {
    flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'center',
    marginTop: hp(4),
    // backgroundColor:"pink",
    
  },
  nameRow: {
// alignItems:"flex-start",
// justifyContent:"flex-start",
    // marginTop: hp(4),
  },
}); 