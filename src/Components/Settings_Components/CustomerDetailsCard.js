import React from 'react';
import { StyleSheet, View } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const CustomerDetailsCard = ({ order }) => {
  const { getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="600"
        margin={[0, 0, hp(2), 0]}
      >
        {AppText.CUSTOMER}
      </ResponsiveText>
      
      <View style={styles.row}>
        <Icon source={globalpath.order_user} size={wp(8)} />
        <ResponsiveText color={getTextColor()} size={3.8} margin={[0, 0, 0, wp(2)]}>
          {order.user}
        </ResponsiveText>
      </View>

      <View style={styles.row}>
        <Icon source={globalpath.order_location} size={wp(8)}  />
        <ResponsiveText color={getTextColor()} size={3.8} margin={[0, 0, 0, wp(2)]}>
          {order.location}
        </ResponsiveText>
      </View>

      <View style={styles.row}>
        <Icon source={globalpath.order_call} size={wp(8)}  />
        <ResponsiveText color={getTextColor()} size={3.8} margin={[0, 0, 0, wp(2)]}>
          {order.call}
        </ResponsiveText>
      </View>
    </View>
  );
};

export default CustomerDetailsCard;

const styles = StyleSheet.create({
  container: {
    padding: wp(4),
    borderRadius: wp(2),
    marginBottom: hp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: 5,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
  },
}); 