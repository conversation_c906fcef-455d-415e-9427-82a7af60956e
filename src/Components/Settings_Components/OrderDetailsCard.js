import React from 'react';
import { StyleSheet, View, Image } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import { globalpath } from '../../Custom/globalpath';
const OrderDetailsCard = ({ order }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
console.log('order inside order details card',order)

  return (
    <View style={[styles.card, { borderColor: getDark_Theme() }]}>
      <View style={styles.content}>
        <Image 
          source={ order.image ? { uri: order.image } : globalpath.logo} 
          style={[styles.image, { borderColor: getDark_Theme() }]} 
          resizeMode='contain'
        />
        <View style={styles.details}>
          <View style={styles.titleRow}>
            <ResponsiveText color={getTextColor()} numberOfLines={1} weight={'600'} size={4}>
              {order.title}
            </ResponsiveText>
          </View>
          <View style={styles.amountRow}>
            <ResponsiveText color={getTextColor()} weight={'600'}>
              ${order.amount}  <ResponsiveText color={"#6B6E82"}>| {order.items} {AppText.ITEMS}</ResponsiveText>
            </ResponsiveText>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(2.5),
    marginBottom: hp(2),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
    borderWidth: 1,
    marginRight: wp(3),
  },
  details: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(1),
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default OrderDetailsCard; 