import React, { useState } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';

const OrderStatusTabs = ({ activeTab, onTabPress }) => {
  const { getTextColor, getDark_theme } = useTheme();
  const AppText = useAppText();

  const tabs = [
    { id: 'processing', label: AppText.PROCESSING },
    { id: 'shipped', label: AppText.SHIPPED },
    { id: 'delivered', label: AppText.DELIVERED },
    // { id: 'returned', label: AppText.RETURNED },
    { id: 'cancelled', label: AppText.CANCELLED },
  ];

  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.container}
      scrollEnabled={false}
    >
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          onPress={() => onTabPress(tab.id)}
          style={[
            styles.tab,
            {
              backgroundColor: activeTab === tab.id 
                ? colors.Light_theme_maincolour 
                : colors.lightGrey,
            },
          ]}
        >
          <ResponsiveText
            style={[
              styles.tabText
            ]}
            color={activeTab === tab.id ? colors.white : colors.black}
            weight={'500'}
     
          >
            {tab.label}
          </ResponsiveText>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(0.5),
  },
  tab: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(1.2),
    borderRadius: wp(1.2),
    marginRight: wp(2.3),
  },
  tabText: {
    // fontSize: wp(3.5),
    fontWeight: '500',
  },
});

export default OrderStatusTabs; 