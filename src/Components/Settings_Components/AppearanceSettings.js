import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Switch, Modal, Animated } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedTheme, setThemeColor, setBackgroundColor, setLanguage } from '../../Redux/Slices/themeSlice';
import useAppText from '../../Custom/AppText';

const AppearanceSettings = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const AppText = useAppText();
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getDark_Theme } = useTheme();
  const currentLanguage = useSelector((state) => state.theme.language);
  const [showLanguageModal, setShowLanguageModal] = useState(false);

  const toggleTheme = () => {
    const newTheme = selectedTheme === 'light' ? 'dark' : 'light';
    dispatch(setSelectedTheme(newTheme));
    dispatch(setThemeColor(newTheme === 'dark' ? colors.Light_theme_maincolour : colors.black));
    dispatch(setBackgroundColor(newTheme === 'dark' ? colors.black : colors.white));
  };

  const toggleLanguage = () => {
    const newLanguage = currentLanguage === 'en' ? 'it' : 'en';
    dispatch(setLanguage(newLanguage));
  };

  const SettingItem = ({ icon, title, subtitle, value, onPress, showToggle, isToggled }) => (
    <TouchableOpacity 
      style={[styles.settingItem, { borderBottomColor: getDark_Theme() }]} 
      onPress={onPress}
      disabled={showToggle}
    >
      <View style={styles.settingContent}>
        <View style={styles.iconContainer}>
          <Icon 
            source={icon} 
            size={wp(6)} 
            tintColor={getTextColor()}
          />
        </View>
        <View style={styles.textContainer}>
          <ResponsiveText 
            color={getTextColor()} 
            size={4}
          >
            {title}
          </ResponsiveText>
          {subtitle && (
            <ResponsiveText 
              color={colors.grey} 
              size={3}
              margin={[hp(0.5), 0, 0, 0]}
            >
              {subtitle}
            </ResponsiveText>
          )}
        </View>
        {showToggle ? (
          <Switch
            trackColor={{ false: '#767577', true: colors.Light_theme_maincolour }}
            thumbColor={isToggled ? '#fff' : '#f4f3f4'}
            ios_backgroundColor="#3e3e3e"
            onValueChange={onPress}
            value={isToggled}
          />
        ) : (
          <View style={styles.valueContainer}>
            <ResponsiveText 
              color={getSecondaryTextColor()} 
              size={3.5}
            >
              {value}
            </ResponsiveText>
            <Icon 
              source={globalpath.rightArrow} 
              size={wp(4)} 
              style={{tintColor: getSecondaryTextColor(), marginLeft: wp(2)}}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
        <View style={[styles.contentContainer, { backgroundColor: backgroundColor }]}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon 
                source={globalpath.cross} 
                size={wp(6)} 
               tintColor={getTextColor()}
              />
            </TouchableOpacity>
            <ResponsiveText 
              color={getTextColor()} 
              size={5}
              weight={'bold'}
            >
              {AppText.APPEARANCE}
            </ResponsiveText>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.settingsContainer}>
            <SettingItem
              icon={globalpath.theme}
              title={AppText.DARK_MODE}
              subtitle={selectedTheme === 'dark' ? 'Dark theme enabled' : 'Light theme enabled'}
              showToggle={true}
              isToggled={selectedTheme === 'dark'}
              onPress={toggleTheme}
            />
            <SettingItem
              icon={globalpath.language}
              title={AppText.LANGUAGE}
              subtitle={currentLanguage === 'en' ? 'English language enabled' : 'Italian language enabled'}
              showToggle={true}
              isToggled={currentLanguage === 'it'}
              onPress={toggleLanguage}
            />
          </View>

          <Modal
            visible={showLanguageModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowLanguageModal(false)}
          >
            <View style={[styles.languageModalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
              <View style={[styles.languageContent, { backgroundColor: backgroundColor }]}>
                <TouchableOpacity 
                  style={[styles.languageOption, { borderBottomColor: getDark_Theme() }]}
                  onPress={() => changeLanguage('en')}
                >
                  <Icon source={globalpath.english} size={wp(6)} style={{tintColor: getTextColor()}} />
                  <ResponsiveText 
                    color={getTextColor()} 
                    size={4}
                    margin={[0, 0, 0, wp(3)]}
                  >
                    {AppText.ENGLISH}
                  </ResponsiveText>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.languageOption}
                  onPress={() => changeLanguage('it')}
                >
                  <Icon source={globalpath.italian} size={wp(6)} style={{tintColor: getTextColor()}} />
                  <ResponsiveText 
                    color={getTextColor()} 
                    size={4}
                    margin={[0, 0, 0, wp(3)]}
                  >
                    {AppText.ITALIAN}
                  </ResponsiveText>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  contentContainer: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    paddingTop: hp(2),
    paddingBottom: hp(4),
    minHeight: hp(50),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    marginBottom: hp(3),
  },
  closeButton: {
    padding: wp(2),
  },
  placeholder: {
    width: wp(10),
  },
  settingsContainer: {
    paddingHorizontal: wp(4),
  },
  settingItem: {
    borderBottomWidth: 1,
    paddingVertical: hp(2),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.Light_theme_maincolour + '20',
  },
  textContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  languageContent: {
    width: wp(80),
    borderRadius: wp(4),
    padding: wp(4),
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(2),
    borderBottomWidth: 1,
  },
});

export default AppearanceSettings; 