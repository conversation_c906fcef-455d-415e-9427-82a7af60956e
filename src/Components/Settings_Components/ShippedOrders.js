import React from 'react';
import { StyleSheet, View, FlatList } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import OrderCard from './OrderCard';

const ShippedOrders = ({ orders }) => {
  const { getTextColor } = useTheme();
  const AppText = useAppText();

  if (!orders || orders.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <ResponsiveText color={"#6B6E82"} size={4}>
          {AppText.NO_ORDERS_FOUND}
        </ResponsiveText>
      </View>
    );
  }

  const renderItem = ({ item }) => (
    <OrderCard order={item} />
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={orders}
        renderItem={renderItem}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingVertical: hp(2),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(30),
  },
});

export default ShippedOrders; 