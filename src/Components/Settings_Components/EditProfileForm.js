import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, KeyboardAvoidingView, SafeAreaView } from 'react-native';
import CustomTextInput from '../CustomTextInput';
import ResponsiveText from '../../Custom/RnText';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import useAppText from '../../Custom/AppText';
import AsyncStorage from '@react-native-async-storage/async-storage';
import EditProfileImage from './EditProfileImage';
import PersonalInfoHeader from '../CustomerComponents/Personal_Area_Components/PersonalInfoHeader';
import CustomeProfileInfoHeader from '../CustomerComponents/Personal_Area_Components/CustomeProfileInfoHeader';
import { ScrollView } from 'react-native-gesture-handler';

const EditProfileForm = () => {
  const [admin, setAdmin] = useState(null);
  const [formData, setFormData] = useState({
    fullName: 'John Smith',
    email: '<EMAIL>',
    phoneNumber: '****** 567 8900',
    bio: 'Professional barber with 10 years of experience',
  });

  const { getsky_Theme ,backgroundColor} = useTheme(); // ✅ Moved here
  const AppText = useAppText();        // ✅ Moved here

  useEffect(() => {
    const fetchAdminDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('adminData');
        if (data) {
          const parsed = JSON.parse(data);
          const adminInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setAdmin(adminInfo);
          setFormData({
            fullName: adminInfo.full_name || '',
            email: adminInfo.email || '',
            phoneNumber: adminInfo.phone_number||'',
            bio: adminInfo.description|| '',
          });
          console.log('✅ Admin object retrieved in the EditProfileform card:', adminInfo);
        }
      } catch (error) {
        console.log('❌ Error fetching admin details:', error);
      }
    };
  
    fetchAdminDetails();
  }, []);
  

  // ✅ Safe conditional rendering
  if (!admin) return null;

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    console.log('Saving profile:', formData);
  };

  return (
    <SafeAreaView style={{flex:1,backgroundColor:backgroundColor}}>
      <ScrollView showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
      <CustomeProfileInfoHeader />
      <EditProfileImage/>
      <View style={{paddingHorizontal:wp(5)}}>
      <CustomTextInput
        label={AppText.FULL_NAME}
        placeholder={AppText.FULL_NAME}
        value={formData.fullName}
        onChangeText={(text) => handleChange('fullName', text)}
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.EMAIL}
        placeholder={AppText.EMAIL}
        value={formData.email}
        onChangeText={(text) => handleChange('email', text)}
        keyboardType="email-address"
        inputStyle={{ backgroundColor: getsky_Theme() }}
        autoCapitalize="none"
      />
      <CustomTextInput
        label={AppText.PHONE_NUMBER}
        placeholder={AppText.PHONE_NUMBER}
        value={formData.phoneNumber}
        onChangeText={(text) => handleChange('phoneNumber', text)}
        keyboardType="phone-pad"
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.BIO}
        placeholder={AppText.BIO}
        value={formData.bio}
        onChangeText={(text) => handleChange('bio', text)}
        multiline={true}
        numberOfLines={4}
        inputStyle={{ 
          backgroundColor: getsky_Theme(),
          height: hp(15),
        }}
      />
      <TouchableOpacity 
        style={[styles.saveButton, { backgroundColor: colors.Light_theme_maincolour }]}
        onPress={handleSave}
      >
        <ResponsiveText 
          color={colors.white} 
          size={4.5}
          weight={'bold'}
        >
          {AppText.SAVE}
        </ResponsiveText>
      </TouchableOpacity>
      </View>
    </View>
      </ScrollView>
    </SafeAreaView>
  );
};


const styles = StyleSheet.create({
  container: {
    // paddingHorizontal: wp(4),
    flex:1
  },
  saveButton: {
    height: hp(6),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: hp(4),
    marginBottom: hp(2),
  },
});

export default EditProfileForm; 