import React from 'react';
import { StyleSheet, View, Image, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';

const HistoryOrderCard = ({ order }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  const getStatusColor = (status) => {
    return status === 'completed' ? "green" : colors.red;
  };

  const getStatusText = (status) => {
    return status === 'completed' ? AppText.COMPLETED : AppText.CANCELLED;
  };

  return (
    <View style={[styles.card, { borderColor: getDark_Theme() }]}>
      <View style={styles.content}>
        <Image 
          source={order.image} 
          style={[styles.image, { borderColor: getDark_Theme() }]} 
        />
        <View style={styles.details}>
          <View style={styles.titleRow}>
            <ResponsiveText color={getTextColor()} numberOfLines={1} maxWidth={wp(30)} weight={'600'} size={4}>
              {order.title}
            </ResponsiveText>
            <ResponsiveText color={getStatusColor(order.status)} weight={'600'}>
              {getStatusText(order.status)}
            </ResponsiveText>
          </View>
          <View style={styles.amountRow}>
            <ResponsiveText color={getTextColor()} weight={'600'}>
              ${order.amount}  <ResponsiveText color={"#6B6E82"}>| {order.date} | {order.items} {AppText.ITEMS}</ResponsiveText>
            </ResponsiveText>
          </View>
          <View style={styles.orderNumberRow}>
            <ResponsiveText color={getTextColor()} weight={'600'}>
              {AppText.ORDER_NUMBER}
            </ResponsiveText>
          
            <ResponsiveText color={"#6B6E82"}>
              #{order.orderNumber}
            </ResponsiveText>
          </View>
        </View>
      </View>
  
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
    borderWidth: 1,
    marginRight: wp(3),
  },
  details: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(1),
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(1),
  },
  orderNumberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  orderButton: {
    alignSelf: 'flex-end',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1),
    borderRadius: wp(1.5),
    marginTop: hp(1),
  },
});

export default HistoryOrderCard; 