import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const OrderStatusDropdown = ({ value, onChange, error }) => {
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [showDropdown, setShowDropdown] = useState(false);

  const statusOptions = [
    // { label: 'PENDING', value: 'PENDING' },
    { label: 'PROCESSING', value: 'PROCESSING' },
    { label: 'SHIPPED', value: 'SHIPPED' },
    { label: 'DELIVERED', value: 'DELIVERED' },
    { label: 'CANCELLED', value: 'CANCELLED' },
  ];

  const selectedStatus = statusOptions.find(option => option.value === value) || statusOptions[0];

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.ORDER_STATUS}
      </ResponsiveText>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            borderColor: error ? colors.red : getDark_Theme(),
            backgroundColor: backgroundColor,
          },
        ]}
        onPress={() => setShowDropdown(!showDropdown)}
      >
        <ResponsiveText
          color={value ? getTextColor() : colors.grey}
          size={3.8}
        >
          {selectedStatus.label}
        </ResponsiveText>
        <Icon
          source={showDropdown ? globalpath.up : globalpath.down}
          size={wp(3.5)}
          tintColor={getTextColor()}
        />
      </TouchableOpacity>

      {showDropdown && (
        <View style={[styles.dropdownContainer, { backgroundColor: colors.lightGrey, borderColor: getDark_Theme() }]}>
          {statusOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.dropdownItem,
                { 
                  borderBottomColor: getDark_Theme(),
                  backgroundColor: option.value === value ? colors.Light_theme_maincolour + '20' : 'transparent'
                }
              ]}
              onPress={() => {
                onChange(option.value);
                setShowDropdown(false);
              }}
            >
              <ResponsiveText 
                color={option.value === value ? colors.Light_theme_maincolour : colors.greyBlack}
                weight={option.value === value ? '600' : '400'}
              >
                {option.label}
              </ResponsiveText>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {error && (
        <ResponsiveText color={colors.red} size={3} margin={[hp(0.5), 0, 0, 0]}>
          {error}
        </ResponsiveText>
      )}
    </View>
  );
};

export default OrderStatusDropdown;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  dropdownButton: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownContainer: {
    position: 'absolute',
    top: hp(11),
    left: 0,
    right: 0,
    marginTop: hp(0.5),
    borderRadius: wp(2),
    borderWidth: 1,
    zIndex: 1000,
  },
  dropdownItem: {
    padding: wp(4),
    borderBottomWidth: 1,
  },
}); 