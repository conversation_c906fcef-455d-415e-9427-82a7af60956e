import React from 'react';
import { StyleSheet, View } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';

const ShippingDetailsCard = ({ order }) => {
  const { getTextColor, getDark_Theme, getsky_Theme } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="600"
        margin={[hp(2), 0, hp(2), 0]}
      >
        {AppText.SHIPPING_DETAILS}
      </ResponsiveText>
      
      <View style={[styles.detailsCard, { borderColor: getDark_Theme() , backgroundColor:getsky_Theme()}]}>
        <ResponsiveText color={getTextColor()} size={3.8}>
          {order.shipping_address}
        </ResponsiveText>
      </View>
    </View>
  );
};

export default ShippingDetailsCard;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  detailsCard: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(4),
  },
}); 