import React from 'react';
import { StyleSheet, View, FlatList } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { wp, hp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import OrderCard from './OrderCard';
import { mockOrders } from '../../Data/mockOrders';

const ReturnedOrders = () => {
  const { getTextColor } = useTheme();
  const AppText = useAppText();

  const returnedOrders = mockOrders.filter(order => order.status === 'returned');

  const renderItem = ({ item }) => (
    <OrderCard order={item} />
  );

  return (
    <View>
      <FlatList
        data={returnedOrders}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.container}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ReturnedOrders; 