import React, {useState} from 'react';
import {StyleSheet, View, TouchableOpacity, FlatList} from 'react-native';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import {colors} from '../../Custom/Colors';
import useAppText from '../../Custom/AppText';
import {globalpath} from '../../Custom/globalpath';
import Icon from '../../Custom/Icon';
import { useSelector } from 'react-redux';
import useTheme from '../../Redux/useTheme';
const SalonContentTabs = ({description, recentWorkImages}) => {
  const [selectedTab, setSelectedTab] = useState('ABOUT');
  const AppText = useAppText();
  const currentLanguage = useSelector((state) => state.theme.language);
  const {getTextColor} = useTheme();
  const renderContent = () => {
    switch (selectedTab) {
      case 'ABOUT':
        return (
          <View style={styles.contentContainer}>
            <ResponsiveText
              numberOfLines={2}
              color={getTextColor()}
              lineHeight={6}>
              {description}
            </ResponsiveText>
            <ResponsiveText
              size={5.5}
              color={getTextColor()}
              margin={[hp(1), 0, wp(1), 0]}>
              {AppText.TECHNIQUE_AND_STYLE}
            </ResponsiveText>
            <ResponsiveText
              numberOfLines={2}
              color={getTextColor()}
              lineHeight={6}>
              {AppText.DETAIL_DES}
            </ResponsiveText>
          </View>
        );
      case 'SERVICE':
        return (
          <View style={styles.contentContainer}>
            <ResponsiveText color={getTextColor()}>
              {AppText.SERVICES}
            </ResponsiveText>
          </View>
        );
      case 'REVIEW':
        return (
          <View style={styles.contentContainer}>
            <ResponsiveText color={getTextColor()}>
              {AppText.REVIEWS}
            </ResponsiveText>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        {[
          {key: 'ABOUT', label: AppText.ABOUT},
          {key: 'SERVICE', label: AppText.SERVICE},
          {key: 'REVIEW', label: AppText.REVIEW},
        ].map(tab => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => setSelectedTab(tab.key)}
            style={[
              [styles.tabButton,{paddingHorizontal:currentLanguage === "en" ? wp(8) : wp(5)}],
              {
                backgroundColor:
                  selectedTab === tab.key
                    ? colors.Light_theme_maincolour
                    : 'transparent',
              },
            ]}>
            <ResponsiveText
              size={3.5}
              color={selectedTab === tab.key ? colors.white : colors.grey}
              weight={'700'}>
              {tab.label}
            </ResponsiveText>
          </TouchableOpacity>
        ))}
      </View>

      {renderContent()}

      <View style={styles.recentWorkContainer}>
        <ResponsiveText size={6} color={getTextColor()}>
          {AppText.RECENT_WORK}
        </ResponsiveText>
        <FlatList
          data={recentWorkImages}
          horizontal
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item}) => (
            <View style={styles.imageContainer}>
              <Icon
                source={item}
                style={styles.image}
                resizeMode="contain"
              />
            </View>
          )}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default SalonContentTabs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabsContainer: {
    height: hp(6),
    backgroundColor: colors.lightGrey1,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    flexDirection: 'row',
    marginTop: hp(2),
    marginHorizontal: wp(4),
    borderRadius: wp(1.5),
  },
  tabButton: {
    paddingVertical: hp(1.2),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: wp(1.5),
  },
  contentContainer: {
    marginHorizontal: wp(5),
    marginVertical: hp(2),
  },
  recentWorkContainer: {
    marginHorizontal: wp(5),
  },
  imageContainer: {
    marginRight: wp(3),
  },
  image: {
    height: hp(15),
    width: wp(25),
    borderRadius: wp(2),
  },
}); 