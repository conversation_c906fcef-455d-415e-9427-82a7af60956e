import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import { useNavigation } from '@react-navigation/native';
import useAppText from '../../Custom/AppText';
const Admin_Personal_Header = () => {
  const navigation = useNavigation();
  const { getTextColor } = useTheme();
  const AppText = useAppText();     

  return (
    <View style={styles.headerContainer}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Icon source={globalpath.goback} size={wp(5)} tintColor={getTextColor()} />
      </TouchableOpacity>
      <ResponsiveText 
        color={getTextColor()} 
        size={5}
        weight={'bold'}
      >
        {AppText.PERSONAL_INFO }
      </ResponsiveText>
      <TouchableOpacity style={styles.editButton} onPress={() => navigation.navigate('EditProfileForm')}>
        <Icon source={globalpath.edit} size={wp(5)} tintColor={getTextColor()} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
  },
  backButton: {
    padding: wp(2),
  },
  editButton: {
    padding: wp(2),
  },
});

export default Admin_Personal_Header; 