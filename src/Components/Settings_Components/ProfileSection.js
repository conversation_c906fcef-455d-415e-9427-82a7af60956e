import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity ,Image} from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
const 
ProfileSection = () => {

  const [admin, setAdmin] = useState(null);

  useEffect(() => {
    const fetchAdminDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('userData');
        if (data) {
          const parsed = JSON.parse(data);
          const AdminInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setAdmin(AdminInfo);
          console.log('✅ Admin object retrieved in the ProfileSection :', AdminInfo);
        }
      } catch (error) {
        console.log('❌ Error fetching Admin details:', error);
      }
    };

    fetchAdminDetails();
  }, []);

  // if (!customer) return null;



  const navigation = useNavigation();
  const { getTextColor, getSecondaryTextColor, getDark_Theme, } = useTheme();
   const handlePress = () => {
    navigation.navigate('Personal_Info');
    console.log('profile section pressed');
   }

  return (
    <TouchableOpacity style={[styles.container, { borderColor: getDark_Theme() }]} onPress={handlePress}>
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Image source={globalpath.profile} style={{width:wp(15),height:wp(15)}} resizeMode={'contain'}/>
        </View>
        <View style={styles.middleSection}>
          <View style={styles.nameContainer}>
            <ResponsiveText color={getTextColor()} size={4.5} weight="bold">
              {admin?.username}
            </ResponsiveText>
            <TouchableOpacity>
              <Icon source={globalpath.profile_eddit} size={wp(5)}  />
            </TouchableOpacity>
          </View>
          <ResponsiveText color={colors.grey} size={3.5}>
            {admin?.email}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    // borderWidth: 1,
    // borderRadius: wp(3),
    marginHorizontal: wp(2),
    marginTop: hp(2),
    padding: wp(4),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    marginRight: wp(3),
    
  },
  middleSection: {
    flex: 1,
    // backgroundColor:'pink'
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
});

export default ProfileSection; 