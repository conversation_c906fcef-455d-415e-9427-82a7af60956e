import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';

const EditProfileImage = () => {
  console.log("EditprofileImage component is renderd")
  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image 
          source={globalpath.profile} 
          style={styles.profileImage}
          resizeMode="cover"
        />
        <TouchableOpacity style={styles.editIconContainer}>
          <Icon 
            source={globalpath.edit} 
            size={wp(5)} 
            // style={{tintColor: colors.white}}
            tintColor={colors.white}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: hp(3),
    marginBottom: hp(4),
  },
  imageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: wp(30),
    height: wp(30),
    borderRadius: wp(15),
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.Light_theme_maincolour,
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
});

export default EditProfileImage; 