import { View, Text, StyleSheet } from 'react-native';
import React from 'react';
import CustomCheckbox from '../../Custom/CustomCheckbox';
import { wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import Custom_Check_box from './Custom_Check_box';

const OrderCheckbox = ({ value, onValueChange, date, label, style }) => {
  const AppText = useAppText();
  const { getTextColor } = useTheme();    
  return (
    <View style={[styles.container, style]}>
      <View style={styles.checkboxContainer}>
      <Custom_Check_box
        value={value}
        onValueChange={onValueChange}
        onTintColor={colors.Light_theme_maincolour}
        onCheckColor={colors.white}
      />
        <ResponsiveText color={getTextColor()} weight={'600'} margin={[0,0,0,wp(2)]} size={4}>{label}</ResponsiveText>
        </View>
      <View style={styles.textContainer}>
        <ResponsiveText color={getTextColor()} size={3.5}>{date}</ResponsiveText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: wp(2),
    justifyContent: 'space-between',
    paddingHorizontal: wp(2),
  },
  textContainer: {
    marginLeft: wp(3),
  },
  statusText: {
    fontSize: wp(4),
    fontWeight: '500',
    color: colors.black,
  },
  dateText: {
    fontSize: wp(3.5),
    color: colors.gray,
    marginTop: wp(0.5),
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default OrderCheckbox; 