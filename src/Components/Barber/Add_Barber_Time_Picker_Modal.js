import React, {useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  Modal,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import {colors} from '../../Custom/Colors';

const Add_Barber_Time_Picker_Modal = ({
  visible,
  onClose,
  onSave,
  initialStartTime,
  initialEndTime,
}) => {
  const AppText = useAppText();
  const {backgroundColor, getDark_Theme, getTextColor} = useTheme();
  const [startTime, setStartTime] = useState(initialStartTime || new Date());
  const [endTime, setEndTime] = useState(initialEndTime || new Date());
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);
  const [error, setError] = useState('');

  // Reset times when modal becomes visible with new initial times
  useEffect(() => {
    if (visible) {
      setStartTime(initialStartTime || new Date());
      setEndTime(initialEndTime || new Date());
      setError('');
    }
  }, [visible, initialStartTime, initialEndTime]);

  const roundToNearestFifteen = date => {
    const minutes = date.getMinutes();
    const roundedMinutes = Math.round(minutes / 15) * 15;
    const newDate = new Date(date);
    newDate.setMinutes(roundedMinutes === 60 ? 0 : roundedMinutes);
    if (roundedMinutes === 60) {
      newDate.setHours(newDate.getHours() + 1);
    }
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);
    return newDate;
  };

  const formatTime = date => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const validateTimeSelection = (start, end) => {
    // Check if end time is after start time
    if (end <= start) {
      return AppText.ERROR2 || 'End time must be after start time';
    }

    // Check if the time difference is at least 15 minutes
    const diffMs = end - start;
    const diffMinutes = diffMs / (1000 * 60);

    if (diffMinutes < 15) {
      return 'Time slot must be at least 15 minutes';
    }

    return null;
  };

  const handleStartTimeConfirm = date => {
    const roundedDate = roundToNearestFifteen(date);
    setStartTime(roundedDate);

    // Validate against end time if it's already set
    if (endTime) {
      const validationError = validateTimeSelection(roundedDate, endTime);
      if (validationError) {
        setError(validationError);
      } else {
        setError('');
      }
    }

    setShowStartPicker(false);
  };

  const handleEndTimeConfirm = date => {
    const roundedDate = roundToNearestFifteen(date);

    // Validate against start time
    const validationError = validateTimeSelection(startTime, roundedDate);
    if (validationError) {
      setError(validationError);
      setShowEndPicker(false);
      return;
    }

    setEndTime(roundedDate);
    setShowEndPicker(false);
    setError('');
  };

  const handleSave = () => {
    // Final validation before saving
    const validationError = validateTimeSelection(startTime, endTime);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    onSave(startTime, endTime);
    onClose();
  };

  const minuteInterval = 15;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContent,
            {backgroundColor, borderColor: getDark_Theme()},
          ]}>
          <ResponsiveText
            color={getTextColor()}
            size={5}
            weight="bold"
            margin={[0, 0, hp(2), 0]}>
            {AppText.SELECT_TIME}
          </ResponsiveText>
          {error ? (
            <View style={styles.errorContainer}>
              <ResponsiveText
                color={colors.red}
                size={3.5}
                weight="500"
                style={styles.errorText}>
                {error}
              </ResponsiveText>
            </View>
          ) : null}

          <View style={styles.timeContainer}>
            <View style={styles.timeSection}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="500"
                margin={[0, 0, hp(1), 0]}>
                {AppText.START_TIME}
              </ResponsiveText>
              <TouchableOpacity
                style={[styles.timeButton, {borderColor: getDark_Theme()}]}
                onPress={() => setShowStartPicker(true)}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {formatTime(startTime)}
                </ResponsiveText>
              </TouchableOpacity>
            </View>

            <View style={styles.timeSection}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="500"
                margin={[0, 0, hp(1), 0]}>
                {AppText.END_TIME}
              </ResponsiveText>
              <TouchableOpacity
                style={[styles.timeButton, {borderColor: getDark_Theme()}]}
                onPress={() => setShowEndPicker(true)}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {formatTime(endTime)}
                </ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, {backgroundColor: getDark_Theme()}]}
              onPress={onClose}>
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.CANCEL}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}>
              <ResponsiveText color={colors.white} size={4}>
                {AppText.SAVE}
              </ResponsiveText>
            </TouchableOpacity>
          </View>

          <DateTimePickerModal
            isVisible={showStartPicker}
            mode="time"
            onConfirm={handleStartTimeConfirm}
            onCancel={() => setShowStartPicker(false)}
            date={startTime}
            is24Hour={false}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            locale="en_US"
            minuteInterval={minuteInterval}
            minimumDate={new Date(new Date().setHours(0, 0, 0, 0))}
            maximumDate={new Date(new Date().setHours(23, 45, 0, 0))}
          />

          <DateTimePickerModal
            isVisible={showEndPicker}
            mode="time"
            onConfirm={handleEndTimeConfirm}
            onCancel={() => setShowEndPicker(false)}
            date={endTime}
            is24Hour={false}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            locale="en_US"
            minuteInterval={minuteInterval}
            minimumDate={new Date(new Date(startTime).setMinutes(startTime.getMinutes() + 15))}
            maximumDate={new Date(new Date().setHours(23, 59, 0, 0))}
          />
        </View>
      </View>
    </Modal>
  );
};

export default Add_Barber_Time_Picker_Modal;

const styles = StyleSheet.create({
  errorContainer: {
    backgroundColor: '#FFE5E5',
    padding: wp(3),
    borderRadius: wp(2),
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: colors.red,
  },
  errorText: {
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    padding: wp(5),
    borderRadius: wp(3),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(3),
  },
  timeSection: {
    flex: 1,
    marginHorizontal: wp(2),
  },
  timeButton: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    flex: 1,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});
