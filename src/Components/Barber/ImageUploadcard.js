import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ImagePreviewModal from './ImagePreviewModal';

const ImageUploadcard = ({ onImageSelect }) => {
  const { getDark_Theme, getTextColor, backgroundColor, getDarK_mode_LightGrayBackground } = useTheme();
  const AppText = useAppText();
  const [selectedImage, setSelectedImage] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  const handleImagePicker = () => {
    const options = {
      mediaType: 'photo',
      includeBase64: true,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        return;
      }
      if (response.errorCode) {
        return;
      }
      if (response.assets && response.assets[0]) {
        const image = response.assets[0];
        const imageDetails = {
          ...image,
          size: (image.fileSize / (1024 * 1024)).toFixed(2), // Convert to MB
          dimensions: `${image.width}x${image.height}`,
        };
        setSelectedImage(imageDetails);
        onImageSelect && onImageSelect(imageDetails);
      }
    });
  };

  const handleDeleteImage = () => {
    setSelectedImage(null);
    onImageSelect && onImageSelect(null);
  };

  return (
    <View style={[styles.container, { borderColor: getDark_Theme(), backgroundColor: backgroundColor }]}>
      <ResponsiveText
        color={getTextColor()}
        size={4.6}
        weight="bold"
        margin={[hp(1), 0, hp(1), 0]}
      >
        {AppText.UPLOAD_BARBER_IMAGE}
      </ResponsiveText>

      {selectedImage ? (
        <View style={styles.imageContainer}>
          <TouchableOpacity 
            style={styles.imageWrapper}
            onPress={() => setShowPreview(true)}
          >
            <Image
              source={{ uri: selectedImage.uri }}
              style={styles.previewImage}
              resizeMode="cover"
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeleteImage}
          >
            <Icon source={globalpath.delete_icon} size={wp(5)} tintColor={colors.white} />
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity
          style={[styles.uploadContainer, { borderColor: colors.c_green, backgroundColor: getDarK_mode_LightGrayBackground() }]}
          onPress={handleImagePicker}
        >
          <Icon source={globalpath.upload} style={styles.uploadIcon} resizeMode="contain" />
        </TouchableOpacity>
      )}

      <ImagePreviewModal
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        imageUri={selectedImage?.uri}
      />
    </View>
  );
};

export default ImageUploadcard;

const styles = StyleSheet.create({
  container: {
    borderRadius: wp(2),
    padding: wp(4),
    borderWidth: 1,
    marginTop: hp(1),
    marginHorizontal: wp(4),
  },
  uploadContainer: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: wp(2),
    padding: wp(4),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: hp(15),
    marginTop: hp(1),
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: hp(20),
    marginTop: hp(1),
    
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  deleteButton: {
    position: 'absolute',
    top: wp(2),
    right: wp(2),
    backgroundColor: colors.red,
    borderRadius: wp(4),
    padding: wp(2),
  },
  uploadIcon: {
    width: wp(58),
    height: wp(11),
  },
}); 