import React, {useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  Image,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Modal from 'react-native-modal';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import {colors} from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import {barberMockData} from '../../Mocks/service_mock';
import BarberCard from '../Salons/BarberCard';
import {SafeAreaView} from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

const BarberDetails = ({ route ,navigation}) => {
  // const navigation = useNavigation();
  const selectedBarber = route.params;
  // console.log("selectedBarber is ",selectedBarber)
  console.log('selected barber details----', selectedBarber.barber);
  
  const {backgroundColor, getDark_Theme, getTextColor} = useTheme();
  const AppText = useAppText();
  const [selectedTab, setSelectedTab] = useState('ABOUT'); // Default Selected Tab
  const [chooseBarber, setChooseBarber] = useState(null);
  const recentWorkImages = [
    globalpath.childcut,
    globalpath.childcut1,
    globalpath.childcut2,
    globalpath.childcut3,
  ];
  const handleBackPress = () => {
    navigation.goBack();
    console.log('back pressed');
  };
  const currentLanguage = useSelector((state) => state.theme.language);

  const handleEditPress = () => {
    navigation.navigate('Barber_Details', { barber: selectedBarber.barber });
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: backgroundColor}]}>
          <View style={styles.DetailedCard}>
            <View style={styles.DetailLeft}>
              <View style={styles.ROWTEXT}>
                <TouchableOpacity 
                  onPress={handleBackPress}
                  hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                  style={styles.backButtonContainer}>
                  <Icon 
                    source={globalpath.goback} 
                    style={styles.BackIcon} 
                    tintColor={colors.white}
                  />
                </TouchableOpacity>
                <ResponsiveText size={7} color={colors.white}>
                  {AppText.DETAILS}  
                </ResponsiveText>
              </View>
{/* 
              <View style={styles.rowstyle}>
                <Icon source={globalpath.stars} size={15} resizeMode={'contain'} margin={[0,0,wp(0.5),0]}></Icon>
                <ResponsiveText size={4} weight={'bold'} color={colors.white}>
                  {AppText.RATING}  
                </ResponsiveText>
              </View> */}

          <View style={{ justifyContent:"center", alignItems:"center",marginLeft:wp(5),marginTop:hp(7)}}> 
          <ResponsiveText size={8} weight={'bold'} color={colors.white} textAlign={'center'}>
                          {selectedBarber.barber.full_name}
                        </ResponsiveText>
                        <ResponsiveText size={3} weight={'bold'} color={colors.white}textAlign={'center'} >
                          {selectedBarber.barber.email}
                        </ResponsiveText>
          </View>
        
            </View>
            <View style={styles.DetailRight}>

               <Image
                      source={
                        selectedBarber.barber.profile_image
                          ? { uri:  selectedBarber.barber.profile_image } // Remote image (from API)
                          : globalpath.logo // Local fallback image
                      }
                    
                      style={styles.image}
                    />

              {/* <Icon
                source={globalpath.detailbarber}
                style={styles.proSTyle}
                resizeMode={'contain'}>
                  
                </Icon> */}
            </View>
            <TouchableOpacity 
                onPress={handleEditPress}
                style={styles.editButtonContainer}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                <Icon 
                  source={globalpath.edit} 
                  style={styles.editIcon} 
                  tintColor={colors.white}
                />
              </TouchableOpacity>
          </View>

          <View
            style={{
              height: hp(6),
              backgroundColor: colors.lightGrey1,
              alignItems: 'center',
              justifyContent: 'space-evenly',
              flexDirection: 'row',
              marginTop: hp(2),
              marginHorizontal: wp(4),
              borderRadius: wp(1.5),
            }}>
            {[
              {key: 'ABOUT', label: AppText.ABOUT},
              {key: 'SERVICE', label: AppText.SERVICE},
              {key: 'REVIEW', label: AppText.REVIEW},
            ].map(tab => (
              <TouchableOpacity
                key={tab.key}
                onPress={() => setSelectedTab(tab.key)} // Set Selected Tab
                style={{
                  backgroundColor:
                    selectedTab === tab.key
                      ? colors.Light_theme_maincolour
                      : 'transparent',
                  paddingHorizontal: currentLanguage === 'en' ? wp(8) :wp(5),
                  paddingVertical: hp(1.2),
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: wp(1.5),
                }}>
                <ResponsiveText
                  size={3.5}
                  color={selectedTab === tab.key ? colors.white : colors.grey} weight={'700'}>
                  {tab.label}
                </ResponsiveText>
              </TouchableOpacity>
            ))}
          </View>
          <ScrollView style={{marginHorizontal: wp(5), marginVertical: hp(2)}} showsVerticalScrollIndicator={false} >
            <ResponsiveText numberOfLines={2} color={getTextColor()} lineHeight={6}> 
              {selectedBarber.longDescription}
            </ResponsiveText>
            <ResponsiveText size={5.5} color={getTextColor()} margin={[hp(1),0,wp(1),0]} >
              {AppText.TECHNIQUE_AND_STYLE}
            </ResponsiveText>
            <ResponsiveText  color={getTextColor()}  lineHeight={6}>
              {selectedBarber?.barber?.description}
            </ResponsiveText>
          </ScrollView>
          {/* <View style={{marginHorizontal: wp(5),marginBottom:hp(3)}}>
            <ResponsiveText size={6} color={getTextColor()}>{AppText.RECENT_WORK}</ResponsiveText>
            <FlatList
              data={recentWorkImages}
              horizontal={true} // Enables horizontal scrolling
              keyExtractor={(item, index) => index.toString()}
              renderItem={({item}) => (
                <View style={{marginRight: wp(3)}}>
                  <Icon
                    source={item}
                    style={{height: hp(15), width: wp(25), borderRadius: wp(2)}}
                    resizeMode="contain"
                  />
                </View>
              )}
              showsHorizontalScrollIndicator={false} // Hides the scrollbar
            />
            
          </View> */}
     
  
    </SafeAreaView>
  );
};

export default BarberDetails;

const styles = StyleSheet.create({
  container:{
    flex:1
  },
  DetailedCard: {
    backgroundColor: colors.Light_theme_maincolour,
    height:"45%",
    flexDirection: 'row',
    // backgroundColor:"red",
    // alignItems:"center",
  },
  DetailLeft: {
    alignItems: 'center',
    marginTop: hp(5),
    flex: 1,
  },
  DetailRight: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.Light_theme_maincolour,
  },
  ROWTEXT: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonContainer: {
    padding: wp(2),
    marginRight: wp(8),
    zIndex: 1,
  },
  BackIcon: {
    height: hp(4),
    width: wp(4),
  },
  rowstyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(6),
  },
  rating: {height: hp(2), width: wp(3), paddingHorizontal: wp(4)},
  proSTyle: {
    height: hp(43),
    position: 'absolute',
    width: wp(100),
    marginLeft: hp(-22),
  },
  editButtonContainer: {
    marginTop: hp(6),
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(2),
    borderRadius: wp(9),
    borderWidth: 1,
    borderColor: colors.white,
    width: wp(8),
    height: wp(8),
    alignItems: 'center',
    justifyContent: 'center',
    right: wp(5),
  },
  editIcon: {
    height: hp(4),
    width: wp(4),
    position: 'absolute',
    // right: wp(5),
  },
  image: {
    width: wp(30),
    height: wp(30),
    borderRadius: wp(20), // Ensures a perfect circle
    borderWidth: 2,
    borderColor: colors.white,
    resizeMode: 'cover',
    alignSelf: 'center',
    marginTop: hp(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5, // Adds shadow for Android
  },
});
