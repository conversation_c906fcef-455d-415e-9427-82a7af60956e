import {
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  View,
  TouchableOpacity,
  Alert
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ScrollView, TextInput} from 'react-native-gesture-handler';
import CustomHeader from '../CustomHeader';
import useAppText from '../../Custom/AppText';
import CustomTextInput from '../CustomTextInput';
import {hp, wp} from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import ResponsiveText from '../../Custom/RnText';
import ImageUploadcard from './ImageUploadcard';
import { colors } from '../../Custom/Colors';
import { Add_barber , barber_working_hours} from '../../Services/API/Endpoints/Admin/Barber';
import Loader from '../../Custom/loader';
import Add_Barber_Week_Card from './Add_Barber_Week_Card';
import Add_Barber_Time_Picker_Modal from './Add_Barber_Time_Picker_Modal';
import { globalpath } from '../../Custom/globalpath';
import Icon from '../../Custom/Icon';

const AddNewBarber = ({navigation}) => {
  const {getTextColor, getDark_Theme, backgroundColor} = useTheme();

  const AppText = useAppText();
  const [loading, setLoading] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false); // default: NOT secure


  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone_number: '',
    description:'',
    role:'barber',
    password:'',
    working_hours: [
      { day_of_week: 0, open_time: '00:00:00', close_time: '00:00:00' }, // Mon
      { day_of_week: 1, open_time: '00:00:00', close_time: '00:00:00' }, // Tue
      { day_of_week: 2, open_time: '00:00:00', close_time: '00:00:00' }, // Wed
      { day_of_week: 3, open_time: '00:00:00', close_time: '00:00:00' }, // Thu
      { day_of_week: 4, open_time: '00:00:00', close_time: '00:00:00' }, // Fri
      { day_of_week: 5, open_time: '00:00:00', close_time: '00:00:00' }, // Sat
      { day_of_week: 6, open_time: '00:00:00', close_time: '00:00:00' }, // Sun
    ]
  });

  const [weekDays, setWeekDays] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [showTimeModal, setShowTimeModal] = useState(false);

  useEffect(() => {
    generateWeekDays();
  }, []);

  const generateWeekDays = () => {
    const days = [
      AppText.MON,
      AppText.TUE,
      AppText.WED,
      AppText.THU,
      AppText.FRI,
      AppText.SAT,
      AppText.SUN
    ];

    const weekDaysData = days.map(day => ({
      day,
      isSelected: false,
      selectedTime: null,
    }));
    setWeekDays(weekDaysData);
  };

  const dayMap = {
    [AppText.MON]: 0,
    [AppText.TUE]: 1,
    [AppText.WED]: 2,
    [AppText.THU]: 3,
    [AppText.FRI]: 4,
    [AppText.SAT]: 5,
    [AppText.SUN]: 6,
  };



  const [selectedImage, setSelectedImage] = useState(null);

  const handleImageSelect = (imageDetails) => {
    setSelectedImage(imageDetails);
    if (imageDetails) {
      console.log('Selected Image Details:', {
        name: imageDetails.fileName,
        size: imageDetails.size + ' MB',
        dimensions: imageDetails.dimensions,
        uri: imageDetails.uri
      });
    } else {
      console.log('Image removed');
    }
  };

  const handleChange = (field, value) => {
    setFormData(prevState => ({
      ...prevState,
      [field]: value
    }));
    console.log("formData-->", formData)

  };


  const handleDayPress = (index) => {
    setSelectedDay(index);
    setShowTimeModal(true);
  };

  const formatTo24Hour = (date) => {
    if (!(date instanceof Date)) return '00:00:00';
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}:00`;
  };


  const handleTimeSelect = (startTime, endTime) => {
    // ✅ 1. For UI display
    const displayStart = startTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const displayEnd = endTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const displayString = `${displayStart} - ${displayEnd}`;

    // ✅ 2. For backend data (24-hour format)
    const formattedStart = formatTo24Hour(startTime);
    const formattedEnd = formatTo24Hour(endTime);

    // ✅ Update UI for selected day
    setWeekDays(prev => {
      const updated = [...prev];
      updated[selectedDay] = {
        ...updated[selectedDay],
        isSelected: true,
        selectedTime: displayString,
      };
      return updated;
    });

    // ✅ Update formData.working_hours
    const dayName = weekDays[selectedDay].day;
    const dayOfWeek = dayMap[dayName];

    setFormData(prev => ({
      ...prev,
      working_hours: prev.working_hours.map(hour =>
        hour.day_of_week === dayOfWeek
          ? {
              ...hour,
              open_time: formattedStart,
              close_time: formattedEnd,
            }
          : hour
      ),
    }));

    setShowTimeModal(false);

    // Note: For AddNewBarber, we don't make an API call here because the barber
    // hasn't been created yet. The working hours will be sent to the server when
    // the user submits the form.
  };


  const handleSubmit = async () => {
    console.log("Working hours ", formData.working_hours);

    // Check if all required fields are filled
    if (!formData.full_name || !formData.email || !formData.phone_number || !formData.password) {
      Alert.alert('Validation Error', 'Please fill in all required fields.');
      return;
    }

    // Validate working hours: Ensure all days have open and close times set (not "00:00:00")
    const invalidWorkingHours = formData.working_hours.some(
      (day) => day.open_time === '00:00:00' || day.close_time === '00:00:00'
    );

    if (invalidWorkingHours) {
      Alert.alert('Validation Error', 'Please set working hours for all days.');
      return;
    }

    // Ensure at least one day has working hours set
    const hasWorkingHoursSet = formData.working_hours.some(
      (day) => day.open_time !== '00:00:00' && day.close_time !== '00:00:00'
    );

    if (!hasWorkingHoursSet) {
      Alert.alert('Validation Error', 'Please set working hours for at least one day.');
      return;
    }

    // Validate that a barber image is selected
    if (!selectedImage || !selectedImage.uri) {
      Alert.alert('Validation Error', 'Please select a barber image.');
      return;
    }

    try {
      setLoading(true);

      const payload = new FormData();
      payload.append('full_name', formData.full_name);
      payload.append('email', formData.email);
      payload.append('phone_number', formData.phone_number);
      payload.append('description', formData.description);
      payload.append('role', formData.role);
      payload.append('password', formData.password);
      payload.append('working_hours', JSON.stringify(formData.working_hours));

      // Append the image if selected
      if (selectedImage?.uri) {
        payload.append('profile_image', {
          uri: selectedImage.uri,
          name: selectedImage.fileName || 'photo.jpg',
          type: 'image/jpeg',
        });
      }

      const response = await Add_barber(payload); // Call the API to add the barber
      console.log('🟢 Barber added:', response);

      // Call the barber_workingHours function with the shop ID and working hours
      await barber_workingHours(response.id, formData.working_hours);

      // Show success alert
      Alert.alert('Success', 'Barber added successfully!');
      navigation.goBack(); // Navigate back after success
    } catch (error) {
      setLoading(false);
      console.error('❌ Error adding barber:', error);

      const errorData = error?.response?.data;
      const firstKey = errorData && typeof errorData === 'object' ? Object.keys(errorData)[0] : null;
      const firstMessage = firstKey ? errorData[firstKey][0] : 'Something went wrong.';

      Alert.alert('Error', firstMessage);
    } finally {
      setLoading(false);
    }
  };



    const barber_workingHours = async (id ,working_hours) => {
      console.log("shop-->", id)
      try {
        const payload = {
          barber : id,
          working_hours:working_hours
        }

        console.log('🚀 Submitting working_hours payload:', working_hours);


        const response = await barber_working_hours(payload); // ✅ Call API
        console.log('✅ barber_working_hours response:', response);

      } catch (error) {
        console.error('❌ Error in executeService:', error);
      }
    };



  return (
    <SafeAreaView style={{flex: 1,backgroundColor:backgroundColor}}>
      <CustomHeader title={AppText.ADD_BARBER} leftIconType="back"  />

  <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
      keyboardVerticalOffset={Platform.OS === 'ios' ? hp(1) : 0}
    >
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{flexGrow: 1}} keyboardShouldPersistTaps="handled">

          <View style={{marginHorizontal: wp(5)}}>
            <CustomTextInput
              label={AppText.BARBER_NAME_LABEL}
              placeholder={AppText.BARBER_PLACEHOLDER}
              value={formData.full_name}
              onChangeText={value => handleChange('full_name', value)}
            />

            {/* <TextInput
              style={[
                styles.input,
                {
                  color: getTextColor(),
                  borderColor: getDark_Theme(),
                  backgroundColor: backgroundColor,
                },
              ]}
              placeholder={AppText.DESCRIPTION}
              placeholderTextColor={'grey'}
              value={formData.description}
              onChangeText={value => handleChange('description', value)}
              // onChangeText={setDescription}
              textAlignVertical="top"
              multiline={true}
            /> */}
             <CustomTextInput
              label={AppText.DESCRIPTION}
              placeholder={AppText.DESCRIPTION}
              value={formData.description}
              onChangeText={value => handleChange('description', value)}
              multiline={true}
            />

            <CustomTextInput
              label={AppText.EMAIL_LABEL}
              placeholder={AppText.ENTER_PHONE_NUMBER}
              value={formData.email}
              onChangeText={value => handleChange('email', value)}
              autoCapitalize="none"
              keyboardType="email-address"
            />

            <CustomTextInput
              label={AppText.PHONE_NUMBER}
              placeholder={AppText.ENTER_PHONE_NUMBER}
              value={formData.phone_number}
              onChangeText={value => handleChange('phone_number', value)}
            />
{/*
        <ResponsiveText
                color={getTextColor()}
                size={4}
                weight={'500'}
                margin={[0, 0, hp(1), 0]}
              >
                {AppText.PASSWORD}
              </ResponsiveText>
               <TextInput
               style={[styles.input,
                {
                  color: getTextColor(),
                  borderColor: getDark_Theme(),
                  backgroundColor:backgroundColor,
                },
               ]}
              placeholder={AppText.PASSWORD_PLACEHOLDER}
              value={formData.password}
              onChangeText={value => handleChange('password', value)}

            /> */}
                <>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight={'500'}
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.PASSWORD}
      </ResponsiveText>

      <View style={[styles.inputWrapper,{
         color: getTextColor(),
         borderColor: getDark_Theme(),
         backgroundColor: backgroundColor,
      }]}>
        <TextInput
          style={[
            styles.input,
            {

              flex: 1,
            },
          ]}
          placeholder={AppText.PASSWORD_PLACEHOLDER}
          placeholderTextColor={colors.grey}
          value={formData.password}
          onChangeText={value => handleChange('password', value)}
          secureTextEntry={isPasswordVisible}
        />
        <TouchableOpacity onPress={() => setIsPasswordVisible(!isPasswordVisible)}>
          <Icon
            source={isPasswordVisible ? globalpath.eyeOff : globalpath.eye}
            size={20}
            margin={[0, wp(4), 0, 0]}
            tintColor={colors.grey}
          />
        </TouchableOpacity>
      </View>
    </>
            <ResponsiveText size={4} color={getTextColor} margin={[hp(0), 0, hp(1), 0]} weight={'600'}>
              {AppText.SET_DATE_TIME}
            </ResponsiveText>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.weekDaysContainer}
            >
              {weekDays.map((day, index) => (
                <View key={index} style={styles.dayContainer}>
                  <Add_Barber_Week_Card
                    day={day.day}
                    isSelected={day.isSelected}
                    selectedTime={day.selectedTime}
                    onPress={() => handleDayPress(index)}
                    getTextColor={getTextColor}
                  />
                  <View style={styles.timeItem}>
                    {day.selectedTime ? (
                      <ResponsiveText
                        color={colors.Light_theme_maincolour}
                        size={3}
                        weight="500"
                      >
                        {day.selectedTime}
                      </ResponsiveText>
                    ) : (
                      <ResponsiveText
                        color={getTextColor()}
                        size={3}
                        weight="500"
                      >
                        -
                      </ResponsiveText>
                    )}
                  </View>
                </View>
              ))}
            </ScrollView>
          </View>
      <ImageUploadcard onImageSelect={handleImageSelect}/>

      <Add_Barber_Time_Picker_Modal
        visible={showTimeModal}
        onClose={() => setShowTimeModal(false)}
        onSave={handleTimeSelect}
        initialStartTime={formData.working_hours[selectedDay]?.start_time ? new Date(`2000-01-01 ${formData.working_hours[selectedDay].start_time}`) : new Date()}
        initialEndTime={formData.working_hours[selectedDay]?.end_time ? new Date(`2000-01-01 ${formData.working_hours[selectedDay].end_time}`) : new Date()}
      />
      <TouchableOpacity
            style={[
              styles.addButton,

            ]}

            onPress={handleSubmit}
            disabled={loading}
          >
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.ADD_BARBER}
            </ResponsiveText>
          </TouchableOpacity>
          </ScrollView>
      </KeyboardAvoidingView>

      {loading ? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default AddNewBarber;

const styles = StyleSheet.create({
  weekDaysContainer: {
    // paddingHorizontal: wp(5),
  },
  dayContainer: {
    marginRight: wp(2),
  },
  timeItem: {
    marginTop: hp(1),
    alignItems: 'center',
  },
  input: {
    height: hp(15),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    fontSize: wp(3.8),
    marginVertical: hp(1),
    flex: 1,
  },
  addButton: {
    marginTop: hp(2),
    marginBottom: hp(4),
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.Light_theme_maincolour,
    marginHorizontal: wp(5),
  },
  input: {
    height: hp(6),
    // borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    fontSize: wp(3.8),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: wp(1),
  },
});
