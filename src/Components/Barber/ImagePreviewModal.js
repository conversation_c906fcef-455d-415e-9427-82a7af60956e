import React from 'react';
import { StyleSheet, View, Modal, TouchableOpacity, Image, Dimensions } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const { width, height } = Dimensions.get('window');

const ImagePreviewModal = ({ visible, onClose, imageUri }) => {
  const { getTextColor,backgroundColor } = useTheme();
  const AppText = useAppText();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: backgroundColor}]}>
          <View style={styles.header}>
            <ResponsiveText
              color={getTextColor()}
              size={4.2}
              weight="bold"
            >
              {AppText.SALON_IMAGE}
            </ResponsiveText>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Icon source={globalpath.cross} size={wp(6)}  tintColor={getTextColor()}/>
            </TouchableOpacity>
          </View>
          
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: imageUri }}
              style={styles.previewImage}
              resizeMode="contain"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ImagePreviewModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width * 0.9,
    height: height * 0.7,
    // backgroundColor: colors.white,
    borderRadius: wp(3),
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  closeButton: {
    padding: wp(2),
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(2),
  },
  previewImage: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
  },
}); 