import React from 'react';
import { View, TouchableOpacity, StyleSheet, Image, Platform, Alert } from 'react-native';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { hp, wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { device_token_delete } from '../../Services/API/Endpoints/Auth/Auth';

const CustomDrawer_Customer = (props) => {
  const AppText = useAppText();
  const { state, navigation } = props;
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, getborderTextColor, getDark_Theme } = useTheme();
  
  const drawerItems = [
    {
      name: AppText.BOOK_NOW,
      icon: globalpath.book_now,
      screen: 'Book_now'
    },
    // {
    //   name: AppText.TREATMENTS,
    //   icon: globalpath.Treatments,
    //   screen: 'Treatments'
    // },
    {
      name: AppText.FIND_PRODUCTS,
      icon: globalpath.buy_product,
      screen: 'Products'
    },
    {
      name: AppText.GIFT_CARD,
      icon: globalpath.Gift_card,
      screen: 'Gift_Card'
    },
    {
      name: AppText.PERSONAL_AREA,
      icon: globalpath.personal_area,
      screen: 'Personal_Area'
    },
    {
      name: AppText.BOOKINGS,
      icon: globalpath.my_bookings,
      screen: 'Bookings'
    },
    {
      name: AppText.TRACK_ORDERS,
      icon: globalpath.track_orders,
      screen: 'Track_Orders'
    },
    // {
    //   name: AppText.CONTACT,
    //   icon: globalpath.Contact_us,
    //   screen: 'Contact'
    // },
    {
      name: AppText.SETTINGS,
      icon: globalpath.settings,
      screen: 'Settings'
    },
    {
      name: AppText.LOGOUT,
      icon: globalpath.logout,
      screen: 'Log out'
    }
  ];


  // const handleLogout = async () => {
  //   try {
  //     // Show confirmation alert
  //     Alert.alert(
  //       'Logout',
  //       'Are you sure you want to logout?',
  //       [
  //         {
  //           text: 'Cancel',
  //           style: 'cancel',
  //         },
  //         {
  //           text: 'Logout',
  //           style: 'destructive',
  //           onPress: async () => {
  //             try {
  //               await AsyncStorage.clear();
  //               console.log('🧹 Cleared AsyncStorage — user logged out');
  //               navigation.reset({
  //                 index: 0,
  //                 routes: [{ name: 'AuthStack' }],
  //               });
  //             } catch (error) {
  //               console.error('❌ Failed to clear storage on logout:', error);
  //               Alert.alert('Error', 'Failed to logout. Please try again.');
  //             }
  //           },
  //         },
  //       ],
  //       { cancelable: true }
  //     );
  //   } catch (error) {
  //     console.error('❌ Error showing logout alert:', error);
  //   }
  // };

  const handleLogout = async () => {
    try {
      Alert.alert(
        'Logout',
        'Are you sure you want to logout?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Logout',
            style: 'destructive',
            onPress: async () => {
              try {
                const userData = await AsyncStorage.getItem('userData');
                const fcmToken = await AsyncStorage.getItem('FCMToken');
  
                const parsedUserData = JSON.parse(userData);
                const user_id = parsedUserData?.user_id;
  
                if (user_id && fcmToken) {
                  const payload = {
                    user: user_id,
                    token: fcmToken,
                  };
  
                  console.log("payload of delete token",payload);
  
                  try {
                    const response = await device_token_delete(payload);
                    console.log('🗑️ Device token deleted successfully:', response);
                  } catch (apiErr) {
                    console.error('❌ Failed to delete device token:', apiErr);
                  }
                }
  
                await AsyncStorage.clear();
                console.log('🧹 Cleared AsyncStorage — user logged out');
  
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'AuthStack' }],
                });
              } catch (error) {
                console.error('❌ Logout error:', error);
                Alert.alert('Error', 'Failed to logout. Please try again.');
              }
            },
          },
        ],
        { cancelable: false }
      );
    } catch (error) {
      console.error('❌ Error showing logout alert:', error);
    }
  };
  
  return (
    <DrawerContentScrollView 
      {...props} 
      style={{ flex: 1, backgroundColor: backgroundColor }}
      scrollEnabled={false}
    >
      <View style={[styles.container, { backgroundColor: backgroundColor }]}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Image source={globalpath.logo} style={styles.imageContainer} tintColor={getTextColor()} />
        </View>
        <View style={[styles.borderBottom, {borderBottomColor: getDark_Theme()}]}/>
        {/* Drawer Items */}
        <View style={styles.drawerItemsContainer}>
          {drawerItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.drawerItem,
                state.routeNames[state.index] === item.screen && { backgroundColor: colors.Light_theme_maincolour }
              ]}
              onPress={() => {
                if (item.screen === 'Log out') {
                  handleLogout();
                } else {
                  navigation.navigate(item.screen);
                }
              }}
            >
              <Icon 
                source={item.icon} 
                size={23} 
                tintColor={state.routeNames[state.index] === item.screen ? colors.white : getborderTextColor()} 
              />
              <ResponsiveText 
                style={[
                  styles.drawerItemText,
                  state.routeNames[state.index] === item.screen && styles.drawerItemTextActive
                ]}
                weight={'600'}
                color={state.routeNames[state.index] === item.screen ? colors.white : getTextColor()}
                
              >
                {item.name}
              </ResponsiveText>
            </TouchableOpacity>
            
          ))}
        </View>
      </View>
    </DrawerContentScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  drawerItemsContainer: {
    flex: 1,
    paddingHorizontal: wp(2),
  },
  headerContainer: {
    padding: wp(1),
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    width: wp(20),
    height: hp(10),
    resizeMode: 'contain',
  },
  drawerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(1),
    paddingHorizontal: wp(2),
    paddingVertical: hp(1.5),
    marginVertical: hp(0.7),
    borderRadius: 8,
  },
  drawerItemText: {
    marginLeft: wp(3.5),
    fontSize: 16,
  },
  drawerItemTextActive: {
    fontWeight: 'bold',
  },
  borderBottom: {
    borderBottomWidth: 1.3,
    marginVertical: hp(1.5),
    marginHorizontal: wp(-4),
  }
});

export default CustomDrawer_Customer;
