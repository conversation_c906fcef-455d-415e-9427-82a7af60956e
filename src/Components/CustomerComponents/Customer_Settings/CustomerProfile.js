import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { wp, hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage'; // <-- Don't forget this!
import { get_customer_details } from '../../../Services/API/Endpoints/Customer/Get_Customer_Details';

const CustomerProfile = () => {
  const [userData, setUserData] = useState(null); // <-- Add this line

  const navigation = useNavigation();
  const { getTextColor, getSecondaryTextColor, getDark_Theme } = useTheme();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const data = await AsyncStorage.getItem('userData');
        if (data !== null) {
          const parsedData = JSON.parse(data);
          setUserData(parsedData); // <-- Now this works
          console.log('📦 Retrieved userData:', parsedData);
        }
      } catch (error) {
        console.error('❌ Error retrieving userData:', error);
      }
    };

    fetchUserData();
  }, []);

  const handlePress = () => {
    navigation.navigate('Personal_Profile_Info');
    console.log('Customer Profile Pressed');
  };

 


  return (
    <TouchableOpacity 
      style={[styles.container, { borderColor: getDark_Theme() }]} 
      onPress={handlePress}
    >
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Image 
            source={globalpath.profile} 
            style={{ width: wp(15), height: wp(15) }} 
            resizeMode={'contain'}
          />
        </View>
        <View style={styles.middleSection}>
          <View style={styles.nameContainer}>
            <ResponsiveText color={getTextColor()} size={4.5} weight="bold">
              {userData?.username || 'Customer Name'} {/* dynamic name */}
            </ResponsiveText>
            <Icon source={globalpath.profile_eddit} size={wp(5)} />
          </View>
          <ResponsiveText color={colors.grey} size={3.5}>
            {userData?.email || '<EMAIL>'}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: wp(2),
    marginTop: hp(2),
    padding: wp(4),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    marginRight: wp(3),
  },
  middleSection: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
});

export default CustomerProfile;
