import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { wp, hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import AsyncStorage from '@react-native-async-storage/async-storage';
const PersonalInfoProfile = () => {
  const { getTextColor,  } = useTheme();
  const [customer, setCustomer] = useState(null);
  // console.log(customer.full_name)

  useEffect(() => {
    const fetchCustomerDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('customerData');
        if (data) {
          const parsed = JSON.parse(data);
          // If it's an array, get the first customer
          const customerInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setCustomer(customerInfo);
          console.log('✅ Customer object retrieved in the PersonalInfoProfile', customerInfo);
        } else {
          console.log('ℹ️ No customer data found in AsyncStorage');
        }
      } catch (error) {
        console.log('❌ Error fetching customer details:', error);
      }
    };

    fetchCustomerDetails();
  }, []);
  

  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <Image 
          source={globalpath.profile} 
          style={styles.profileImage}
          resizeMode="contain"
        />
      </View>
      <View style={styles.rightSection}>
        <ResponsiveText 
          color={getTextColor()} 
          size={5}
          weight={'bold'}
        >
          {customer?.full_name || 'jhon smith'}
        </ResponsiveText>
        <ResponsiveText 
          color={colors.grey} 
          size={4}
          margin={[hp(1), 0, 0, 0]}
        >
          {customer?.email || 'no email is found'}
        </ResponsiveText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(3),
  },
  leftSection: {
    marginRight: wp(3),
  },
  profileImage: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
  },
  rightSection: {
    flex: 1,
  },
});

export default PersonalInfoProfile; 