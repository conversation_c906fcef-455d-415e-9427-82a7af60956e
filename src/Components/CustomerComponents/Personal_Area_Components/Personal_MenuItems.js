import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';


const Personal_MenuItem = ({ icon, title, onPress }) => {
  const { getTextColor, getSecondaryTextColor, getDark_Theme } = useTheme();

  return (
    <TouchableOpacity 
      style={[styles.container, { borderBottomColor: getDark_Theme() }]} 
      onPress={onPress}
    >
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Icon source={icon} size={wp(6)} tintColor={getTextColor()} />
        </View>
        <View style={styles.middleSection}>
          <ResponsiveText color={getTextColor()} size={4} weight={'600'}>
            {title}
          </ResponsiveText>
        </View>
        <View style={styles.rightSection}>
          <Icon source={globalpath.left} size={wp(5)} tintColor={getTextColor()}  />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    paddingVertical: hp(2),
    marginHorizontal: wp(1.5),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: wp(6),

  },
  leftSection: {
    marginRight: wp(3),
  },
  middleSection: {
    flex: 1,
  },
  rightSection: {
    marginLeft: wp(2),
  },
});

export default Personal_MenuItem; 