import { <PERSON><PERSON>, <PERSON><PERSON>ist, RefreshControl, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { DeliveryAdressData } from '../../../Mocks/CustomerMock_data';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { delete_Address, list_customer_Address } from '../../../Services/API/Endpoints/Customer/Add_New_Address';
import Loader from '../../../Custom/loader';

const DeliveryAddressCard = () => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { backgroundColor, getsky_Theme, getTextColor } = useTheme();

  const [addresses, setAddresses] = useState(DeliveryAdressData);
  const [editingId, setEditingId] = useState(null);
  const [editedText, setEditedText] = useState('');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleSave = (id) => {
    const updated = addresses.map((item) => {
      if (item.id === id) {
        return { ...item, location: editedText };
      }
      return item;
    });
    setAddresses(updated);
    setEditingId(null);
  };

  const handleDelete = (id) => {
    console.log("HandleDelete pressed with ID:", id);
    Alert.alert(
      AppText.DELETE_ADDRESS,
      AppText.SURE,
      [
        { text: AppText.CANCEL, style: 'cancel' },
        {
          text: AppText.DELETE,
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await delete_Address(id);
              console.log("Address deleted successfully");
              List_Customer_addresses(); // Refresh list after deletion
            } catch (error) {
              console.log("Error deleting address:", error?.response?.data || error.message);
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const renderItem = ({ item }) => {
    const isEditing = item.id === editingId;
    return (
      <View style={[styles.deliveryCard, { backgroundColor: getsky_Theme() }]}>
        <View style={styles.IconContainer}>
          <Icon source={item?.title === 'Home' ? globalpath.Home1 : globalpath.office} size={hp(5)} />
        </View>
        <View style={{ flex: 1 }}>
          <View style={styles.Title_Edit_Delete_Icon_row}>
            <ResponsiveText color={getTextColor()} size={6.5} weight={'400'}>
              {item?.title}
            </ResponsiveText>
            <View style={styles.edit_Del_Icons}>
              <TouchableOpacity onPress={() => navigation.navigate('Add_New_Addres', { item })}>
                <Icon source={globalpath.editt} size={hp(2.4)} />
              </TouchableOpacity>
              <TouchableOpacity onPress={() => handleDelete(item.id)}>
                <Icon
                  source={globalpath.delete_icon}
                  tintColor={colors.Light_theme_maincolour}
                  margin={[0, 0, 0, wp(4)]}
                  size={hp(2.4)}
                />
              </TouchableOpacity>
            </View>
          </View>

          <ResponsiveText
            color={getTextColor()}
            maxWidth={wp(60)}
            numberOfLines={3}
            margin={[hp(1), 0, 0, wp(5)]}
          >
            {item?.address}
          </ResponsiveText>
        </View>
      </View>
    );
  };

  const List_Customer_addresses = async () => {
    console.log("Listing customer's saved addresses");
    setLoading(true);
    try {
      const customerDataString = await AsyncStorage.getItem('customerData');
      const customerDataArray = JSON.parse(customerDataString);
      const customerData = customerDataArray[0];
      const customerId = customerData?.id;

      const response = await list_customer_Address(customerId);
      console.log("Addresses fetched successfully:", response);

      if (response) {
        setAddresses(response); // <-- This is the array for FlatList
      } else {
        console.log('No addresses found');
        setAddresses([]); // Clear if none found
      }
    } catch (error) {
      console.log("Error in List_Customer_addresses:", error?.response?.data || error.message);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      List_Customer_addresses();
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await List_Customer_addresses();
    setRefreshing(false);
  };

  return (
    loading ? (
      <Loader />
    ) : (
      <View style={{ flex: 1 }}>
        <FlatList
          data={addresses}
          renderItem={renderItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingBottom: hp(20) }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              colors={[colors.Light_theme_maincolour]}
              onRefresh={onRefresh}
            />
          }
          ListEmptyComponent={(
            <ResponsiveText textAlign={'center'} size={4} margin={[hp(10), 0, 0, 0]}>
              {AppText.NO_RECORD_FOUND}
            </ResponsiveText>
          )}
        />

        {/* Add New Address Button */}
        <TouchableOpacity
          style={styles.AddNewAddress_Button_Style}
          onPress={() => navigation.navigate('Add_New_Addres')}
        >
          <ResponsiveText color={colors.white} weight={'400'} size={4.5}>
            {AppText.ADD_NEW_ADDRESS}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    )
  );
};

export default DeliveryAddressCard;

const styles = StyleSheet.create({
  deliveryCard: {
    borderRadius: wp(3),
    marginHorizontal: wp(5),
    paddingVertical: hp(2),
    flexDirection: 'row',
    paddingHorizontal: wp(4),
    marginVertical: hp(1),
  },
  IconContainer: {},
  Title_Edit_Delete_Icon_row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: wp(5),
  },
  edit_Del_Icons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  AddNewAddress_Button_Style: {
    position: 'absolute',
    bottom: hp(3),
    left: wp(5),
    right: wp(5),
    backgroundColor: colors.Light_theme_maincolour,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2),
    borderRadius: wp(3),
    elevation: 5, // for Android shadow
    shadowColor: '#000', // for iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
