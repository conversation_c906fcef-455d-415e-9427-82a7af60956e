import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity ,Image} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import AsyncStorage from '@react-native-async-storage/async-storage';
const 
Personal_ProfileSection = () => {
  console.log('👀 Component Rendered | customer:', customer);

  const navigation = useNavigation();
  const { getTextColor, getSecondaryTextColor, getDark_Theme, } = useTheme();
   const handlePress = () => {
    navigation.navigate('Personal_Profile_Info');
    console.log(' Perosnal profile section pressed');
   }
const [customer,SetCustomer]=useState(null)

   useEffect(() => {
    const fetchCustomerData = async () => {
      try {
        const data = await AsyncStorage.getItem('userData');
        if (data !== null) {
          const parsedData = JSON.parse(data);
          SetCustomer(parsedData); // <-- Now this works
          console.log('📦 Retrieved CustomerData:', parsedData);
        }
      } catch (error) {
        console.error('❌ Error retrieving userData:', error);
      }
    };

    fetchCustomerData();
  }, []);


  return (
    <TouchableOpacity style={[styles.container, { borderColor: getDark_Theme() }]} onPress={handlePress}>
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Image source={globalpath.profile} style={{width:wp(15),height:wp(15)}} resizeMode={'contain'}/>
        </View>
        <View style={styles.middleSection}>
          <View style={styles.nameContainer}>
            <ResponsiveText color={getTextColor()} size={4.5} weight="bold">
              {customer?.username }
            </ResponsiveText>
            <TouchableOpacity>
              <Icon source={globalpath.profile_eddit} size={wp(4)} margin={[0,0,0,wp(3)]}  />
            </TouchableOpacity>
          </View>
          <ResponsiveText color={colors.grey} size={3.5}>
            {customer?.email}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    // borderWidth: 1,
    // borderRadius: wp(3),
    marginHorizontal: wp(2),
    marginTop: hp(2),
    padding: wp(4),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    marginRight: wp(3),
    
  },
  middleSection: {
    flex: 1,
    // backgroundColor:'pink'
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
});

export default Personal_ProfileSection; 