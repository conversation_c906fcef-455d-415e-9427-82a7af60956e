import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { globalpath } from '../../../Custom/globalpath';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
const CustomeProfileInfoHeader = () => {
  const navigation = useNavigation();
  const { getTextColor } = useTheme();
  const AppText = useAppText();     

  return (
    <View style={styles.headerContainer}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Icon source={globalpath.goback} size={wp(5)} tintColor={getTextColor()} />
      </TouchableOpacity>
      <ResponsiveText 
        color={getTextColor()} 
        size={5}
        weight={'bold'}
      >
        {AppText.PERSONAL_INFO }
      </ResponsiveText>
      <TouchableOpacity style={styles.editButton} >
        {/* <Icon source={globalpath.edit} size={wp(5)} tintColor={getTextColor()} /> */}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
  },
  backButton: {
    padding: wp(2),
  },
  editButton: {
    padding: wp(2),
  },
});

export default CustomeProfileInfoHeader; 