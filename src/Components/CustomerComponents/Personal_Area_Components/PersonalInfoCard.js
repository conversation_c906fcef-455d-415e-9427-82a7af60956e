import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { wp, hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';
import AsyncStorage from '@react-native-async-storage/async-storage';

const InfoItem = ({ icon, label, value }) => {
  const { getTextColor } = useTheme();

  return (
    <View style={styles.infoItem}>
      <View style={styles.iconContainer}>
        <Icon source={icon} size={wp(6)} style={{ tintColor: getTextColor() }} />
      </View>
      <View style={styles.textContainer}>
        <ResponsiveText color={getTextColor()} size={4.5}>
          {label}
        </ResponsiveText>
        <ResponsiveText color={"#6B6E82"} size={4} margin={[hp(0.5), 0, 0, 0]}>
          {value}
        </ResponsiveText>
      </View>
    </View>
  );
};

const PersonalInfoCard = () => {
  const AppText = useAppText();
  const { getsky_Theme } = useTheme();
  const [customer, setCustomer] = useState(null);

  useEffect(() => {
    const fetchCustomerDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('customerData');
        if (data) {
          const parsed = JSON.parse(data);
          const customerInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setCustomer(customerInfo);
          console.log('✅ Customer object retrieved in the Personal info card:', customerInfo);
        }
      } catch (error) {
        console.log('❌ Error fetching customer details:', error);
      }
    };

    fetchCustomerDetails();
  }, []);

  if (!customer) return null; // Optional: show a loader or placeholder

  return (
    <View style={[styles.container, { backgroundColor: getsky_Theme() }]}>
      <InfoItem
        icon={globalpath.user}
        label={AppText.FULL_NAME}
        value={customer?.full_name || 'jhon'}
      />
      <InfoItem
        icon={globalpath.email}
        label={AppText.EMAIL}
        value={customer?.email ||'jhon'}
      />
      <InfoItem
        icon={globalpath.phone}
        label={AppText.PHONE_NUMBER}
        value={customer?.phone_number ||"jhon"}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: wp(4),
    borderRadius: wp(4),
    padding: wp(4),
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: hp(3),
  },
  iconContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    marginRight: wp(3),
  },
  textContainer: {
    flex: 1,
  },
});

export default PersonalInfoCard;
