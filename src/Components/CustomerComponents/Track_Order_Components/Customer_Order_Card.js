import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { wp, hp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';
import { useNavigation } from '@react-navigation/native';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const Customer_Order_Card = ({ order }) => {
  const navigation = useNavigation();
  const { getTextColor, backgroundColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  const handleTrackNow = () => {
    navigation.navigate('Order_Details', { order });
  };

  // Format the date to a more readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get the first item's product name and image
  const firstItem = order.items && order.items.length > 0 ? order.items[0] : null;
  const productName = firstItem ? firstItem.product_name : 'No Product';
  const productImage = firstItem?.product_image;

  return (
    <View style={[styles.card, { borderColor: getDark_Theme() }]}>
      <View style={styles.content}>
        <View style={[styles.imageContainer, { borderColor: getDark_Theme() }]}>
          {productImage ? (
            <Image 
              source={{ uri: productImage }} 
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <Icon 
              source={globalpath.logo} 
              size={wp(12)}
              tintColor={colors.Light_theme_maincolour}
            />
          )}
        </View>
        <View style={styles.details}>
          <View style={styles.titleRow}>
            <ResponsiveText color={getTextColor()} numberOfLines={1} maxWidth={wp(30)} weight={'600'} size={4}>
              {productName ? productName : "-"}
            </ResponsiveText>
            <ResponsiveText color={colors.Light_theme_maincolour} weight={'600'}>
              {order.status}
            </ResponsiveText>
          </View>
          <View style={styles.amountRow}>
            <ResponsiveText color={getTextColor()} weight={'600'}>
              ${order.total_price} <ResponsiveText color={"#6B6E82"}>| {order.items.length} {AppText.ITEMS}</ResponsiveText>
            </ResponsiveText>
          </View>
          <ResponsiveText color={"#6B6E82"} size={3} margin={[wp(2),0,0,0]}>
            {formatDate(order.order_date)}
          </ResponsiveText>
        </View>
      </View>
      <TouchableOpacity 
        style={[styles.trackButton, { backgroundColor: colors.Light_theme_maincolour }]}
        onPress={handleTrackNow}
      >
        <ResponsiveText weight={'600'} color={colors.white}>
          {/* {AppText.VIEW_DETAILS} */}
          {AppText.TRACK_NOW}
        </ResponsiveText>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
    borderWidth: 1,
    marginRight: wp(3),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    overflow: 'hidden'
  },
  image: {
    width: '100%',
    height: '100%',
  },
  details: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(1),
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  trackButton: {
    alignSelf: 'flex-end',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1),
    borderRadius: wp(1.5),
    marginTop: hp(1),
  },
});

export default Customer_Order_Card; 