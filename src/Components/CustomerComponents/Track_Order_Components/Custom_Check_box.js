import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const Custom_Check_box = ({ value, onValueChange, onTintColor, onCheckColor }) => {
  return (
    <TouchableOpacity onPress={onValueChange}>
      <View style={[styles.checkbox, { borderColor: onTintColor }]}>
        {value && (
          <Icon
            source={globalpath.check}
            size={wp(4)}
            tintColor={onCheckColor}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    width: wp(5),
    height: wp(5),
    borderWidth: 1,
    borderRadius: wp(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Custom_Check_box; 