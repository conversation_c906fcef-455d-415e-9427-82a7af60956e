import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const Customer_Check_box = ({ value, onValueChange, onTintColor, onCheckColor }) => {
  return (
    <View onPress={onValueChange}>
      <View style={[styles.checkbox, { borderColor: onTintColor }]}>
        {value && (
          <Icon
            source={globalpath.check}
            size={wp(4)}
            tintColor={onCheckColor}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    width: wp(5),
    height: wp(5),
    borderWidth: 1,
    borderRadius: wp(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    width: wp(3),
    height: wp(1.5),
    borderLeftWidth: 2,
    borderBottomWidth: 2,
    transform: [{ rotate: '-45deg' }],
    marginTop: -wp(0.5),
  },
});

export default Customer_Check_box; 