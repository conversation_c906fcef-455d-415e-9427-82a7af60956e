import { View, Text, StyleSheet } from 'react-native';
import React from 'react';
import { wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import Customer_Check_box from './Customer_Check_box';

const Customer_OrderCheckbox = ({ value, onValueChange, date, label, style }) => {
  const AppText = useAppText();
  const { getTextColor } = useTheme();    
  return (
    <View style={[styles.container, style]}>
      <View style={styles.checkboxContainer}>
        <Customer_Check_box
          value={value}
          onValueChange={onValueChange}
          onTintColor={colors.Light_theme_maincolour}
          onCheckColor={colors.white}
        />
        <ResponsiveText color={getTextColor()} weight={'600'} margin={[0,0,0,wp(2)]} size={4}>{label}</ResponsiveText>
      </View>
      <View style={styles.textContainer}>
        <ResponsiveText color={getTextColor()} size={3.5}>{date}</ResponsiveText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: wp(2),
    justifyContent: 'space-between',
    paddingHorizontal: wp(2),
  },
  textContainer: {
    marginLeft: wp(3),
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default Customer_OrderCheckbox; 