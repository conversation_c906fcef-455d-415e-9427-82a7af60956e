import React from 'react';
import { StyleSheet, View, Modal, TouchableOpacity, FlatList, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { wp, hp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';

const Customer_OrderItemsModal = ({ visible, onClose, order }) => {
  const { getTextColor, getDark_Theme, getsky_Theme } = useTheme();
  const AppText = useAppText();

  const renderItem = ({ item }) => (
    <View style={[styles.itemContainer, { borderColor: getDark_Theme() }]}>
      <View style={[styles.imageContainer, { borderColor: getDark_Theme() }]}>
        {item.product_image ? (
          <Image 
            source={{ uri: item.product_image }}
            style={styles.productImage}
            resizeMode="cover"
          />
        ) : (
          <Icon 
            source={globalpath.logo}
            size={wp(12)}
            tintColor={colors.Light_theme_maincolour}
          />
        )}
      </View>
      <View style={styles.itemDetails}>
        <ResponsiveText color={getTextColor()} size={4} weight="600">
          {item.product_name}
        </ResponsiveText>
        <View style={styles.detailRow}>
          <ResponsiveText color={colors.Light_theme_maincolour} size={3.5} weight="500">
            ${item.price_at_purchase}
          </ResponsiveText>
          <ResponsiveText color={getTextColor()} size={3.5}>
            × {item.quantity}
          </ResponsiveText>
        </View>
        <ResponsiveText color={getTextColor()} size={3.5} weight="500">
          Total: ${(parseFloat(item.price_at_purchase) * item.quantity).toFixed(2)}
        </ResponsiveText>
      </View>
    </View>
  );

  const renderHeader = () => (
    <View style={[styles.orderSummary, { borderColor: getDark_Theme() }]}>
      <ResponsiveText color={getTextColor()} size={3.8} weight="500">
        Order Date: {new Date(order.order_date).toLocaleDateString()}
      </ResponsiveText>
      <ResponsiveText color={colors.Light_theme_maincolour} size={3.8} weight="600">
        Total Amount: ${order.total_price}
      </ResponsiveText>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity 
          style={styles.overlay} 
          activeOpacity={1} 
          onPress={onClose}
        />
        <View style={[styles.modalContent, { backgroundColor: getsky_Theme() }]}>
          <View style={styles.header}>
            <ResponsiveText color={getTextColor()} size={4.5} weight="600">
              Order #{order.id} Details
            </ResponsiveText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon source={globalpath.cross} size={wp(5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={order.items}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={renderHeader}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>
      </View>
    </Modal>
  );
};

export default Customer_OrderItemsModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    width: '100%',
    maxHeight: '70%',
    height: '70%',
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    paddingBottom: hp(2),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  closeButton: {
    padding: wp(2),
  },
  listContainer: {
    paddingBottom: hp(2),
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1
  },
  imageContainer: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
    borderWidth: 1,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  itemDetails: {
    marginLeft: wp(3),
    flex: 1,
    justifyContent: 'space-between',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: hp(0.5),
  },
  orderSummary: {
    padding: wp(3),
    marginBottom: hp(2),
    borderRadius: wp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1
  },
  separator: {
    height: hp(1.5),
  },
}); 