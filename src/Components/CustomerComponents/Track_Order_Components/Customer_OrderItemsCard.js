import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { wp, hp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';

const Customer_OrderItemsCard = ({ onViewAll }) => {
  const { getTextColor, getDark_Theme, getsky_Theme } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="600"
        margin={[hp(2), 0, hp(2), 0]}
      >
        {AppText.ORDER_ITEMS}
      </ResponsiveText>
      
      <View style={[styles.detailsCard, { borderColor: getDark_Theme(), backgroundColor: getsky_Theme() }]}>
        <View style={styles.row}>
          <Icon source={globalpath.order_item} size={wp(6)} tintColor={getTextColor()} />
          <ResponsiveText color={getTextColor()} size={3.8} margin={[0, 0, 0, wp(2)]}>
            {AppText.ITEMS} 
          </ResponsiveText>
          <TouchableOpacity onPress={onViewAll} style={styles.viewAllButton}>
            <ResponsiveText color={colors.Light_theme_maincolour} size={3.5} weight="bold">
              {AppText.VIEW_ALL}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default Customer_OrderItemsCard;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  detailsCard: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(4),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllButton: {
    marginLeft: 'auto',
  },
}); 