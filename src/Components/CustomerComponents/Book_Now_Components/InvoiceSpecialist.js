import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import useTheme from '../../../Redux/useTheme'
import useAppText from '../../../Custom/AppText'
import { colors } from '../../../Custom/Colors'

const InvoiceSpecialist = ({ specialist, duration }) => {
  const { getTextColor } = useTheme()
  const AppText = useAppText()

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={styles.column}>
          <ResponsiveText 
            size={3.5} 
            weight={'500'} 
            color={getTextColor()}
          >
            {AppText.SPECIALIST}
          </ResponsiveText>
          <ResponsiveText 
            size={3.8} 
            weight={'600'} 
            color={colors.Light_theme_maincolour}
            margin={[hp(0.5), 0, 0, 0]}
          >
            {/* {specialist?.name} */}
            Bella
          </ResponsiveText>
        </View>
        <View style={styles.column}>
          <ResponsiveText 
            size={3.5} 
            weight={'500'} 
            color={getTextColor()}
          >
            {AppText.DURATION}
          </ResponsiveText>
          <ResponsiveText 
            size={3.8} 
            weight={'600'} 
            color={colors.Light_theme_maincolour}
            margin={[hp(0.5), 0, 0, 0]}
          >
            {duration}
          </ResponsiveText>
        </View>
      </View>
      <View style={[styles.border, { borderBottomColor: getTextColor() }]} />
    </View>
  )
}

export default InvoiceSpecialist

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  column: {
    flex: 1,
  },
  border: {
    borderBottomWidth: 1,
    marginTop: hp(2),
    opacity: 0.2,
  },
}) 