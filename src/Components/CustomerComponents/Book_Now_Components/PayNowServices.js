import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import useTheme from '../../../Redux/useTheme'
import useAppText from '../../../Custom/AppText'
import { colors } from '../../../Custom/Colors'
import DashedLine from './DashedLine'

const PayNowServices = ({ services }) => {
  const { getTextColor } = useTheme()
  const AppText = useAppText()

  return (
    <View style={styles.container}>
      <ResponsiveText 
        size={4} 
        weight={'600'} 
        color={getTextColor()}
        textAlign={'center'}
        margin={[0, 0, hp(2), 0]}
      >
        {AppText.SERVICE}
      </ResponsiveText>

      {services?.map((service, index) => (
        <View key={index} style={styles.serviceRow}>
          <ResponsiveText 
            size={3.8} 
            weight={'500'} 
            color={getTextColor()}
          >
            {service.name}
          </ResponsiveText>
          <ResponsiveText 
            size={3.8} 
            weight={'600'} 
            color={colors.c_green}
          >
            ${service.price}
          </ResponsiveText>
        </View>
      ))}

      <DashedLine color={getTextColor()} />
    </View>
  )
}

export default PayNowServices

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  serviceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
}) 