import React from 'react';
import {StyleSheet, View, Image} from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import {hp, wp} from '../../../Custom/Responsiveness';
import {globalpath} from '../../../Custom/globalpath';

const DateTimeDisplay = ({selectedDate, selectedTimeSlot}) => {
  const {getTextColor} = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Image
          source={globalpath.calendar}
          style={[styles.icon, {tintColor: getTextColor()}]}
          resizeMode="contain"
        />
        <ResponsiveText
          color={getTextColor()}
          size={4.2}
          weight="600"
          margin={[0, 0, 0, wp(2)]}>
          {AppText.DATE_TIME}
        </ResponsiveText>
      </View>

      <View style={styles.detailsContainer}>
        <ResponsiveText color={getTextColor()} size={3.8} weight="500">
          {selectedDate + '\n' + selectedTimeSlot?.slice(0, 5)}
        </ResponsiveText>
      </View>
    </View>
  );
};

export default DateTimeDisplay;

const styles = StyleSheet.create({
  container: {
    padding: wp(3),
    marginTop: hp(3),
    marginBottom: hp(1),
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  icon: {
    width: wp(4),
    height: wp(4),
    // tintColor: colors.Light_theme_maincolour,
  },
  detailsContainer: {
    marginLeft: wp(8),
  },
});
