import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const SelectedServiceCard = ({ service, onSeeDetails, isSelected }) => {
  const { getTextColor, } = useTheme();
  const AppText = useAppText();

  if (!service) return null;
  console.log("SERVICE IS momnaaa ",service)

  const handleSeeDetailsPress = (e) => {
    e.stopPropagation();
    onSeeDetails(service);
  };

  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image
          source={
            service.service_image 
              ? typeof service.service_image === 'string'
                ? { uri: service.service_image }
                : { uri: service.service_image.uri }
              : globalpath.logo
          }
          style={styles.image}
        />
        {isSelected && (
          <View style={styles.tickContainer}>
            <Icon source={globalpath.white_tick} size={wp(6)} />
          </View>
        )}
      </View>
      <ResponsiveText
        color={getTextColor()}
        size={3.5}
        weight="500"
        style={styles.title}
        numberOfLines={1}
      >
        {service.name || service.title}
      </ResponsiveText>
      <ResponsiveText
        color={colors.Light_theme_maincolour}
        // size={3}
        weight="600"
        style={styles.price}
      >
        ${service.price || service.amount}
      </ResponsiveText>
      <TouchableOpacity 
        style={styles.detailsButton}
        onPress={handleSeeDetailsPress}
      >
        <Icon source={globalpath.eye2} size={wp(4)} tintColor={getTextColor()} />
        <ResponsiveText
          color={colors.grey}
          size={3.2}
          weight="500"
          margin={[0, 0, 0, wp(1)]}
        >
          {AppText.SEE_DETAILS}
        </ResponsiveText>
      </TouchableOpacity>
    </View>
  );
};

export default SelectedServiceCard;

const styles = StyleSheet.create({
  container: {
    width: wp(25),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  imageContainer: {
    position: 'relative',
    marginBottom: hp(1),
  },
  image: {
    width: wp(18),
    height: wp(18),
    borderRadius: wp(10),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  tickContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: wp(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: hp(0.5),
  },
  price: {
    marginBottom: hp(0.5),
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}); 