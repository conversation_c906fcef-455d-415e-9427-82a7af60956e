import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import useTheme from '../../../Redux/useTheme'
import useAppText from '../../../Custom/AppText'
import { colors } from '../../../Custom/Colors'
import DashedLine from './DashedLine'
const InvoiceSummary = ({ services, discount }) => {
  const { getTextColor } = useTheme()
  const AppText = useAppText()

  // Calculate subtotal
  const subtotal = services?.reduce((sum, service) => sum + parseFloat(service.price), 0) || 0
  const discountAmount = (subtotal * discount) / 100
  const total = subtotal - discountAmount

  return (
    <View style={styles.container}>
      <View style={styles.summaryRow}>
        <ResponsiveText 
          size={3.8} 
          weight={'500'} 
          color={getTextColor()}
        >
          {AppText.SUBTOTAL}
        </ResponsiveText>
        <ResponsiveText 
          size={3.8} 
          weight={'600'} 
          color={colors.Light_theme_maincolour}
        >
          ${subtotal.toFixed(2)}
        </ResponsiveText>
      </View>

      <View style={styles.summaryRow}>
        <ResponsiveText 
          size={3.8} 
          weight={'500'} 
          color={getTextColor()}
        >
          {AppText.DISCOUNT} ({discount}%)
        </ResponsiveText>
        <ResponsiveText 
          size={3.8} 
          weight={'600'} 
          color={colors.red}
        >
          -${discountAmount.toFixed(2)}
        </ResponsiveText>
      </View>

      {/* <View style={[styles.dottedLine, { borderBottomColor: getTextColor() }]} /> */}
      <DashedLine color={getTextColor()} />
      <View style={styles.summaryRow}>
        <ResponsiveText 
          size={4.2} 
          weight={'600'} 
          color={getTextColor()}
        >
          {AppText.TOTAL}
        </ResponsiveText>
        <ResponsiveText 
          size={4.2} 
          weight={'600'} 
          color={colors.Light_theme_maincolour}
        >
          ${total.toFixed(2)}
        </ResponsiveText>
      </View>
    </View>
  )
}

export default InvoiceSummary

const styles = StyleSheet.create({
  container: {
    marginTop: hp(2),
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  dottedLine: {
    borderBottomWidth: 1,
    borderStyle: 'dashed',
    marginVertical: hp(2),
    opacity: 0.2,
  },
}) 