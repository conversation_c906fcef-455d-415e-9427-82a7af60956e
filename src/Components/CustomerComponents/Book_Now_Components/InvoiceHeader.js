import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import useTheme from '../../../Redux/useTheme'
import useAppText from '../../../Custom/AppText'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'

const InvoiceHeader = () => {
  const { getTextColor } = useTheme()
  const AppText = useAppText()

  return (
    <View style={styles.container}>
      <View style={styles.headerContent}>
       
        <ResponsiveText 
          size={4.5} 
          weight={'600'} 
          color={colors.Light_theme_maincolour}
        >
          {AppText.PAY_IN_SALON}
        </ResponsiveText>
        <Icon 
          source={globalpath.coupon} 
          size={wp(6)} 
          tintColor={colors.Light_theme_maincolour}
          margin={[0, wp(2), 0, wp(2)]}
        />
      </View>
    </View>
  )
}

export default InvoiceHeader

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'center',
    // paddingVertical: hp(2),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),

  },
}) 