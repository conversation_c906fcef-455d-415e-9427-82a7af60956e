import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';

const TimeSlot = ({ time, isSelected, isBooked, onPress }) => {
  const { getTextColor, getDark_Theme } = useTheme();

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isSelected ? colors.Light_theme_maincolour : 'transparent',
          borderColor: isSelected ? colors.Light_theme_maincolour : getDark_Theme(),
          opacity: isBooked ? 0.5 : 1,
        },
      ]}
      onPress={onPress}
      disabled={isBooked}
    >
      <ResponsiveText
        size={4}
        weight="500"
        color={isSelected ? colors.white : getTextColor()}
      >
        {formatTime(time)}
      </ResponsiveText>
    </TouchableOpacity>
  );
};

export default TimeSlot;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: wp(2.5),
    borderRadius: wp(1.5),
    borderWidth: 1.1,
    marginHorizontal: wp(1),
    marginVertical: hp(0.5),
    paddingVertical: hp(0.7),
    // backgroundColor:"red"
  },
}); 