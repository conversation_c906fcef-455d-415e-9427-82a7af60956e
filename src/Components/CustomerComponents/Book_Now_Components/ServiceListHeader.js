import React from 'react';
import { StyleSheet, View, Image } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { globalpath } from '../../../Custom/globalpath';

const ServiceListHeader = () => {
  const { getTextColor } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <Image
        source={globalpath.Scissors}
        style={[styles.icon,{tintColor:getTextColor()}]}
        resizeMode="contain"
      />
      <ResponsiveText
        color={getTextColor()}
        size={4.2}
        weight="600"
        margin={[0, 0, 0, wp(2)]}
      >
        {AppText.SERVICE_LIST}
      </ResponsiveText>
    </View>
  );
};

export default ServiceListHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),

  },
  icon: {
    width: wp(4.5),
    height: wp(4.5),
    // tintColor: colors.Light_theme_maincolour,
  },
}); 