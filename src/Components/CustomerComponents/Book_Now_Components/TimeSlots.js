import React, {useState, useEffect} from 'react';
import {StyleSheet, View, ScrollView, TouchableOpacity} from 'react-native';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import TimeSlot from './TimeSlot';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import {colors} from '../../../Custom/Colors';

const TimeSlots = ({selectedDate, availabilityData, onTimeSelect}) => {
  const {getTextColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const [selectedTime, setSelectedTime] = useState(null);
  const [showTimeSlots, setShowTimeSlots] = useState(true);
  const [year, month, day] = selectedDate.split('-');
  const monthKey = `${month}-${year}`; // "05-2025"
  const dateKey = `${day}-${month}-${year}`; // "02-05-2025"
  const timeSlots = availabilityData?.[monthKey]?.[dateKey] || [];

  const handleTimeSelect = time => {
    setSelectedTime(time);
    onTimeSelect?.(time);
  };

  const handleToggleTimeSlots = () => {
    setShowTimeSlots(!showTimeSlots);
  };

  // const timeSlots = selectedDate ? timeSlotsData[formatDate(selectedDate)] || [] : [];

  // Reset view when date changes
  useEffect(() => {
    console.log('timeSlots in TimeSlots ', timeSlots);
    setShowTimeSlots(true);
    if (timeSlots.length > 0) {
      const firstAvailableSlot = timeSlots.find(slot => !slot.is_booked);
      if (firstAvailableSlot) {
        setSelectedTime(firstAvailableSlot.start_time);
        onTimeSelect?.(firstAvailableSlot.start_time);
        console.log('firstAvailableSlot==', firstAvailableSlot.start_time);
      }
    } else {
      setSelectedTime(null);
      onTimeSelect?.(null);
    }
  }, [selectedDate, availabilityData]);

  const groupedTimeSlots = [];
  for (let i = 0; i < timeSlots.length; i += 4) {
    groupedTimeSlots.push(timeSlots.slice(i, i + 4));
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <ResponsiveText
          size={4.2}
          weight="600"
          color={getTextColor()}
          style={styles.title}>
          {AppText.AVAILABLE_TIME}
        </ResponsiveText>
        {timeSlots.length > 0 && selectedTime && (
          <TouchableOpacity onPress={handleToggleTimeSlots}>
            {showTimeSlots ? (
              <Icon source={globalpath.tick} size={wp(5.5)} />
            ) : (
              <View style={styles.selectedTimeContainer}>
                <Icon
                  source={globalpath.watch}
                  size={wp(3.7)}
                  tintColor={colors.white}
                />
                <ResponsiveText
                  size={4}
                  color={colors.white}
                  style={styles.selectedTimeText}>
                  {selectedTime}
                </ResponsiveText>
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>
      {timeSlots.length > 0 ? (
        showTimeSlots ? (
          <View
            style={[styles.timeSlotsContainer, {borderColor: getDark_Theme()}]}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}>
              {groupedTimeSlots.map((row, rowIndex) => (
                <View key={rowIndex} style={styles.row}>
                  {row.map(slot => (
                    <TimeSlot
                      key={slot.id}
                      time={slot.start_time}
                      isSelected={selectedTime === slot.start_time}
                      isBooked={slot.is_booked}
                      onPress={() => handleTimeSelect(slot.start_time)}
                    />
                  ))}
                </View>
              ))}
            </ScrollView>
          </View>
        ) : null
      ) : (
        <View style={[styles.noSlotsContainer, {borderColor: getDark_Theme()}]}>
          <ResponsiveText
            size={3.5}
            color={getTextColor()}
            style={styles.noSlotsText}>
            {AppText.NO_AVAILABLE_TIME}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

export default TimeSlots;

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(2),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(3),
    marginBottom: hp(1),
  },
  title: {
    flex: 1,
    marginVertical: hp(1),
  },
  timeSlotsContainer: {
    height: hp(23),
    backgroundColor: 'transparent',
    borderRadius: wp(1.5),
    // borderWidth: 1,
    // borderColor: '#E0E0E0',
    marginHorizontal: wp(2),
  },
  scrollContent: {
    // padding: wp(2),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: hp(1),
  },
  noSlotsContainer: {
    height: hp(23),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderRadius: wp(1.5),
    // borderWidth: 1,
    marginHorizontal: wp(2),

    // borderColor: '#E0E0E0',
  },
  noSlotsText: {
    textAlign: 'center',
    paddingHorizontal: wp(5),
  },
  selectedTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.grey,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    borderRadius: wp(1.5),
  },
  selectedTimeText: {
    marginLeft: wp(2),
  },
});
