import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import SelectedServiceCard from './SelectedServiceCard';
import Book_Now_Details_Modal from './Book_Now_Details_Modal';
import AddServiceModal from '../../../Components/Salons/AddServiceModal';

const SelectedServicesList = ({ selectedServices, onServicesUpdate }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const [modalVisible, setModalVisible] = useState(false);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [selectedServiceForDetails, setSelectedServiceForDetails] = useState(null);
  const [selectedServiceIds, setSelectedServiceIds] = useState([]);

  useEffect(() => {
    // Initialize with all services selected by default
    if (selectedServices.length > 0) {
      setSelectedServiceIds(selectedServices.map(service => service.id));
    }
  }, [selectedServices]);

  const handleAddService = () => {
    setModalVisible(true);
  };

  const handleServiceSelect = (newServices) => {
    onServicesUpdate([...selectedServices, ...newServices]);
    setModalVisible(false);
  };

  const handleSeeDetails = (service) => {
    setSelectedServiceForDetails(service);
    setDetailsModalVisible(true);
  };

  const handleServicePress = (serviceId) => {
    setSelectedServiceIds(prevIds => {
      if (prevIds.includes(serviceId)) {
        return prevIds.filter(id => id !== serviceId);
      } else {
        return [...prevIds, serviceId];
      }
    });
  };

  const renderServiceCard = ({ item }) => (
    <TouchableOpacity onPress={() => handleServicePress(item.id)}>
      <SelectedServiceCard
        service={item}
        onSeeDetails={handleSeeDetails}
        isSelected={selectedServiceIds.includes(item.id)}
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <View style={styles.headerRow}>
          <ResponsiveText
            size={4.2}
            weight="600"
            color={getTextColor()}
            style={styles.title}
          >
            {AppText.SELECTED_SERVICES}
          </ResponsiveText>
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddService}
          >
            <Icon source={globalpath.plus} size={wp(3.5)} tintColor={colors.white} />
            <ResponsiveText
              color={colors.white}
              size={3.2}
              weight="500"
              margin={[0, 0, 0, wp(1)]}
            >
              {AppText.ADD_SERVICE}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
        <View style={[styles.borderBottom, { backgroundColor: getDark_Theme() }]} />
      </View>
      
      <FlatList
        data={selectedServices}
        renderItem={renderServiceCard}
        keyExtractor={item => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      />

      <Book_Now_Details_Modal
        visible={detailsModalVisible}
        onClose={() => setDetailsModalVisible(false)}
        service={selectedServiceForDetails}
      />

      <AddServiceModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onServiceSelect={handleServiceSelect}
        previouslySelectedServices={selectedServices}
      />
    </View>
  );
};

export default SelectedServicesList;

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(2),
  },
  headerContainer: {
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  title: {
    fontSize: wp(4.8),
    fontWeight: '600',
  },
  borderBottom: {
    height: 1,
    bottom:wp(0.5)
  },
  scrollContent: {
    paddingHorizontal: wp(2),
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(1.4),
    paddingVertical: hp(0.8),
    borderRadius: wp(1.5),
  },
}); 