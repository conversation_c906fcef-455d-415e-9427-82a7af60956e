import React, {useState, useEffect} from 'react';
import {StyleSheet, ScrollView, View} from 'react-native';
import {hp, wp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import DateCard from './DateCard';
import useAppText from '../../../Custom/AppText';
import TimeSlots from './TimeSlots';
import {timeSlotsData} from '../../../Custom/mockData';
import {getbarbarsAvailability} from '../../../Services/API/Endpoints/Customer/BookAppointment';

const DateRangeCards = ({selectedDate,availabilityData,onTimeSelected,onDateSelected}) => {
  const AppText = useAppText();
  const [availableDates, setAvailableDates] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [selectedDateForTime, setSelectedDateForTime] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  useEffect(() => {
    if (selectedDate) {
      const startDate = new Date(selectedDate);
      const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
      const dates = [];
      
      for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
        const formattedDate = date.toISOString().split('T')[0];
        dates.push({
          date: formattedDate,
          day: date.toLocaleDateString('en-US', { weekday: 'short' }),
          isSelected: false
        });
      }
      
      setAvailableDates(dates);
      // Select the first date by default
      if (dates.length > 0) {
        setSelectedCard(dates[0].date);
        setSelectedDateForTime(dates[0].date);
        onDateSelected(dates[0].date);
      }

      // Log the week data array
      console.log('🟢 Week Data Array:', dates);
    }
  }, [selectedDate]);
  useEffect(() => {
    if (onTimeSelected) {
      onTimeSelected(selectedTimeSlot);
    }
  }, [selectedTimeSlot]);
  const handleCardPress = (date) => {
    setSelectedCard(date);
    setSelectedDateForTime(date);
  
    // Send selected date to parent
    if (onDateSelected) {
      onDateSelected(date);
    }
  
    console.log('🟢 Selected Date:', date);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {availableDates.map((dateInfo) => (
          <DateCard
            key={dateInfo.date}
            day={dateInfo.day}
            date={dateInfo.date}
            isSelected={selectedCard === dateInfo.date}
            onPress={() => handleCardPress(dateInfo.date)}
          />
        ))}
      </ScrollView>
      {selectedDateForTime && <TimeSlots selectedDate={selectedDateForTime} availabilityData={availabilityData} onTimeSelect={setSelectedTimeSlot}/>}
    </View>
  );
};

export default DateRangeCards;

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(1),
  },
  title: {
    marginBottom: hp(1),
    paddingHorizontal: wp(3),
  },
  scrollContent: {
    paddingHorizontal: wp(1),
  },
});
