import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import {colors} from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
// import { barbersData } from '../../../Custom/mockData';

const BarberCard = ({barber, isSelected, onPress}) => {
  const {getTextColor} = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.barberCard,
        {borderColor: isSelected ? colors.Light_theme_maincolour : colors.grey},
      ]}
      onPress={onPress}>
      <View style={styles.imageContainer}>
        <Image
          source={
            barber.profile_image
              ? {uri: barber.profile_image}
              : globalpath.logo
          }
          style={styles.barberImage}
        />
        {isSelected && (
          <View style={styles.tickContainer}>
            <Icon
              source={globalpath.white_tick}
              size={wp(6)}
              //   tintColor={colors.white}
            />
          </View>
        )}
      </View>
      <ResponsiveText
        size={3.5}
        weight="500"
        color={getTextColor()}
        style={styles.barberName}>
        {barber.full_name}
      </ResponsiveText>
    </TouchableOpacity>
  );
};

const BarberSelection = ({barbersData, onBarberSelect}) => {
  const AppText = useAppText();
  const {getTextColor, getDark_Theme} = useTheme();
  const [selectedBarber, setSelectedBarber] = useState(null);

  const handleBarberSelect = barberId => {
    setSelectedBarber(barberId);
    if (onBarberSelect) {
      const selected = barbersData.find(b => b.id === barberId);
      onBarberSelect(selected); // passing full barber object to parent
    }
  };
  useEffect(() => {
    if (barbersData && barbersData.length > 0) {
      setSelectedBarber(barbersData[0].id);
      if (onBarberSelect) {
        onBarberSelect(barbersData[0]);
      }
    }
  }, [barbersData]);
  const renderItem = ({item}) => (
    <BarberCard
      barber={item}
      isSelected={selectedBarber === item.id}
      onPress={() => handleBarberSelect(item.id)}
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <ResponsiveText
          size={4.2}
          weight="600"
          color={getTextColor()}
          style={styles.title}>
          {AppText.FIRST_AVAILABLE_BARBER}
        </ResponsiveText>
        <View
          style={[styles.borderBottom, {backgroundColor: getDark_Theme()}]}
        />
      </View>
      {barbersData?.length > 0 ? (
        <FlatList
          data={barbersData}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        />
      ) : (
        <View style={styles.noBarbersContainer}>
          <Image
            source={globalpath.no_barber} // <- make sure you have a relevant image in your assets
            style={styles.noBarberImage}
            resizeMode="contain"
          />
          <ResponsiveText size={4} weight="500" color={colors.grey}>
            No barbers found
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

export default BarberSelection;

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(2),
  },
  headerContainer: {
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  title: {
    fontSize: wp(4.8),
    fontWeight: '600',
  },
  borderBottom: {
    height: 1,
    // backgroundColor: colors.grey,
    marginTop: hp(0.5),
  },
  scrollContent: {
    paddingHorizontal: wp(2),
  },
  barberCard: {
    width: wp(25),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  imageContainer: {
    position: 'relative',
    marginBottom: hp(1),
  },
  barberImage: {
    width: wp(18),
    height: wp(18),
    borderRadius: wp(10),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  tickContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: wp(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  barberName: {
    textAlign: 'center',
  },
  noBarbersContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  noBarberImage: {
    opacity: 0.6,
  },
});
