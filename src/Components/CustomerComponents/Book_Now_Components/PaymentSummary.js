import React from 'react';
import { StyleSheet, View } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
const PaymentSummary = ({ selectedServices, appliedCoupon }) => {
  console.log('selectedServices', selectedServices);
  const { getTextColor,getDark_Theme } = useTheme();
  const AppText = useAppText();

  // Calculate total price of all services
  const totalPrice = selectedServices.reduce((sum, service) => sum + parseFloat(service.price), 0);

  // Calculate discount amount if coupon is applied
  const discountPercentage = appliedCoupon ? parseFloat(appliedCoupon) : 0;
  const discountAmount = (totalPrice * discountPercentage) / 100;
  const finalTotal = totalPrice - discountAmount;

  return (
    <View style={[styles.container,{borderColor:getDark_Theme()}]}>
      <ResponsiveText
        color={getTextColor()}
        size={4.2}
        weight="600"
        margin={[hp(2), 0, hp(2), 0]}
        textAlign={'center'}
      >
        {AppText.PAYMENT_SUMMARY}
      </ResponsiveText>

      {/* Services List */}
      <View style={styles.servicesList}>
        {selectedServices.map((service, index) => (
          <View key={index} style={styles.serviceRow}>
            <ResponsiveText
              color={getTextColor()}
              size={3.8}
              weight="500"
            >
              {service.name}
            </ResponsiveText>
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3.8}
              weight="500"
            >
              ${service.price}
            </ResponsiveText>
          </View>
        ))}
      </View>

      {/* Subtotal */}
      <View style={styles.serviceRow}>
        <ResponsiveText
          color={getTextColor()}
          size={3.8}
          weight="500"
        >
          {AppText.SUBTOTAL}
        </ResponsiveText>
        <ResponsiveText
          color={colors.Light_theme_maincolour}
          size={3.8}
          weight="500"
        >
          ${totalPrice.toFixed(2)}
        </ResponsiveText>
      </View>

      {/* Discount if applied */}
      {appliedCoupon && (
        <View style={styles.serviceRow}>
          <ResponsiveText
            color={getTextColor()}
            size={3.8}
            weight="500"
          >
            {AppText.DISCOUNT} ({discountPercentage}%)
          </ResponsiveText>
          <ResponsiveText
            color={colors.red}
            size={3.8}
            weight="500"
          >
            -${discountAmount.toFixed(2)}
          </ResponsiveText>
        </View>
      )}

      {/* Dotted Line */}
      <View style={styles.dottedLine} />

      {/* Final Total */}
      <View style={styles.serviceRow}>
        <ResponsiveText
          color={getTextColor()}
          size={4.2}
          weight="700"
        >
          {AppText.TOTAL}
        </ResponsiveText>
        <ResponsiveText
          color={colors.Light_theme_maincolour}
          size={4.2}
          weight="600"
        >
          ${finalTotal.toFixed(2)}
        </ResponsiveText>
    
      </View>
    </View>
  );
};

export default PaymentSummary;

const styles = StyleSheet.create({
  container: {
    padding: wp(3),
    borderWidth:1,
    borderRadius:wp(1.5),
    marginHorizontal:wp(3),
  
  },
  servicesList: {
    marginBottom: hp(1),
    marginHorizontal:wp(3)
  },
  serviceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
    
  },
  dottedLine: {
    height: 1,
    width: '100%',
    marginVertical: hp(2),
    backgroundColor: colors.grey,
    opacity: 0.5,
  },
}); 