import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'

const DashedLine = ({ color }) => {
  return (
    <View style={styles.container}>
      {[...Array(22)].map((_, index) => (
        <View 
          key={index} 
          style={[
            styles.dash, 
            { backgroundColor: color }
          ]} 
        />
      ))}
    </View>
  )
}

export default DashedLine

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: hp(2),
    width: '100%',
    justifyContent: 'space-between',
  },
  dash: {
    width: wp(3),
    height: 0.4,
    opacity: 0.8,
  },
}) 