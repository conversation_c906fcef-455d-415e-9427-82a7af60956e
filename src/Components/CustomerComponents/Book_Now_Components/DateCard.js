import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const DateCard = ({
  day,
  date,
  isSelected,
  onPress,
  width = wp(25),
}) => {
  const { getDark_Theme, getTextColor ,backgroundColor,} = useTheme();

  const formatDate = (dateString) => {
    const [year, month, day] = dateString.split('-');
    return `${day}-${month}-${year}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          borderColor: isSelected ? colors.Light_theme_maincolour : getDark_Theme(),
          backgroundColor: isSelected ? colors.Light_theme_maincolour : 'transparent',
          width,
        },
      ]}
      onPress={onPress}
    >
      {isSelected && (
        <Icon
          source={globalpath.tick}
          size={wp(4)}
          // tintColor={colors.white}
          style={styles.tickIcon}
        />
      )}
      <ResponsiveText
        color={isSelected ? colors.white : getTextColor()}
        size={4.2}
        weight="600"
      >
        {day}
      </ResponsiveText>
      <ResponsiveText
        color={isSelected ? colors.white : getTextColor()}
        size={3.1}
        weight="500"
        style={styles.dateText}
      >
        {formatDate(date)}
      </ResponsiveText>
    </TouchableOpacity>
  );
};

export default DateCard;

const styles = StyleSheet.create({
  card: {
    height: hp(10),
    borderWidth: 1,
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: wp(1),
    position: 'relative',
    // width:"9%"
  },
  tickIcon: {
    position: 'absolute',
    top: hp(0.5),
    right: wp(1),
  },
  dateText: {
    marginTop: hp(0.5),
  },
}); 