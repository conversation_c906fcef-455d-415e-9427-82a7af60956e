import React, {useEffect, useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Modal} from 'react-native';
import {Calendar} from 'react-native-calendars';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import useAppText from '../../../Custom/AppText';
import {globalpath} from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';
import DateRangeCards from './DateRangeCards';

const DateSelection = ({onMonthSelect, availabilityData, onTimeSlotSelect,onDateSelected}) => {
  console.log('availabilityData', availabilityData);
  const AppText = useAppText();
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);

  const {getTextColor, backgroundColor, getDark_Theme} = useTheme();
  const today = new Date();
  const formattedToday = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  const minDate = today.toISOString().split('T')[0];
  const maxDate = new Date(today.getFullYear(), 11, 31)
    .toISOString()
    .split('T')[0];
  const [selectedMonth, setSelectedMonth] = useState(formattedToday);
  const formatDate = dateString => {
    if (!dateString) return '';
    const [year, month, day] = dateString.split('-');
    return `${day}-${month}-${year}`;
  };
  useEffect(() => {
    console.log('formattedDateToday', formattedToday);
    onMonthSelect(formattedToday);
  }, []);
  useEffect(() => {
    if (selectedTimeSlot) {
      console.log('selectedTimeSlot======', selectedTimeSlot);
      if (onTimeSlotSelect) {
        onTimeSlotSelect({
          selectedTimeSlot,
        });
      }
    }
  }, [selectedTimeSlot]);
  const handleDateSelect = date => {
    setSelectedMonth(date.dateString);
    onMonthSelect(date.dateString);
    setShowCalendar(false);
  };
  useEffect(() => {
    if (selectedDate) {
      console.log('Selected date from DateRangeCards:', selectedDate);
      onDateSelected(selectedDate)
    } else {
      console.log('Selected date from DateRangeCards======:', );
    }
  }, [selectedDate]);
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <ResponsiveText size={4.2} weight="600" color={getTextColor()}>
          {AppText.CHOOSE_DATE}
        </ResponsiveText>

        <TouchableOpacity
          style={styles.dateContainer}
          onPress={() => setShowCalendar(true)}>
          <Icon
            source={globalpath.calendar}
            size={wp(4)}
            tintColor={colors.greyBlack}
          />
          <ResponsiveText
            size={3.2}
            color={colors.black}
            style={styles.dateText}>
            {selectedMonth ? formatDate(selectedMonth) : AppText.SELECT_DATE}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
      <View style={[styles.borderBottom, {backgroundColor: getDark_Theme()}]} />

      {selectedMonth && (
        <DateRangeCards
          selectedDate={selectedMonth}
          availabilityData={availabilityData}
          onTimeSelected={setSelectedTimeSlot}
          onDateSelected={setSelectedDate}
        />
      )}

      <Modal
        visible={showCalendar}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCalendar(false)}>
        <View style={styles.modalContainer}>
          <View style={[styles.calendarContainer, {backgroundColor}]}>
            <Calendar
              onDayPress={handleDateSelect}
              minDate={minDate}
              maxDate={maxDate}
              markedDates={{
                [selectedMonth]: {
                  selected: true,
                  selectedColor: colors.Light_theme_maincolour,
                },
              }}
              theme={{
                backgroundColor: backgroundColor,
                calendarBackground: backgroundColor,
                textSectionTitleColor: getTextColor(),
                selectedDayBackgroundColor: colors.Light_theme_maincolour,
                selectedDayTextColor: colors.white,
                todayTextColor: colors.Light_theme_maincolour,
                dayTextColor: getTextColor(),
                textDisabledColor: colors.grey,
                dotColor: colors.Light_theme_maincolour,
                selectedDotColor: colors.white,
                arrowColor: colors.Light_theme_maincolour,
                monthTextColor: getTextColor(),
                indicatorColor: colors.Light_theme_maincolour,
                textDayFontWeight: '300',
                textMonthFontWeight: 'bold',
                textDayHeaderFontWeight: '300',
                textDayFontSize: 16,
                textMonthFontSize: 16,
                textDayHeaderFontSize: 16,
              }}
            />
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowCalendar(false)}>
              <ResponsiveText size={4} color={colors.white}>
                {AppText.CANCEL}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // marginVertical: hp(2),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(3),
    marginVertical: wp(2.5),
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey1,
    padding: wp(2),
    borderRadius: wp(1.5),
    // flex: 1,
    marginLeft: wp(3),
  },
  dateText: {
    flex: 1,
    marginLeft: wp(2),
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  calendarContainer: {
    borderRadius: wp(3),
    padding: wp(3),
    width: '90%',
  },
  closeButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginTop: hp(2),
  },
  borderBottom: {
    height: 1,
    bottom: wp(0.5),
    marginHorizontal: wp(3),
  },
});

export default DateSelection;
