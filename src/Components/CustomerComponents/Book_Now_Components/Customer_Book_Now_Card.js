import React from 'react';
import { StyleSheet, View, ImageBackground, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';
import { wp, hp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';

const Customer_Book_Now_Card = ({ item, onPress }) => {
  const AppText = useAppText();

  return (
    <View >
      <ImageBackground 
          source={item.shop_image
                      ? { uri: item.shop_image } // Remote image (from API)
                      : globalpath.logo }
          style={styles.salonCard}
          imageStyle={styles.salonCardImage}
        >
        <View style={styles.salonCardOverlay} />
        <View style={styles.salonCardContent}>
          <ResponsiveText weight={'bold'} size={6} color={colors.white} numberOfLines={3} margin={[hp(2),0,0,0]}>
            {item.name}
          </ResponsiveText>

          <View style={{height:hp(7)}}/>
          
          <View style={styles.salonInfoRow}>
            <Icon 
              source={globalpath.location}
              size={wp(4)} 
              tintColor={colors.white}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText color={colors.white} weight={'700'} size={3.6} numberOfLines={1} maxWidth={"90%"}>
              {item.address_city}
            </ResponsiveText>
          </View>
          
          <View style={[styles.salonInfoRow,{justifyContent:"space-between"}]}>
            <View style={{flexDirection:"row",alignItems:"center"}}>
            <Icon 
              source={globalpath.watch}
              size={wp(4)} 
              tintColor={colors.white}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText color={colors.white} weight={'700'} size={3.6} margin={[0,wp(8),0,0]} numberOfLines={1} maxWidth={"78%"}>
            {`${AppText.OPEN_TODAY} : ${
    Array.isArray(item?.working_hours) && item.working_hours.length > 0
      ? item.working_hours[0].open_time
      : ''
  }`}
            </ResponsiveText>
            </View>

            <TouchableOpacity style={styles.bookNowButton}  onPress={onPress} activeOpacity={0.8}>
            <ResponsiveText color={colors.white} weight={'bold'} size={3.5} >
              {AppText.BOOK_NOW}
            </ResponsiveText>
          </TouchableOpacity>
          </View>

       
        </View>
      </ImageBackground>
    </View>
  );
};

export default Customer_Book_Now_Card;

const styles = StyleSheet.create({
  salonCard: {
    width: '100%',
    borderRadius: wp(2),
    overflow: 'hidden',
    marginBottom: hp(2),
  },
  salonCardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  salonCardOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  salonCardContent: {
    padding: wp(4),
    position: 'relative',
  },
  salonInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: wp(2),
  },
  bookNowButton: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: wp(2),
    paddingHorizontal: wp(3),
    borderRadius: wp(1),
    alignSelf: 'flex-start',
    // marginTop: wp(3),
  },
}); 