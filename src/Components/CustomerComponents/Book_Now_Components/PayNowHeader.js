import React from 'react'
import { StyleSheet, View } from 'react-native'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import useTheme from '../../../Redux/useTheme'
import useAppText from '../../../Custom/AppText'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'

const PayNowHeader = () => {
  const { getTextColor } = useTheme()
  const AppText = useAppText()

  return (
    <View style={styles.container}>
      <View style={styles.headerContent}>
      <ResponsiveText 
          size={4.5} 
          weight={'600'} 
          color={colors.c_green}
        >
          {AppText.CHECK_OUT_PAYMENT}
        </ResponsiveText>
        <Icon 
          source={globalpath.coupon} 
          size={wp(6)} 
          tintColor={colors.c_green}
          margin={[0, wp(2), 0, wp(3)]}
        />
       
      </View>
    </View>
  )
}

export default PayNowHeader

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
}) 