import React from 'react';
import {StyleSheet, View} from 'react-native';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import {colors} from '../../../Custom/Colors';
import moment from 'moment';

const InvoiceDateTime = ({date, startTime}) => {
  console.warn('selectedTime==', date, startTime);
  const formattedTime = moment(startTime, 'HH:mm:ss').format('HH:mm');
  const {getTextColor} = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={styles.column}>
          <ResponsiveText size={3.5} weight={'500'} color={getTextColor()}>
            {AppText.DATE}
          </ResponsiveText>
          <ResponsiveText
            size={3.8}
            weight={'600'}
            color={colors.Light_theme_maincolour}
            margin={[hp(0.5), 0, 0, 0]}>
            {date}
          </ResponsiveText>
        </View>
        <View style={styles.column}>
          <ResponsiveText size={3.5} weight={'500'} color={getTextColor()}>
            {AppText.START_TIME}
          </ResponsiveText>
          <ResponsiveText
            size={3.8}
            weight={'600'}
            color={colors.Light_theme_maincolour}
            margin={[hp(0.5), 0, 0, 0]}>
            {formattedTime}
          </ResponsiveText>
        </View>
      </View>
    </View>
  );
};

export default InvoiceDateTime;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  column: {
    flex: 1,
  },
});
