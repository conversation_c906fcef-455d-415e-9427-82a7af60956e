import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { hp, wp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { useNavigation } from '@react-navigation/native';

const Book_Now_Service_Card = ({ item, isSelected, onPress, onSeeDetails, salon }) => {
  const { getDark_Theme, getTextColor, getborderTextColor } = useTheme();
  const AppText = useAppText();
  const navigation = useNavigation();
  console.log("momna khaan",item);

  const handleBuyPress = () => {
    navigation.navigate('Book_Now_Buy', { 
      service: {
        id: item.id,
        name: item.title,
        price: item.amount,
        service_image: item.service_image,
        description: item.description,
        duration: item.duration
      },
      salon: salon 
    });
  };

  return (
    <View
      style={[
        styles.card,
        {
          borderColor:getDark_Theme(),
        },
      ]}
    >
      <Image
        source={item.service_image ? { uri: item.service_image.uri } : globalpath.logo}
        style={styles.image}
      />
      <View style={styles.contentContainer}>
        <View style={styles.titleRow}>
          <ResponsiveText
            color={getTextColor()}
            size={4.3}
            weight="600"
            numberOfLines={1}
             maxWidth={wp(45)}
          >
            {item.title}
          </ResponsiveText>
          <View style={[styles.priceContainer, { backgroundColor: colors.darkGrey }]}>
            <ResponsiveText
              color={colors.white}
              size={3.5}
              weight="500"
            >
              ${item.amount}
            </ResponsiveText>
          </View>
        </View>
        <ResponsiveText
          color={getborderTextColor()}
          size={3.7}
          weight="400"
          margin={[hp(0.5), 0, hp(0.5), 0]}
          numberOfLines={1}
        >
          {item.description}
        </ResponsiveText>
        <View style={styles.bottomRow}>
          <View style={[styles.timeContainer, { backgroundColor: colors.lightGrey6 }]}>
            <Icon source={globalpath.watch} size={wp(3)} tintColor={colors.c_green} />
            <ResponsiveText
              color={colors.c_green}
              size={3}
              weight="500"
              margin={[0, 0, 0, wp(1)]}
            >
              {item.duration}
            </ResponsiveText>
          </View>
          <TouchableOpacity 
            style={[styles.timeContainer]}
            onPress={() => onSeeDetails && onSeeDetails(item)}
          >
            <Icon source={globalpath.eye2} size={wp(4)} tintColor={getborderTextColor()} margin={[0, 0, wp(0.5), wp(0)]} />
            <ResponsiveText
              color={getborderTextColor()}
              size={3}
              weight="500"
              margin={[0, 0, 0, wp(1)]}
            >
              {AppText.SEE_DETAILS}
            </ResponsiveText>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.priceContainer}
            onPress={handleBuyPress}
          >
            <ResponsiveText
              color={colors.white}
              size={4}
              // weight="bold"
              numberOfLines={1}
              maxWidth={wp(15)}
            >
              {AppText.BUY}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default Book_Now_Service_Card;

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
    position: 'relative',
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
  },
  contentContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(0.5),
    borderRadius: wp(1)
  },
  tickIcon: {
    position: 'absolute',
    top: hp(1),
    right: wp(2),
  },
  priceContainer: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.2),
    borderRadius: wp(1)
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
}); 