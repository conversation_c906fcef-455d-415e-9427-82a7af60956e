import React from 'react';
import {StyleSheet, View, Image, ScrollView} from 'react-native';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import {hp, wp} from '../../../Custom/Responsiveness';
import { globalpath } from '../../../Custom/globalpath';

const SelectedServicesDisplay = ({selectedServices}) => {
  const {getTextColor} = useTheme();
  console.log('momnaaaaaa', selectedServices);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {selectedServices.map((service, index) => (
        <View key={index} style={styles.serviceContainer}>
          {/* <Image
            source={
              service.service_image
                ? {uri: service.service_image}
                : globalpath.logo // your fallback image path
            }
            style={styles.serviceImage}
            resizeMode="cover"
          /> */}
                  <Image
          source={
            service.service_image?.uri
              ? {uri: service.service_image.uri}
              : globalpath.logo
          }
          style={styles.serviceImage}
          resizeMode="cover"
        />
          <View style={styles.serviceDetails}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              numberOfLines={2}>
              {service.name}
            </ResponsiveText>
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3.8}
              weight="600"
              margin={[hp(0.5), 0, 0, 0]}>
              ${service.price}
            </ResponsiveText>
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

export default SelectedServicesDisplay;

const styles = StyleSheet.create({
  container: {
    maxHeight: hp(25.5),

    padding: wp(3),
  },
  serviceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
    marginHorizontal: wp(2),
  },
  serviceImage: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
  },
  serviceDetails: {
    flex: 1,
    marginLeft: wp(3),
  },
});
