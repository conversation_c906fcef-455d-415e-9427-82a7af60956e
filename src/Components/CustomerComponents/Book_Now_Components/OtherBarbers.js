import React, { useState } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, Image } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { barbersData } from '../../../Custom/mockData';

const BarberCard = ({ barber, isSelected, onPress }) => {
  const { getTextColor } = useTheme();

  return (
    <TouchableOpacity
      style={[styles.barberCard, { borderColor: isSelected ? colors.Light_theme_maincolour : colors.grey }]}
      onPress={onPress}
    >
      <View style={styles.imageContainer}>
        <Image
          source={barber.image}
          style={styles.barberImage}
        />
        {isSelected && (
          <View style={styles.tickContainer}>
            <Icon
              source={globalpath.white_tick}
              size={wp(6)}
            //   tintColor={colors.white}
            />
          </View>
        )}
      </View>
      <ResponsiveText
        size={3.5}
        weight="500"
        color={getTextColor()}
        style={styles.barberName}
      >
        {barber.name}
      </ResponsiveText>
      
    </TouchableOpacity>
  );
};

const OtherBarbers = () => {
  const AppText = useAppText();
  const { getTextColor, getDark_Theme } = useTheme();
  const [selectedBarber, setSelectedBarber] = useState(barbersData[0].id);

  const handleBarberSelect = (barberId) => {
    setSelectedBarber(barberId);
  };

  const renderItem = ({ item }) => (
    <BarberCard
      barber={item}
      isSelected={selectedBarber === item.id}
      onPress={() => handleBarberSelect(item.id)}
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <ResponsiveText
          size={4.8}
          weight="600"
          color={getTextColor()}
          style={styles.title}
        >
          {AppText.OTHER_BARBERS}
        </ResponsiveText>
        <View style={[styles.borderBottom,{backgroundColor:getDark_Theme()}]} />
      </View>
      
      <FlatList
        data={barbersData}
        renderItem={renderItem}
        keyExtractor={item => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      />
    </View>
  );
};

export default OtherBarbers;

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(2),
  },
  headerContainer: {
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  title: {
    fontSize: wp(4.8),
    fontWeight: '600',
  },
  borderBottom: {
    height: 1,
    // backgroundColor: colors.grey,
    marginTop: hp(1),
  },
  scrollContent: {
    paddingHorizontal: wp(2),
  },
  barberCard: {
    width: wp(25),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  imageContainer: {
    position: 'relative',
    marginBottom: hp(1),
  },
  barberImage: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  tickContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: wp(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  barberName: {
    textAlign: 'center',
  },
}); 