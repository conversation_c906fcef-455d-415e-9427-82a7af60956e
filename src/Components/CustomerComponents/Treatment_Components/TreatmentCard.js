import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { useNavigation } from '@react-navigation/native';
const TreatmentCard = ({ item, onPress }) => {
  const AppText = useAppText();
  const { getDark_Theme, getTextColor } = useTheme();
  const navigation = useNavigation();

  const handlePress = () => {
    navigation.navigate('Treatment_Purchased', { treatment: item });
  };

  return (
    <TouchableOpacity 
      style={[styles.card, { borderColor: getDark_Theme() }]} 
      onPress={handlePress}
    >
      <Image 
        source={item.image} 
        style={styles.cardImage}
      />
      <View style={styles.contentContainer}>
        <View style={styles.titleRow}>
          <ResponsiveText 
            color={getTextColor()} 
            size={4.5} 
            weight="600"
            style={styles.title}
            numberOfLines={1}
            ellipsizeMode="tail"
            margin={[hp(1),0,0,wp(0)]}
          >
            {item.title}
          </ResponsiveText>
          <ResponsiveText 
            color={getTextColor()} 
            size={4.5}
            weight="600"
          >
            ${item.amount}
          </ResponsiveText>
        </View>
        
        <ResponsiveText 
          color={getTextColor()} 
          size={3.5}
          style={styles.description}
          numberOfLines={2}
          ellipsizeMode="tail"
          maxWidth={wp(60)}
          margin={[hp(1),0,0,wp(0)]}
        >
          {item.description}
        </ResponsiveText>

        <View style={styles.durationContainer}>
          <Icon 
            source={globalpath.watch} 
            size={20} 
            tintColor={colors.white} 
            margin={[0,wp(2),0,0]} 
          />
          <ResponsiveText color={colors.white} size={3.5} >
            {item.duration}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: wp(2),
    marginHorizontal: wp(1),
    marginVertical: hp(1),
    overflow: 'hidden',
    paddingHorizontal: wp(4),
    paddingVertical: hp(0.5),
  },
  cardImage: {
    width: '100%',
    height: hp(15),
    resizeMode: 'cover',
    borderRadius: wp(2),
    marginTop: hp(2),
  },
  contentContainer: {
    padding: wp(3),
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  title: {
    flex: 1,
    marginRight: wp(2),
  },
  description: {
    marginBottom: hp(1),
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(1),
    backgroundColor:colors.Light_theme_maincolour,
    alignSelf:"flex-start",
    paddingHorizontal:wp(2.5),
    paddingVertical:wp(1.5),
    borderRadius:wp(1.5)
  },
});

export default TreatmentCard; 