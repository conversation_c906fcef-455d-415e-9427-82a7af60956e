import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const CustomerTreatmentTypeDropdown = ({ onSelect }) => {
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedType, setSelectedType] = useState(null);

  const options = [
    { label: AppText.DIGITAL_VOUCHER, value: 'digital' },
    { label: AppText.GIFT_CARD, value: 'physical' },
    { label: AppText.GIFT_CARD_WITH_BOX, value: 'physical_with_box' }
  ];

  const handleSelect = (type) => {
    setSelectedType(type);
    setShowDropdown(false);
    onSelect(type);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            borderColor: getDark_Theme(),
            backgroundColor: backgroundColor,
          },
        ]}
        onPress={() => setShowDropdown(!showDropdown)}
      >
        <ResponsiveText color={selectedType ? getTextColor() : colors.grey} size={3.8}>
          {selectedType ? options.find(opt => opt.value === selectedType).label : AppText.SELECT}
        </ResponsiveText>
        <Icon
          source={showDropdown ? globalpath.up : globalpath.down}
          size={wp(3.5)}
          tintColor={getTextColor()}
        />
      </TouchableOpacity>

      {showDropdown && (
        <View style={[styles.dropdownContainer, { backgroundColor }]}>
          {options.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.dropdownItem,
                { borderBottomColor: getDark_Theme() }
              ]}
              onPress={() => handleSelect(option.value)}
            >
              <ResponsiveText color={getTextColor()}>{option.label}</ResponsiveText>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

export default CustomerTreatmentTypeDropdown;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
    paddingHorizontal: wp(4),
  },
  dropdownButton: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownContainer: {
    marginTop: hp(0.5),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
    maxHeight: hp(20),
  },
  dropdownItem: {
    padding: wp(4),
    borderBottomWidth: 1,
  },
}); 