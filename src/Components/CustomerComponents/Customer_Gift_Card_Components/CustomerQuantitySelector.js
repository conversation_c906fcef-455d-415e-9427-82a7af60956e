import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';

const CustomerQuantitySelector = ({ amount, onQuantityChange }) => {
  const [quantity, setQuantity] = useState(1);
  const { getTextColor, getDark_Theme} = useTheme();

  const handleIncrement = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    onQuantityChange(newQuantity);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      onQuantityChange(newQuantity);
    }
  };

  const totalAmount = amount * quantity;

  return (
    <View style={styles.container}>
      <View style={styles.quantityContainer}>
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleDecrement}
        >
          <Icon source={globalpath.minus} size={wp(4)} tintColor={colors.grey} />
        </TouchableOpacity>
        <View style={[styles.totalAmount,{ borderColor: getDark_Theme() }]}> 
        <ResponsiveText 
          color={getTextColor()} 
          size={4} 
          weight="600"
          style={styles.quantity}
        >
          {quantity}
        </ResponsiveText>
        </View>
    
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleIncrement}
        >
          <Icon source={globalpath.plus} size={wp(4)} tintColor={colors.Light_theme_maincolour} />
        </TouchableOpacity>
      </View>
      <ResponsiveText 
        color={colors.Light_theme_maincolour} 
        size={4.5} 
        weight="bold"
      >
        ${totalAmount}
      </ResponsiveText>
    </View>
  );
};

export default CustomerQuantitySelector;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: hp(2),
    paddingHorizontal: wp(4),
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantity: {
    marginHorizontal: wp(4),
  },
  totalAmount: {
    // marginHorizontal: wp(1),
    borderWidth: 1,
    padding: wp(2),
    borderRadius: wp(5),
    paddingVertical: hp(2),

  },
}); 