import React, { useState, useRef , useMemo } from 'react';
import { StyleSheet, View, ScrollView, Dimensions, Image } from 'react-native';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
const { width } = Dimensions.get('window');

const Customer_Product_Image_Carousel = ({ images }) => {
  console.log("Customer_Product_Image_Carousel image is ", images);
  const { getDarK_mode_LightGrayBackground, getDark_Theme } = useTheme();
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef(null);


   // ✅ Memoize unique images so no recalculation on every render
    const uniqueImages = useMemo(() => {
      const seen = new Set();
      return images.filter(img => {
        const url = img?.uri || img?.image; // depending on your format
        if (url && !seen.has(url)) {
          seen.add(url);
          return true;
        }
        return false;
      });
    }, [images]);



  const handleScroll = (event) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const offset = event.nativeEvent.contentOffset.x;
    const activeSlide = Math.floor(offset / slideSize);
    setActiveIndex(activeSlide);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.lightGrey }]}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
       {uniqueImages.map((img, index) => (
                 <View key={index} style={styles.imageContainer}>
                   <Image
                     source={
                       img?.image || img?.uri
                         ? { uri: img.image || img.uri }
                         : globalpath.logo
                     }
                     style={styles.image}
                     resizeMode="cover"
                   />
                 </View>
               ))}
      </ScrollView>
      <View style={styles.paginationContainer}>
        {uniqueImages.map((_, index) => (
                 <View
                   key={index}
                   style={[
                     styles.paginationDot,
                     {
                       backgroundColor: index === activeIndex ? colors.Light_theme_maincolour : colors.grey,
                       width: index === activeIndex ? wp(6) : wp(2),
                       borderRadius: index === activeIndex ? wp(2) : wp(1),
                     },
                   ]}
                 />
               ))}
      </View>
    </View>
  );
};

export default Customer_Product_Image_Carousel;

const styles = StyleSheet.create({
  container: {
    height: hp(40),
    borderBottomRightRadius: wp(4),
    borderBottomLeftRadius: wp(4),
    // marginBottom: hp(1),
    bottom: hp(1.5),
  },
  imageContainer: {
    width: width,
    alignItems: "center",
    justifyContent: "center",
    bottom: hp(3)
  },
  image: {
    width: '80%',
    height: '80%',
  },
  paginationContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: hp(2.5),
    alignSelf: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    height: wp(2),
    marginHorizontal: wp(1.5),
  },
}); 