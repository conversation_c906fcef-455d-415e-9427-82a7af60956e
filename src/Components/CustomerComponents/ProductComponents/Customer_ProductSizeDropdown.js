import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, ScrollView, Pressable } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import * as Animatable from 'react-native-animatable';

const Customer_ProductSizeDropdown = ({ product, size, onSelectSize }) => {
  const [showSizeModal, setShowSizeModal] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();

  useEffect(() => {
    if (product?.variants && product.price) {
      const matchingVariant = product.variants.find(
        variant => parseFloat(variant.price_override) === parseFloat(product.price)
      );
      if (matchingVariant) {
        setSelectedVariant(matchingVariant);
        onSelectSize(matchingVariant);
      }
    }
  }, [product]);

  const handleVariantSelect = (variant) => {
    setSelectedVariant(variant);
    onSelectSize(variant);
    setShowSizeModal(false);
  };

  const getVariantDisplayText = (variant) => {
    return `${variant.value} ${variant.name}`;
  };

  return (
    <View style={styles.container}>
      <View style={{flex:1}}>
      <View style={styles.view}>
        <ResponsiveText
          color={getTextColor()}
          size={3.8}
          weight="700"
          margin={[0, wp(2), 0, 0]}
        >
          {AppText.SIZE}
        </ResponsiveText>
        <TouchableOpacity
          style={[
            styles.sizeButton,
            {
              backgroundColor: colors.lightGrey,
              borderColor: getDark_Theme(),
            },
          ]}
          onPress={() => setShowSizeModal(true)}
        >
          <ResponsiveText 
            color={colors.greyBlack} 
            size={3.4} 
            weight="500"
            style={styles.selectedText}
          >
            {selectedVariant ? getVariantDisplayText(selectedVariant) : AppText.SELECT_SIZE}
          </ResponsiveText>
          <Icon
            source={globalpath.down}
            size={wp(3.5)}
            tintColor={colors.greyBlack}
            margin={[0, 0, 0, wp(2)]}
          />
        </TouchableOpacity>
      </View>
      <View style={{flexDirection:"row",alignItems:"center",justifyContent:"space-between", marginTop:hp(3)}}>
      <ResponsiveText color={getTextColor()} size={3.8} weight="bold">
        {AppText.STOCK}
      </ResponsiveText>
      <View style={{backgroundColor:colors.lightGrey,width:wp(28),paddingVertical:hp(1),borderRadius:wp(1.5),borderWidth:1,borderColor:getDark_Theme(),alignItems:"center",justifyContent:"center"}}>
      <ResponsiveText color={getTextColor()} size={3.6} weight="500">
  {selectedVariant?.stock ?? '--'}
</ResponsiveText>
        </View>
        </View>
      </View>
      <Modal
        visible={showSizeModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSizeModal(false)}
      >
        <Pressable 
          style={styles.modalOverlay} 
          onPress={() => setShowSizeModal(false)}
        >
          <Animatable.View 
            animation="slideInUp" 
            duration={300}
            style={[styles.sizeModalContent, { backgroundColor }]}
          >
            <View style={styles.modalHeader}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="600"
              >
                {AppText.SELECT_SIZE}
              </ResponsiveText>
              <TouchableOpacity 
                onPress={() => setShowSizeModal(false)}
                style={styles.closeButton}
              >
                <Icon source={globalpath.cross} size={wp(5.5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>

            <ScrollView 
              style={styles.sizeList}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: hp(10) }}
            >
              {product?.variants?.map((variant) => (
                <Animatable.View
                  key={variant.id}
                  animation="fadeIn"
                  duration={300}
                  delay={200}
                >
                  <TouchableOpacity
                    style={[
                      styles.sizeItem,
                      {
                        backgroundColor: selectedVariant?.id === variant.id ? colors.Light_theme_maincolour : 'transparent',
                        borderColor: selectedVariant?.id === variant.id ? colors.Light_theme_maincolour : getDark_Theme(),
                      },
                    ]}
                    onPress={() => handleVariantSelect(variant)}
                  >
                    <View style={styles.row}>
                      <View style={styles.sizeInfo}>
                        <ResponsiveText
                          size={3.8}
                          color={selectedVariant?.id === variant.id ? colors.white : getTextColor()}
                          style={styles.variantText}
                        >
                          {getVariantDisplayText(variant)}
                        </ResponsiveText>
                        {variant.stock < 10 && (
                          <ResponsiveText
                            size={3}
                            color={colors.red}
                            style={styles.stockText}
                          >
                            Only {variant.stock} left
                          </ResponsiveText>
                         )} 
                      </View>
                      <ResponsiveText
                        size={3.8}
                        color={selectedVariant?.id === variant.id ? colors.white : colors.Light_theme_maincolour}
                        style={styles.priceText}
                      >
                        {/* ₹{variant.price_override} */}
                        {variant.price_override}

                      </ResponsiveText>
                    </View>
                  </TouchableOpacity>
                </Animatable.View>
              ))}
            </ScrollView>
          </Animatable.View>
        </Pressable>
      </Modal>
    </View>
  );
};

export default Customer_ProductSizeDropdown;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  view: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  sizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingHorizontal: wp(5),
    paddingVertical: hp(1.2),
    borderRadius: wp(1.5),
    borderWidth: 1,
    flex: 1,
    maxWidth: wp(28),
    justifyContent:"center",

  },
  selectedText: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  sizeModalContent: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    maxHeight: hp(70),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(3),
    paddingHorizontal: wp(2),
  },
  closeButton: {
    padding: wp(2),
  },
  sizeList: {
    paddingHorizontal: wp(2),
  },
  sizeItem: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(1),
    borderRadius: wp(1.5),
    borderWidth: 1,
    marginBottom: hp(1.5),
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sizeInfo: {
    flex: 1,
  },
  variantText: {
    marginBottom: hp(0.5),
  },
  stockText: {
    marginTop: hp(0.5),
  },
  priceText: {
    fontWeight: 'bold',
  },
}); 