import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { wp, hp } from '../../../Custom/Responsiveness';

const CartBadge = () => {
  const totalItems = useSelector((state) => state.cart.totalItems);

  if (!totalItems || totalItems === 0) return null;

  return (
    <View style={styles.badge}>
      <ResponsiveText color={colors.white} size={3} weight="bold">
        {totalItems}
      </ResponsiveText>
    </View>
  );
};

export default CartBadge;

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: -hp(1),
    right: -wp(2),
    backgroundColor: colors.Light_theme_maincolour,
    borderRadius: wp(3),
    minWidth: wp(5),
    height: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(1),
  },
}); 