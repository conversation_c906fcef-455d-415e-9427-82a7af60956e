import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ActivityIndicator, Alert } from 'react-native';
import { useDispatch } from 'react-redux';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import useAppText from '../../../Custom/AppText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import CartBadge from './CartBadge';
import { setCartItems } from '../../../Redux/Slices/cartSlice';
import { add_to_cart, get_add_to_cart } from '../../../Services/API/Endpoints/Customer/Product';

const CustomerAddToCartButton = ({ product, quantity, selectedVariant, onPress }) => {
  const AppText = useAppText();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      Alert.alert('Error', 'Please select a size first');
      return;
    }

    try {
      setLoading(true);
      
      // Use the selected variant
      const payload = {
        product: product.id,
        variant: selectedVariant.id,
        quantity: quantity
      };

      console.log('Sending add to cart payload:', payload);

      // Make API call to add to cart
      const response = await add_to_cart(payload);
      console.log('Add to cart API response:', response);

      // Fetch latest cart data to get updated count
      const cartResponse = await get_add_to_cart();
      if (cartResponse && cartResponse.length > 0) {
        const cartData = cartResponse[0];
        const totalItems = cartData.items.length;
        dispatch(setCartItems(totalItems));
      }

      if (onPress) onPress();
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert('Error', 'Failed to add item to cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.c_green }]}
      >
        <ResponsiveText color={colors.white} size={4} weight="bold">
          {AppText.BUY_NOW}
        </ResponsiveText>
      </TouchableOpacity> */}
      <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.Light_theme_maincolour }]}
        onPress={handleAddToCart}
        disabled={loading || !selectedVariant}
      >
        {loading ? (
          <ActivityIndicator color={colors.white} size="small" />
        ) : (
          <ResponsiveText color={colors.white} size={4} weight="bold">
            {AppText.ADD_TO_CART}
          </ResponsiveText>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default CustomerAddToCartButton;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: hp(2),
  },
  button: {
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    marginRight: wp(2),
    opacity: 1,
  },
  cartIcon: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    backgroundColor: colors.lightGrey1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 