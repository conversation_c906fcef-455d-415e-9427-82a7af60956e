import React, { useRef, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import Freq_Bought_CustomerProductCard from './Freq_Bought_CustomerProductCard';

const { width } = Dimensions.get('window');
const CARD_WIDTH = wp(35);
const CARD_MARGIN = wp(1);
const CARD_TOTAL_WIDTH = CARD_WIDTH + (CARD_MARGIN * 2);

const RelatedProducts = ({ products, onProductPress }) => {
  const { getTextColor } = useTheme();
  const AppText = useAppText();
  const scrollViewRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleScroll = (event) => {
    const offset = event.nativeEvent.contentOffset.x;
    const index = Math.round(offset / (CARD_TOTAL_WIDTH * 2));
    setCurrentIndex(index);
  };

  const scrollToIndex = (index) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * (CARD_TOTAL_WIDTH * 2),
        animated: true,
      });
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      scrollToIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < Math.ceil(products.length / 2) - 1) {
      scrollToIndex(currentIndex + 1);
    }
  };

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4.2}
        weight="600"
        margin={[0,0,hp(1.5),wp(2)]}
      >
        {AppText.RELATED_PRODUCTS}
      </ResponsiveText>

      <View style={styles.carouselContainer}>
        <TouchableOpacity
          style={[styles.navButton, styles.leftButton]}
          onPress={handlePrev}
          disabled={currentIndex === 0}
        >
          <Icon
            source={globalpath.left}
            size={wp(4)}
            tintColor={currentIndex === 0 ? colors.grey : colors.Light_theme_maincolour}
          />
        </TouchableOpacity>

        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {products.map((product, index) => (
            <Freq_Bought_CustomerProductCard
              key={product.id}
              product={product}
              onPress={() => onProductPress(product)}
            />
          ))}
        </ScrollView>

        <TouchableOpacity
          style={[styles.navButton, styles.rightButton]}
          onPress={handleNext}
          disabled={currentIndex >= Math.ceil(products.length / 2) - 1}
        >
          <Icon
            source={globalpath.goback}
            size={wp(4)}
            tintColor={currentIndex >= Math.ceil(products.length / 2) - 1 ? colors.grey : colors.Light_theme_maincolour}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default RelatedProducts;

const styles = StyleSheet.create({
  container: {
    marginTop: hp(2),
  },
  carouselContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // height: hp(20),
  },
  scrollContent: {
    paddingHorizontal: wp(2),
  },
  navButton: {
    width: wp(7),
    height: wp(7),
    borderRadius: wp(5),
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  leftButton: {
    marginRight: wp(2),
  },
  rightButton: {
    marginLeft: wp(2),
  },
}); 