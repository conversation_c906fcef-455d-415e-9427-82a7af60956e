import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';

const Cart_CustomerProductQuantity = ({ price, onQuantityChange, initialQuantity = 1 , onRemove}) => {
  const [quantity, setQuantity] = useState(initialQuantity);
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  useEffect(() => {
    setQuantity(initialQuantity);
  }, [initialQuantity]);

  const handleIncrement = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    onQuantityChange(newQuantity, true);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      onQuantityChange(newQuantity, false);
    }
  };

  // Calculate total based on current price and quantity
  const totalAmount = parseFloat(price) * quantity;

  return (
    <View style={styles.container}>
      <View style={styles.quantityContainer}>
        <View>
        <ResponsiveText
          color={getTextColor()}
          size={3.4}
          weight="700"
          margin={[0, wp(2), 0, 0]}
        >
          {AppText.QUANTITY}
        </ResponsiveText>
        </View>
        <View style={{flexDirection:'row', alignItems:"center"}}>

    
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleDecrement}
        >
          <Icon source={globalpath.minus} size={wp(3.5)} tintColor={colors.grey} />
        </TouchableOpacity>
        <View style={[styles.quantityBox, { borderColor: getDark_Theme() }]}>
          <ResponsiveText 
            color={getTextColor()} 
            size={4} 
            weight="600"
          >
            {quantity}
          </ResponsiveText>
        </View>
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleIncrement}
        >
          <Icon source={globalpath.plus} size={wp(3.5)} tintColor={colors.grey} />
        </TouchableOpacity>
        </View>
        {/* <ResponsiveText 
          color={colors.Light_theme_maincolour} 
          size={4.5} 
          weight="bold"
          // numberOfLines={1}
          // maxWidth={wp(20)}
          margin={[0,0,0,wp(9)]}
        >
          ${totalAmount.toFixed(2)}
        </ResponsiveText> */}
            {/* <TouchableOpacity
                      style={styles.removeButton}
                      onPress={onRemove}
                    >
                      <Icon source={globalpath.delete_icon} 
                      size={wp(5)} 
                      tintColor={colors.red}
                      // margin={[0,0,0,wp(13)]}
                      />
                    </TouchableOpacity> */}
      </View>

    </View>
  );
};

export default Cart_CustomerProductQuantity;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginVertical: hp(2),
    // alignSelf: 'flex-end',

  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:'space-between',
    flex:1
  },
  button: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityBox: {
    borderWidth: 1,
    paddingHorizontal: wp(4),
    borderRadius: wp(1.5),
    paddingVertical: hp(1),
    marginHorizontal: wp(2),
  },
  // removeButton: {
  //  backgroundColor:colors.lightGrey,
  //  marginHorizontal:wp(10),
  //  paddingHorizontal:wp(2),
  //  paddingVertical:hp(1),
  //  borderRadius:wp(7),
  // },
}); 