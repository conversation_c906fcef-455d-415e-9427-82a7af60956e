import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';
import { wp } from '../../../Custom/Responsiveness';

const CustomerProductFilter = ({ onPress }) => {
  return (
    <TouchableOpacity
      style={[styles.filterButton, { backgroundColor: colors.Light_theme_maincolour }]}
      onPress={onPress}
    >
      <Icon source={globalpath.filter} size={wp(4.2)} tintColor={colors.white} />
    </TouchableOpacity>
  );
};

export default CustomerProductFilter;

const styles = StyleSheet.create({
  filterButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
    width: wp(12),
    height: wp(12),
  },
}); 