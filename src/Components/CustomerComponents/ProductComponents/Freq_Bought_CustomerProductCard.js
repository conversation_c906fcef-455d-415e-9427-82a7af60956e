import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
const Freq_Bought_CustomerProductCard = ({ product, onPress }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const navigation = useNavigation();

  const handlePress = () => {
    // navigation.navigate('Add_To_Cart_Customer', { product });

  };

  return (
    <TouchableOpacity
      style={[styles.container, { borderColor: getDark_Theme() }]}
      onPress={onPress}
    >
      <Image
        source={product.image}
        style={styles.image}
        resizeMode="stretch"
      />
      <View style={styles.contentContainer}>
        <ResponsiveText
          color={getTextColor()}
          size={3.2}
          weight="500"
          margin={[0, 0, hp(0.3), 0]}
          numberOfLines={2}
          textAlign={"center"}
        >
          {product.title}
        </ResponsiveText>
        <ResponsiveText
          color={colors.Light_theme_maincolour}
          size={3.4}
          weight="bold"
          margin={[0, 0, hp(0.8), 0]}
          textAlign={"center"}
        >
          ${product.price}
        </ResponsiveText>
        <TouchableOpacity
          style={[styles.viewDetailsButton, { backgroundColor: colors.Light_theme_maincolour }]}
          onPress={handlePress}
        >
          <ResponsiveText color={colors.white} size={3.2} weight="500">
            {AppText.SEE_DETAILS}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default Freq_Bought_CustomerProductCard;

const styles = StyleSheet.create({
  container: {
    width: wp(35),
    borderWidth: 1,
    borderRadius: wp(2),
    overflow: 'hidden',
    margin: wp(1),
  },
  image: {
    width: '100%',
    height: hp(11),
    // height: hp(12),
    // padding:wp(4)
  },
  contentContainer: {
    padding: wp(2.5),
  },
  viewDetailsButton: {
    paddingVertical: hp(0.8),
    borderRadius: wp(1.5),
    alignItems: 'center',
  },
}); 