import React from 'react';
import { StyleSheet, View, TextInput } from 'react-native';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { wp ,hp} from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';

const CustomerProductSearch = ({ searchText, setSearchText }) => {
  const { getDark_Theme, getTextColor } = useTheme();

  return (
    <View style={[styles.searchContainer, { borderColor: getDark_Theme() }]}>
      <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0, wp(2), 0, 0]} />
      <TextInput
        style={[styles.searchInput, { color: getTextColor() }]}
        placeholder="Search Products..."
        placeholderTextColor={colors.grey}
        value={searchText}
        onChangeText={setSearchText}
      />
    </View>
  );
};

const styles = StyleSheet.create({
 searchContainer: {
    flex: 1,
    height: hp(5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    marginRight: wp(2),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
});

export default CustomerProductSearch; 