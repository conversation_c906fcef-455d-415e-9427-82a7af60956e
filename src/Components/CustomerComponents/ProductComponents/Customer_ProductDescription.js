import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const Customer_ProductDescription = ({ description }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { getDark_Theme, getTextColor } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <ResponsiveText
          color={getTextColor()}
          size={4}
          weight="bold"
        >
          {AppText.PRODUCT_DETAILS}
        </ResponsiveText>
        <Icon
          source={isExpanded ? globalpath.down : globalpath.up}
          size={wp(4)}
          tintColor={getTextColor()}
          margin={[0,wp(2),0,0]}
        />
      </TouchableOpacity>
      {isExpanded && (
        <View style={styles.descriptionContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={3.5}
            weight="400"
          >
            {description}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

export default Customer_ProductDescription;

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
    marginBottom: hp(2),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  descriptionContainer: {
    marginTop: hp(1),
    padding: wp(2),
    backgroundColor: colors.lightGrey,
    borderRadius: wp(2),
  },
}); 