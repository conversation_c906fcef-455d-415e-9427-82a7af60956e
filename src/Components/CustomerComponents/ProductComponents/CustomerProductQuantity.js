import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';

const CustomerProductQuantity = ({ price, onQuantityChange, initialQuantity = 1 }) => {
  const [quantity, setQuantity] = useState(initialQuantity);
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  useEffect(() => {
    setQuantity(initialQuantity);
  }, [initialQuantity]);

  const handleIncrement = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    onQuantityChange(newQuantity);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      onQuantityChange(newQuantity);
    }
  };

  const totalAmount = price * quantity;

  return (
    <View style={styles.container}>
      <View style={styles.quantityContainer}>
        <ResponsiveText
          color={getTextColor()}
          size={3.4}
          weight="700"
          margin={[0, wp(2), 0, 0]}
        >
          {AppText.QUANTITY}
        </ResponsiveText>
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleDecrement}
        >
          <Icon source={globalpath.minus} size={wp(4)} tintColor={colors.grey} />
        </TouchableOpacity>
        <View style={[styles.quantityBox, { borderColor: getDark_Theme() }]}>
          <ResponsiveText 
            color={getTextColor()} 
            size={4} 
            weight="600"
          >
            {quantity}
          </ResponsiveText>
        </View>
        <TouchableOpacity 
          style={[styles.button, { borderColor: colors.Light_theme_maincolour }]} 
          onPress={handleIncrement}
        >
          <Icon source={globalpath.plus} size={wp(4)} tintColor={colors.Light_theme_maincolour} />
        </TouchableOpacity>
      </View>
      <ResponsiveText 
        color={colors.Light_theme_maincolour} 
        size={4.5} 
        weight="bold"
        numberOfLines={1}
        maxWidth={wp(20)}
      >
        ${totalAmount}
      </ResponsiveText>
    </View>
  );
};

export default CustomerProductQuantity;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: hp(2),
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityBox: {
    borderWidth: 1,
    paddingHorizontal: wp(4),
    borderRadius: wp(1.5),
    paddingVertical: hp(1),
    marginHorizontal: wp(2),
  },
}); 