import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';

const CustomerProductFavorite = () => {
  const [isFavorite, setIsFavorite] = useState(false);
  const { getTextColor } = useTheme();

  const handleFavoritePress = () => {
    setIsFavorite(!isFavorite);
  };

  return (
    <TouchableOpacity 
      style={styles.favoriteButton} 
      onPress={handleFavoritePress}
    >
      <Icon 
        source={isFavorite ? globalpath.filled_heart : globalpath.heart} 
        size={wp(6)} 
        tintColor={ isFavorite ? colors.red : getTextColor()} 
        margin={[0,wp(1),wp(4),wp(0)]}
      />
    </TouchableOpacity>
  );
};

export default CustomerProductFavorite;

const styles = StyleSheet.create({
  favoriteButton: {
    padding: wp(2.5),
    borderRadius: wp(6)
  },
}); 