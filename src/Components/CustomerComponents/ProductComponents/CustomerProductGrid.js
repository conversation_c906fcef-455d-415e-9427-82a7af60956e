import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, ImageBackground, FlatList, RefreshControl } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
import { globalpath } from '../../../Custom/globalpath';
import { list_products_by_category } from '../../../Services/API/Endpoints/Customer/Product';

const CustomerProductGrid = ({ data, onProductPress,onRefresh }) => {
  const navigation = useNavigation();
  const [refreshing,setRefreshing]=useState(false)
  // const [subCategoryProduct,SetsubCategoryProduct]=useState([])


  // const onRefresh = () => {
  //   setRefreshing(true);
  //   if (data)
  //   {
  //     setRefreshing(false)
  //   }
   
  // };


  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();  // ✅ Call the refresh function from parent
    setRefreshing(false);
  };

  const handleProductPress = async (item) => {
    console.log('the sending Item id for the product subCategry is ======', item.id);
    try {
      const response = await list_products_by_category(item.id);
      console.log('Fetched products:', response.data);
  
      navigation.navigate('Products_Details', {
        product: response, // <- use the fresh API response directly
      });
  
      console.log("Navigated with product data ✔️✔️✔️");
  
    } catch (error) {
      console.error('API error:', error);
    }
  };
  const renderProductItem = ({ item }) => (

    
    
    <TouchableOpacity
      style={styles.productCard}
      activeOpacity={0.9}
      onPress={() => handleProductPress(item)}
      >
      <ImageBackground
      source={
        item.category_image
          ? { uri: item.category_image }
          : globalpath.logo // fallback image
      } style={styles.backgroundImage}
      >
        <View style={styles.titleContainer}>
          <ResponsiveText 
          color={colors.white} 
          textAlign="center"
           weight={'500'} 
           size={4.5}
           numberOfLines={1}
           >{item.name}</ResponsiveText>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
       <FlatList
        data={data}
        renderItem={renderProductItem}
        keyExtractor={item => item.id.toString() 
          
        }
        numColumns={2}
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        onRefresh={onRefresh}
        refreshControl={
          <RefreshControl
           refreshing={refreshing} 
           onRefresh={handleRefresh}  
            colors={[colors.Light_theme_maincolour]}
             tintColor={colors.Light_theme_maincolour} />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: wp(2),
  },
  productCard: {
    width: '48%',
    height: hp(20),
    margin: '1%',
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
  },
  titleContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: wp(2),
  },
});

export default CustomerProductGrid; 