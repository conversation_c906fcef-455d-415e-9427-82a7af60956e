import React from 'react';
import { View, TouchableOpacity, StatusBar, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useTheme from '../../Redux/useTheme';
import CartBadge from './ProductComponents/CartBadge';

const Customer_Header = ({ 
  title, 
  onPress, 
  onCartPress,
  showBellIcon = true,
  showCartIcon = false,
  leftIconType = 'bars' // 'bars' or 'back'
}) => {
  const navigation = useNavigation();
  const { getTextColor, getLightGrayBackground, getBellBackground, getborderTextColor, getDarK_mode_LightGrayBackground, getDark_Theme, backgroundColor, isDarkTheme } = useTheme();

  const handleLeftIconPress = () => {
    if (leftIconType === 'back') {
      navigation.goBack();
    } else {
      navigation.openDrawer();
    }
  };

  const handleCartPress = () => {
    navigation.navigate('Main_Add_Cart');
  };

  const getLeftIconSource = () => {
    return leftIconType === 'back' ? globalpath.goback : globalpath.bars;
  };

  return (
    <View>
      <StatusBar backgroundColor={backgroundColor} barStyle={isDarkTheme() ? "light-content" : "dark-content"} />
      
      <View style={[styles.headerContainer]}>
        <View style={styles.leftIconContainer}>
          {/* <TouchableOpacity 
          onPress={handleLeftIconPress}
          > */}
          <TouchableOpacity onPress={leftIconType === 'back' && onPress ? onPress : handleLeftIconPress}>

            <Icon 
              source={getLeftIconSource()} 
              size={ leftIconType === 'back' ? wp(4.5) : wp(5.5)} 
              margin={[0, 0, 0, wp(5)]} 
              tintColor={getTextColor()} 
            />
          </TouchableOpacity>
        </View>

        <View style={styles.titleContainer}>
          <ResponsiveText size={4.7} weight={'600'} color={getTextColor()} style={styles.title} >
            {title}
          </ResponsiveText>
        </View>

        <View style={styles.rightIconContainer}>
          {showCartIcon && (
            <TouchableOpacity
              style={[styles.iconContainer, { backgroundColor: getBellBackground() }]}
              onPress={handleCartPress}
            >
              <Icon source={globalpath.Add_Cart} size={wp(6.5)} tintColor={getTextColor()} />
              <CartBadge />
            </TouchableOpacity>
          )}
          
          {showBellIcon ? (
            <TouchableOpacity
              style={[styles.iconContainer, { backgroundColor: getBellBackground() }]}
              onPress={onPress}
            >
              <Icon source={globalpath.bell} size={wp(6.5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          ) : (
            <View style={styles.placeholder} />
          )}
        </View>
      </View>
      <View style={{borderBottomWidth:1, borderColor: getDark_Theme(), marginVertical: wp(2.5)}}/>
    </View>
  );
};

export default Customer_Header;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp(1),
    justifyContent: 'space-between',
    marginBottom: wp(1),
  },
  leftIconContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    textAlign: 'center',
  },
  rightIconContainer: {
    flex: 1,
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    borderRadius: wp(10),
    padding: wp(1.8),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(3),
  },
  placeholder: {
    width: wp(10),
  },
});