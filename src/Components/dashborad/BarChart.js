import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BarChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { colors } from '../../Custom/Colors';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';

const CustomBarChart = ({ data }) => {
  const { getTextColor, getChartColour } = useTheme();
  const screenWidth = Dimensions.get('window').width;

  const chartConfig = {
    backgroundColor: getChartColour(),
    backgroundGradientFrom: getChartColour(),
    backgroundGradientTo: getChartColour(),
    decimalPlaces: 0,
    color: (opacity = 1) => colors.chart_color,
    labelColor: (opacity = 1) => getTextColor(),
    fillShadowGradientFrom: colors.chart_color, // Added to match line chart
    fillShadowGradientTo: colors.chart_color2,  // Added to match line chart
    fillShadowGradientFromOpacity: 0.7,         // Added to match line chart
    fillShadowGradientToOpacity: 0.3,           // Added to match line chart
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeWidth: 0, // Hide grid lines to match line chart
    },
  };

  return (
    <View style={styles.container}>
      <BarChart
        data={data}
        width={screenWidth - wp(10)}
        height={hp(25)} // Match line chart height
        chartConfig={chartConfig}
        style={styles.chart}
        showBarTops={false}
        fromZero
        withHorizontalLabels={true}
        withVerticalLabels={true}
        withHorizontalLines={false} // Match line chart
        withVerticalLines={false}    // Match line chart
        withShadow={true}           // Match line chart
        flatColor={true}            // This helps with gradient colors
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1),
  },
  chart: {
    marginVertical: hp(1),
    borderRadius: wp(2),
  },
});

export default CustomBarChart;