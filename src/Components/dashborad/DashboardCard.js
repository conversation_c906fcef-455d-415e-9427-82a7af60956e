import React from 'react';
import {StyleSheet, View, TouchableOpacity} from 'react-native';
import {colors} from '../../Custom/Colors';
import {wp, hp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import useTheme from '../../Redux/useTheme';
const DashboardCard = ({
  icon,
  value,
  label,
  onPress,
  style,
  iconStyle,
  valueStyle,
  labelStyle,
}) => {
  const {
    getIconColour,
    backgroundColor,
    getTextColor,
    getSecondaryTextColor,
    getDarK_mode_LightGrayBackground,
    getborderTextColor,
    getDark_Theme,
  } = useTheme();

  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: getDarK_mode_LightGrayBackground(),
          borderColor: getDark_Theme(),
        },
        style,
      ]}
      onPress={onPress}>
      <View style={styles.iconContainer}>
        <Icon
          source={icon}
          size={wp(5)}
          style={iconStyle}
          tintColor={getIconColour()}
        />
      </View>
      <View style={styles.contentContainer}>
        <ResponsiveText
          size={5.5}
          weight="bold"
          color={colors.c_green}
          style={valueStyle}
          margin={[0, 0, wp(3), 0]}
          textAlign={'center'}>
          {value}
        </ResponsiveText>
        <ResponsiveText
          x
          size={3.6}
          color={getborderTextColor()}
          style={labelStyle}
          textAlign={'center'}
          //   weight={'600'}
        >
          {label}
        </ResponsiveText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flex: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(3),
    paddingVertical: wp(1.7),
    margin: wp(2),
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 0.5},
    shadowOpacity: 0.2,
    shadowRadius: 1,
    borderWidth: 1,
  },
  iconContainer: {
    position: 'absolute',
    top: wp(3),
    left: wp(3),
    // right: wp(3),
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor:"pink"
  },
});

export default DashboardCard;
