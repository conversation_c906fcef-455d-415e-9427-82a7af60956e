import React from 'react';
import { View, StyleSheet } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { colors } from '../../Custom/Colors';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';

const CustomLineChart = ({ data }) => {
  const { getTextColor, getChartColour } = useTheme();
  const screenWidth = Dimensions.get('window').width;

  const chartConfig = {
    backgroundColor: getChartColour(),
    backgroundGradientFrom: getChartColour(),
    backgroundGradientTo: getChartColour(),
    decimalPlaces: 0,
    color: (opacity = 1) => colors.chart_color,
    labelColor: (opacity = 1) => getTextColor(),
    fillShadowGradientFrom: colors.chart_color, // Start color of the fill gradient
    fillShadowGradientTo: colors.chart_color2,   // End color of the fill gradient
    fillShadowGradientFromOpacity: 0.7, // Adjust opacity as needed
    fillShadowGradientToOpacity: 0.3,   // Adjust opacity as needed
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeWidth: 0, // Hide grid lines
    },
  };

  return (
    <View style={styles.container}>
      <LineChart
        data={data}
        width={screenWidth - wp(10)}
        height={hp(25)}
        chartConfig={chartConfig}
        bezier
        withHorizontalLines={false}
        withVerticalLines={false}
        withShadow={true} // Required for the fill to show
        style={styles.chart}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1),
  },
  chart: {
    marginVertical: hp(1),
    borderRadius: wp(2),
  },
});

export default CustomLineChart;