import React, { useState  , useEffect} from 'react';
import { View, TouchableOpacity, Image, ScrollView, StyleSheet } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { colors } from '../../Custom/Colors';
import { wp, hp } from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';

import { delete_product_variant_image } from '../../Services/API/Endpoints/Admin/products';

const VariantImagePicker = ({ onSelectImages, variantId, initialImages = [] }) => {
  
  const [images, setImages] = useState([]);
  const { getTextColor, getDark_Theme, backgroundColor } = useTheme();
  const AppText = useAppText();
    
  
    useEffect(() => {
      if (Array.isArray(initialImages) && initialImages.length > 0) {
        setImages(initialImages);
      }
    }, [initialImages]);


    const removeImage = async (image , index) => {
      console.log("img---",image);
      const updated = [...images];
      const imageToDelete = updated[index];
    
      // Check if it's an existing uploaded image with an ID and remote URI
      if (
        imageToDelete?.id &&
        typeof imageToDelete.uri === 'string' &&
        imageToDelete.uri.startsWith('https')
      ) {
        try {
          await delete_product_variant_image(imageToDelete.id);
          console.log(`🧼 Deleted image ID: ${imageToDelete.id} from server`);
        } catch (error) {
          console.error('❌ Failed to delete image from API:', error);
          Alert.alert('Error', 'Failed to delete image from server.');
          return; // exit early to prevent local deletion if API fails
        }
      }
    
      // Remove from local state after successful (or skipped) API deletion
      updated.splice(index, 1);
      setImages(updated);
      onSelectImages && onSelectImages(variantId, updated);
    };

    


  
    const pickImages = () => {
      launchImageLibrary({ mediaType: 'photo', selectionLimit: 0 }, (response) => {
        if (response.didCancel || response.errorCode) return;
        const newImages = response.assets.map(img => ({
          uri: img.uri,
          fileName: img.fileName || img.uri.split('/').pop(),
          image: undefined,
        }));
        const updated = [...images, ...newImages];
        setImages(updated);
        onSelectImages && onSelectImages(variantId, updated);
      });
    };
  
   

  return (
    <View style={[styles.container, { borderColor: getDark_Theme() }]}>
      <View style={styles.headerRow}>
        <ResponsiveText color={colors.black} size={4.5} weight="bold">
        {AppText.VARINAT_IMAGES}
        </ResponsiveText>
        <TouchableOpacity onPress={pickImages}>
          <Icon source={globalpath.plus} size={wp(5.5)} tintColor={colors.c_green} />
        </TouchableOpacity>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.imageScroll}>
        {images.map((img, idx) => (
          <View key={idx} style={styles.imageWrapper}>
            <Image source={{ uri: img.uri }} style={styles.image} />
            <TouchableOpacity style={styles.removeIcon} onPress={() => removeImage(img , idx)}>
              <ResponsiveText color={colors.white} size={3.5} weight="bold">
                −
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default VariantImagePicker;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginTop: hp(2),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  imageScroll: {
    flexDirection: 'row',
  },
  imageWrapper: {
    position: 'relative',
    marginRight: wp(3),
    width: wp(25),
    height: wp(25),
    borderRadius: wp(2),
    overflow: 'visible', // 👈 CHANGE from 'hidden' to 'visible'
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: '#f8f8f8',
    marginTop:hp(1.1)
  },
  
  image: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
  },
  
  removeIcon: {
    position: 'absolute',
    top: -wp(3), // 👈 Move the button above the image a bit
    right: -wp(3), // 👈 Move slightly outside right
    backgroundColor: colors.red,
    width: wp(7),
    height: wp(7),
    borderRadius: wp(3.5),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    borderWidth: 2,
    borderColor: '#fff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
});
