import React, { useState , useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, TextInput, Modal, ScrollView , Alert } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import CustomCheckbox from '../../Custom/CustomCheckbox';
import VariantImagePicker from './VariantImagePicker';
import { delete_product_variant_image } from '../../Services/API/Endpoints/Admin/products';



const ProductVariants = ({ selectedVariants, isEdit ,  onVariantSelect , onMatchedVariant,    onVariantImagesChange , 

  initialVariantImagesMap = {} // ✅ new prop for initial images
 }) => {
  const { getTextColor, getDark_Theme, backgroundColor } = useTheme();
  const AppText = useAppText();
  const [isAddingVariant, setIsAddingVariant] = useState(false);
  const [volumeValue, setVolumeValue] = useState('');
  const [volumeUnit, setVolumeUnit] = useState('ml');
  const [variants, setVariants] = useState(() => {
    if (isEdit) {
      return selectedVariants.map(v => ({
        unit: v.name,
        value: v.value,
        price_override: v.price_override || '',
        stock: v.stock || '',
      }));
    } else {
      return [
        { unit: 'ml', value: '250', price_override: '', stock: '' },
        { unit: 'ml', value: '500', price_override: '', stock: '' },
        { unit: 'ml', value: '1000', price_override: '', stock: '' },
      ];
    }
  });


  const [variantImagesMap, setVariantImagesMap] = useState(initialVariantImagesMap);

  console.log("selectedVariants--------", selectedVariants)


  useEffect(() => {
    if (isEdit) {
      // ✅ When editing, use only selectedVariants
      const transformedVariants = selectedVariants.map(v => ({
        unit: v.name,
        value: v.value,
        price_override: v.price_override || '',
        stock: v.stock || '',
      }));
      setVariants(transformedVariants);
    } 
  }, [isEdit, selectedVariants]);



  // ✅ new - handle image change by selected reindex
  const handleImageChange = (variantValue, images) => {
    const selectedIndex = selectedVariants.findIndex(v => v.name === variantValue.unit && v.value === variantValue.value);

    if (selectedIndex !== -1) {
      const updatedMap = { ...variantImagesMap, [`variant_${selectedIndex}`]: images };
      setVariantImagesMap(updatedMap);
      onVariantImagesChange && onVariantImagesChange(updatedMap);
    } else {
      console.warn(`⚠️ Variant not found in selectedVariants for`, variantValue);
    }
  };

  const pickVariantImages = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      allowsMultipleSelection: true,
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
    });

    if (!result.canceled) {
      const newImages = result.assets.map(asset => ({ uri: asset.uri }));
      setVariantImages(prev => [...prev, ...newImages]);
    }
  };

  const removeImage = (index) => {
    setVariantImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddVariant = () => {
    if (volumeValue.trim()) {
      const numericValue = parseFloat(volumeValue);
      if (!isNaN(numericValue) && numericValue > 0) {
        const exists = variants.some(v => v.unit === volumeUnit && v.value === volumeValue);
        if (!exists) {
          setVariants(prev => [
            ...prev,
            {
              unit: volumeUnit,
              value: volumeValue,
              price_override: '',
              stock: '',
            }
          ]);
        }
        setVolumeValue('');
        setIsAddingVariant(false);
      }
    }
  };

  const presetVolumes = [
    { value: '100', unit: 'ml' },
    { value: '200', unit: 'ml' },
    { value: '300', unit: 'ml' },
    { value: '400', unit: 'ml' },
    { value: '500', unit: 'ml' },
    { value: '750', unit: 'ml' },
    { value: '1', unit: 'L' },
    { value: '1.5', unit: 'L' },
    { value: '2', unit: 'L' },
  ];

  const handlePresetSelect = (value, unit) => {
    setVolumeValue(value);
    setVolumeUnit(unit);
  };

  const isVariantSelected = (variant) => {
    return selectedVariants.some(v => v.name === variant.unit && v.value === variant.value);
  };


  const handleVariantToggle = (variant, selected) => {
    if (selected) {
      if (isEdit) {
        // Show Alert if Edit mode
        Alert.alert(
          'Confirmation',
          'Are you sure you want to uncheck? This will delete your variant entries and images from the server!',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Yes, Uncheck',
              onPress: async () => {
                await unselectVariantAndDeleteImages(variant);
              },
            },
          ],
          { cancelable: true }
        );
      } else {
        unselectVariantAndDeleteImages(variant);
      }
    } else {
      // ➕ Add New Variant
      onVariantSelect(prev => [
        ...prev,
        {
          name: variant.unit,
          value: variant.value,
          price_override: '',
          stock: '',
        }
      ]);
    }
  };
  
  const unselectVariantAndDeleteImages = async (variant) => {
    console.log("🔍 Unchecking Variant: ", variant);
  
    // 🛠 Find the FULL variant from selectedVariants
    const matchedVariant = selectedVariants.find(
      v => v.name === variant.unit && v.value === variant.value
    );
  
    console.log("The matched variants is ", matchedVariant);
  
    // 🚀 Send to parent
    if (onMatchedVariant && matchedVariant?.id) {
      console.log("🚀 Sending matchedVariant to Parent...");
      onMatchedVariant(matchedVariant);
    }
  
    if (!matchedVariant) {
      console.warn(`⚠️ No matching full variant found for: ${variant.value} ${variant.unit}`);
      return;
    }
  
    // 🖼️ Deleting Images
    if (matchedVariant.variant_images && matchedVariant.variant_images.length > 0) {
      console.log(`📦 Found variant_images to delete:`, matchedVariant.variant_images);
  
      for (const img of matchedVariant.variant_images) {
        const imageUrl = img?.uri || img?.image;
  
        if (matchedVariant?.id && img?.id && imageUrl?.startsWith('https')) {
          try {
            console.log(`📤 Deleting image from API (ID: ${img.id})...`);
            await delete_product_variant_image(img.id);
          } catch (error) {
            console.error(`❌ Failed to delete image ID: ${img.id}`, error);
            Alert.alert('Error', `Failed to delete image from server. Image ID: ${img.id}`);
          }
        } else {
          console.warn('⚠️ Skipped API delete (local image):', img);
        }
      }
    } else {
      console.log(`ℹ️ No images found inside matched full variant`);
    }
  
    // 🧹 Now, also clear variantImagesMap for this variant!
    const selectedIndex = selectedVariants.findIndex(
      v => v.name === variant.unit && v.value === variant.value
    );
  
    if (selectedIndex !== -1) {
      const updatedMap = { ...variantImagesMap };
      delete updatedMap[`variant_${selectedIndex}`]; // 🧹 Clear the images of this variant
      setVariantImagesMap(updatedMap);
      onVariantImagesChange && onVariantImagesChange(updatedMap);
      console.log(`🧹 Cleared images in variantImagesMap for variant_${selectedIndex}`);
    } else {
      console.warn(`⚠️ Variant not found in selectedVariants for clearing images`, variant);
    }
  
    // 🛠 Finally, remove the variant from selectedVariants
    onVariantSelect(prev => {
      const updated = prev.filter(
        v => !(v.name === variant.unit && v.value === variant.value)
      );
      console.log('✅ Updated selectedVariants after deletion:', updated);
      return updated;
    });
  };
  
  
  
  
  
  
  
  
  
  

  return (
    <View style={[styles.container, { borderColor: getDark_Theme() }]}>
      <View style={styles.varientView}>
        <ResponsiveText
          color={getTextColor()}
          size={4.6}
          weight="bold"
          margin={[hp(1), 0, hp(1), 0]}
        >
          {AppText.PRODUCT_VARIANTS}
        </ResponsiveText>
        <TouchableOpacity style={styles.addVariant} onPress={() => setIsAddingVariant(true)}>
          <ResponsiveText color={colors.white} size={3} weight="bold">
            {AppText.ADD_VARIANT}
          </ResponsiveText>
        </TouchableOpacity>
      </View>

      <View style={styles.variantsContainer}>
      {variants.map((variant, index) => {
          const selected = isVariantSelected(variant);
          const selectedData = selectedVariants.find(
            v => v.name === variant.unit && v.value === variant.value
          );

          const selectedIndex = selectedVariants.findIndex(
            v => v.name === variant.unit && v.value === variant.value
          );

          return (
            <View key={index} style={{ marginBottom: hp(2) }}>
              <TouchableOpacity
                style={[
                  styles.variantItem,
                  { borderColor: selected ? colors.c_green : getDark_Theme() },
                ]}
                onPress={() => handleVariantToggle(variant, selected)}
              >
                <CustomCheckbox
                value={selected}
                onValueChange={() => handleVariantToggle(variant, selected)}
                onTintColor={colors.Light_theme_maincolour}
                onCheckColor={colors.white}
              />
                <ResponsiveText
                  color={getTextColor()}
                  size={3.8}
                  weight="500"
                  margin={[0, 0, 0, wp(2)]}
                >
                  {variant.value} {variant.unit}
                </ResponsiveText>
              </TouchableOpacity>

              {selected && (
                <View style={{ marginTop: hp(1), marginLeft: wp(10) }}>
                  <TextInput
                    placeholder={AppText.ENTER_PRODUCT_VARIANT_PRICE}
                    keyboardType="numeric"
                    value={selectedData?.price_override}
                    onChangeText={text => onVariantSelect(prev => prev.map(v =>
                      v.name === variant.unit && v.value === variant.value
                        ? { ...v, price_override: text }
                        : v
                    ))}
                    style={[styles.volumeInput, { marginBottom: hp(1) }]}
                    placeholderTextColor={colors.grey}
                  />
                  <TextInput
                    placeholder={AppText.ENTER_PRODUCT_VARIANT_STOCK}
                    keyboardType="numeric"
                    value={selectedData?.stock?.toString()}
                    onChangeText={text => onVariantSelect(prev => prev.map(v =>
                      v.name === variant.unit && v.value === variant.value
                        ? { ...v, stock: text }
                        : v
                    ))}
                    style={styles.volumeInput}
                    placeholderTextColor={colors.grey}
                  />

                  {/* 🔥 Pass variant object instead of index */}
                  <VariantImagePicker
                    variantId={{ unit: variant.unit, value: variant.value }}
                    initialImages={initialVariantImagesMap?.[`variant_${selectedIndex}`] || []}
                    onSelectImages={handleImageChange}
                  />
                </View>
              )}
            </View>
          );
        })}
      </View>

      <Modal
        visible={isAddingVariant}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsAddingVariant(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor, borderColor: getDark_Theme(), borderWidth: 1.5 }]}>
            <ResponsiveText
              color={getTextColor()}
              size={4.6}
              weight="bold"
              margin={[hp(2), 0, hp(2), 0]}
            >
              {AppText.ADD_VOLUME_VARIANT}
            </ResponsiveText>

            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.presetButtons}>
                {presetVolumes.map((preset, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.presetButton,
                      volumeValue === preset.value && volumeUnit === preset.unit && styles.selectedPreset
                    ]}
                    onPress={() => handlePresetSelect(preset.value, preset.unit)}
                  >
                    <ResponsiveText
                      color={getTextColor()}
                      size={3.2}
                      weight="500"
                    >
                      {preset.value} {preset.unit}
                    </ResponsiveText>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <View style={styles.volumeInputContainer}>
              <TextInput
                style={[styles.volumeInput, { color: getTextColor(), borderColor: getDark_Theme() }]}
                value={volumeValue}
                onChangeText={setVolumeValue}
                placeholder={AppText.ENTER_VALUE}
                placeholderTextColor={colors.grey}
                keyboardType="numeric"
              />
              <View style={styles.unitSelector}>
                {['ml', 'L'].map(unit => (
                  <TouchableOpacity
                    key={unit}
                    style={[
                      styles.unitButton,
                      volumeUnit === unit && styles.selectedUnit
                    ]}
                    onPress={() => setVolumeUnit(unit)}
                  >
                    <ResponsiveText
                      color={volumeUnit === unit ? colors.white : getTextColor()}
                      size={3.2}
                      weight="500"
                    >
                      {unit}
                    </ResponsiveText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity style={[styles.modalButton, { backgroundColor: getDark_Theme() }]} onPress={() => setIsAddingVariant(false)}>
                <ResponsiveText color={colors.white} size={3.5} weight="bold">
                  {AppText.CANCEL}
                </ResponsiveText>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.modalButton, styles.saveButton]} onPress={handleAddVariant}>
                <ResponsiveText color={colors.white} size={3.5} weight="bold">
                  {AppText.ADD_VARIANT_BUTTON}
                </ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ProductVariants;

const styles = StyleSheet.create({
  container: {
    borderRadius: wp(2),
    padding: wp(4),
    borderWidth: 1,
    marginTop: hp(1),
  },
  variantsContainer: {
    marginTop: hp(1),
  },
  variantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  varientView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addVariant: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(2.5),
    borderRadius: wp(1.5),
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    borderRadius: wp(2),
    padding: wp(4),
  },
  presetButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: hp(2),
  },
  presetButton: {
    padding: wp(2),
    borderRadius: wp(1.5),
    borderWidth: 1,
    borderColor: colors.Light_theme_maincolour,
    marginRight: wp(2),
    marginBottom: hp(1),
  },
  selectedPreset: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  volumeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  volumeInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: wp(1.5),
    padding: wp(2),
    fontSize: 16,
    marginRight: wp(2),
  },
  unitSelector: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(1.5),
    borderColor: colors.Light_theme_maincolour,
    overflow: 'hidden',
  },
  unitButton: {
    padding: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedUnit: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  modalButton: {
    padding: wp(2.5),
    borderRadius: wp(1.5),
    width: '45%',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },

  uploadButton: {
    marginTop: hp(1),
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
  },
  imageWrapper: {
    position: 'relative',
    marginRight: wp(2),
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
  },
  removeBtn: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: colors.red,
    borderRadius: wp(2),
    width: wp(5),
    height: wp(5),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
