import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import Product_Preview_Modal from './Product_Preview_Modal';
import { delete_product_image } from '../../Services/API/Endpoints/Admin/products';

const Product_Upload_Image = ({ onImageSelect, initialImage, from }) => {
  console.log("The Initials images is ", initialImage)
  const { getDark_Theme, getTextColor, backgroundColor, getDarK_mode_LightGrayBackground } = useTheme();
  const AppText = useAppText();
  const [selectedImage, setSelectedImage] = useState(null); // array or object
  const [showPreview, setShowPreview] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    if (!hasInitialized && initialImage) {
      if (Array.isArray(initialImage)) {
        const formatted = initialImage.map(img => ({
          uri: img.uri || img.image,
          image: img.image,
          fileName: img.image?.split('/').pop() || 'existing.jpg',
          id: img.id // ✅ KEEP the id also
        }));
        setSelectedImage(formatted);
        onImageSelect && onImageSelect(formatted);
      } else {
        const singleImage = {
          uri: initialImage.uri || initialImage.image,
          fileName: initialImage.image?.split('/').pop() || 'existing.jpg',
        };
        setSelectedImage(singleImage);
        onImageSelect && onImageSelect(singleImage);
      }
      setHasInitialized(true); // 👈 prevents re-trigger
    }
  }, [initialImage]);
  

  const handleImagePicker = () => {
    const options = {
      mediaType: 'photo',
      includeBase64: true,
      selectionLimit: from === 'products' ? 0 : 1,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel || response.errorCode) return;

      if (from === 'products') {
        const images = response.assets.map(image => ({
          ...image,
          size: (image.fileSize / (1024 * 1024)).toFixed(2),
          dimensions: `${image.width}x${image.height}`,
        }));
        const allImages = [...(selectedImage || []), ...images];
        setSelectedImage(allImages);
        onImageSelect && onImageSelect(allImages);
      } else {
        const image = response.assets[0];
        const imageDetails = {
          ...image,
          size: (image.fileSize / (1024 * 1024)).toFixed(2),
          dimensions: `${image.width}x${image.height}`,
        };
        setSelectedImage(imageDetails);
        onImageSelect && onImageSelect(imageDetails);
      }
    });
  };

  const handleRemoveImage = async (index) => {
    const updatedImages = [...selectedImage];
    const imageToDelete = updatedImages[index];
  
    if (
      imageToDelete?.id &&
      typeof imageToDelete.uri === 'string' &&
      imageToDelete.uri.startsWith('https')
    ) {
      try {
        await delete_product_image(imageToDelete.id);
        console.log(`🧼 Deleted image ID: ${imageToDelete.id} from server`);
      } catch (error) {
        console.error('❌ Failed to delete image from API:', error);
        Alert.alert('Error', 'Failed to delete image from server.');
        return; // 🛑 Exit early if API deletion fails
      }
    }
  
    // ✅ Now remove locally
    updatedImages.splice(index, 1);
    setSelectedImage(updatedImages.length ? updatedImages : null);
    onImageSelect && onImageSelect(updatedImages.length ? updatedImages : null);
  };

  const handleDeleteImage = () => {
    setSelectedImage(null);
    onImageSelect && onImageSelect(null);
  };

  const renderImages = () => {
    if (Array.isArray(selectedImage)) {
      return (
        <ScrollView
          horizontal
          contentContainerStyle={styles.imageScrollContainer}
          showsHorizontalScrollIndicator={false}
        >
          {selectedImage.map((img, idx) => (
            <View key={idx} style={styles.outerContainer}>
              <View style={styles.multiImageContainer}>
                <TouchableOpacity
                  style={styles.imageWrapperMulti}
                  onPress={() => setShowPreview(true)}
                >
                  <Image
                    source={{ uri: img.uri }}
                    style={styles.previewImage}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              </View>
              {from === 'products' && (
                <TouchableOpacity
                  style={styles.perImageDelete}
                  onPress={() => handleRemoveImage(idx)}
                >
                  <ResponsiveText color={colors.white} size={3.5} weight="bold">
                    –
                  </ResponsiveText>
                </TouchableOpacity>
              )}
            </View>
          ))}
        </ScrollView>
      );
    }
  
    return (
      <View style={styles.imageContainer}>
        <TouchableOpacity
          style={styles.imageWrapper}
          onPress={() => setShowPreview(true)}
        >
          <Image
            source={{ uri: selectedImage.uri }}
            style={styles.previewImage}
            resizeMode="cover"
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDeleteImage}
        >
          {from === 'products' ? (
            <ResponsiveText color={colors.white} size={4} weight="bold">
              –
            </ResponsiveText>
          ) : (
            <Icon
              source={globalpath.delete_icon}
              size={wp(5)}
              tintColor={colors.white}
            />
          )}
        </TouchableOpacity>
      </View>
    );
  };
  
  

  return (
    <View style={[styles.container, { borderColor: getDark_Theme(), backgroundColor }]}>
      <View style={styles.headerRow}>
        <ResponsiveText color={getTextColor()} size={4.6} weight="bold">
          {AppText.UPLOAD_PRODUCT_IMAGE}
        </ResponsiveText>
        {from === 'products' && Array.isArray(selectedImage) && selectedImage.length > 0 && (
    <TouchableOpacity onPress={handleImagePicker}>
      <Icon source={globalpath.plus} size={wp(5)} tintColor={colors.c_green} />
    </TouchableOpacity>
  )}
      </View>

      {selectedImage && (Array.isArray(selectedImage) ? selectedImage.length > 0 : true) ? (
          renderImages()
        ) : (
          <TouchableOpacity
            style={[
              styles.uploadContainer,
              { borderColor: colors.c_green, backgroundColor: getDarK_mode_LightGrayBackground() },
            ]}
            onPress={handleImagePicker}
          >
            <Icon source={globalpath.upload} style={styles.uploadIcon} resizeMode="contain" />
          </TouchableOpacity>
        )}

      <Product_Preview_Modal
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        imageUri={
          Array.isArray(selectedImage)
            ? selectedImage[0]?.uri
            : selectedImage?.uri
        }
      />
    </View>
  );
};

export default Product_Upload_Image;

const styles = StyleSheet.create({
  container: {
    borderRadius: wp(2),
    padding: wp(4),
    borderWidth: 1,
    marginTop: hp(1),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  uploadContainer: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: wp(2),
    padding: wp(4),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: hp(15),
  },
  imageScrollContainer: {
    paddingTop: hp(2.5), // Space for the minus icon to appear above image
  },
  outerContainer: {
    marginRight: wp(3),
    alignItems: 'center',
    justifyContent: 'flex-end',
    position: 'relative',
    // paddingTop: hp(1.5), // Shift image down a bit so icon fits above it
  },
  multiImageContainer: {
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: '#f8f8f8',
    width: wp(30),
    height: wp(30),
    overflow: 'hidden',
  },
  
  imageWrapperMulti: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
    overflow: 'hidden',
  
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: hp(20),
    marginTop: hp(1),
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: wp(2),
    overflow: 'hidden',
    backgroundColor: '#f8f8f8',
  },
  imageWrapper: {
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  previewImage: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
  },
  deleteButton: {
    position: 'absolute',
    top: wp(2),
    right: wp(2),
    backgroundColor: colors.red,
    borderRadius: wp(4),
    padding: wp(2),
    zIndex: 99,
  },
  perImageDelete: {
    position: 'absolute',
    top: 0,
    right: 0,
    transform: [{ translateX: wp(2.5) }, { translateY: -wp(2.5) }],
    backgroundColor: colors.red,
    width: wp(6.5),
    height: wp(6.5),
    borderRadius: wp(6.5) / 2,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    borderWidth: 2,
    borderColor: '#fff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  uploadIcon: {
    width: wp(58),
    height: wp(11),
  },
});
