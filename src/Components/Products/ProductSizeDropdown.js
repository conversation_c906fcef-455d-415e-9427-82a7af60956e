import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, TextInput, ScrollView } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const ProductSizeDropdown = ({ size, onSelectSize }) => {
  const [showSizeModal, setShowSizeModal] = useState(false);
  const [isAddingSize, setIsAddingSize] = useState(false);
  const [volumeValue, setVolumeValue] = useState('');
  const [volumeUnit, setVolumeUnit] = useState('ml');
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const AppText = useAppText();

  const [sizes, setSizes] = useState([
    { label: '250ml', value: '250ml' },
    { label: '100ml', value: '100ml' },
    { label: '1L', value: '1L' },
  ]);

  const presetVolumes = [
    { value: '100', unit: 'ml' },
    { value: '200', unit: 'ml' },
    { value: '300', unit: 'ml' },
    { value: '400', unit: 'ml' },
    { value: '500', unit: 'ml' },
    { value: '750', unit: 'ml' },
    { value: '1', unit: 'L' },
    { value: '1.5', unit: 'L' },
    { value: '2', unit: 'L' },
  ];

  const handleAddSize = () => {
    if (volumeValue.trim()) {
      const numericValue = parseFloat(volumeValue);
      if (!isNaN(numericValue) && numericValue > 0) {
        let id, label;
        
        if (volumeUnit === 'ml') {
          id = `${numericValue}ml`;
          label = `${numericValue} ml`;
        } else {
          id = `${numericValue}L`;
          label = `${numericValue} L`;
        }
        
        // Check if size already exists
        const sizeExists = sizes.some(s => s.value === id);
        
        if (!sizeExists) {
          const newSize = { id, label, value: id };
          setSizes([...sizes, newSize]);
          setVolumeValue('');
          setIsAddingSize(false);
        }
      }
    }
  };

  const handlePresetSelect = (value, unit) => {
    setVolumeValue(value);
    setVolumeUnit(unit);
  };

  const selectedSize = sizes.find(s => s.value === size);

  return (
    <View style={styles.container}>
      <View style={styles.view}>
        <ResponsiveText
          color={getTextColor()}
          size={3.4}
          weight="700"
          margin={[0, wp(2), 0, 0]}
        >
          {AppText.SIZE}
        </ResponsiveText>
        <TouchableOpacity
          style={[
            styles.sizeButton,
            {
              backgroundColor: getDark_Theme(),
              borderColor: getDark_Theme(),
            },
          ]}
          onPress={() => setShowSizeModal(true)}
        >
          <ResponsiveText color={getTextColor()} size={3.4} weight="500">
            {selectedSize ? selectedSize.label : AppText.SELECT_SIZE}
          </ResponsiveText>
          <Icon
            source={globalpath.down}
            size={wp(3.5)}
            tintColor={getTextColor()}
            margin={[0, 0, 0, wp(2)]}
          />
        </TouchableOpacity>
      </View>

      {/* Size Selection Modal */}
      <Modal
        visible={showSizeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSizeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.sizeModalContent, { backgroundColor: backgroundColor }]}>
            <View style={styles.modalHeader}>
              <ResponsiveText
                color={getTextColor()}
                size={4.6}
                weight="bold"
              >
                {AppText.SIZE}
              </ResponsiveText>
              <TouchableOpacity onPress={() => setShowSizeModal(false)}>
                <Icon source={globalpath.close} size={wp(5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.sizeList}>
              {sizes.map((item) => (
                <TouchableOpacity
                  key={item.value}
                  style={[
                    styles.sizeItem,
                    { 
                      backgroundColor: item.value === size ? colors.Light_theme_maincolour : 'transparent',
                      borderColor: getDark_Theme()
                    }
                  ]}
                  onPress={() => {
                    onSelectSize(item.value);
                    setShowSizeModal(false);
                  }}
                >
                  <ResponsiveText 
                    color={item.value === size ? colors.white : getTextColor()} 
                    size={3.8} 
                    weight="500"
                  >
                    {item.label}
                  </ResponsiveText>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, {backgroundColor: getDark_Theme()}]}
                onPress={() => setShowSizeModal(false)}
              >
                <ResponsiveText
                  color={colors.white}
                  size={3.5}
                  weight="bold"
                >
                  {AppText.CANCEL}
                </ResponsiveText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.saveButton]}
                onPress={() => {
                  setShowSizeModal(false);
                  setIsAddingSize(true);
                }}
              >
                <ResponsiveText
                  color={colors.white}
                  size={3.5}
                  weight="bold"
                >
                  {AppText.ADD_VARIANT}
                </ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Size Modal */}
      <Modal
        visible={isAddingSize}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setIsAddingSize(false);
          setShowSizeModal(true);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: backgroundColor, borderColor: getDark_Theme(), borderWidth: 1.5 }]}>
            <View style={styles.modalHeader}>
              <ResponsiveText
                color={getTextColor()}
                size={4.6}
                weight="bold"
              >
                {AppText.ADD_VOLUME_VARIANT}
              </ResponsiveText>
              <TouchableOpacity onPress={() => {
                setIsAddingSize(false);
                setShowSizeModal(true);
              }}>
                <Icon source={globalpath.close} size={wp(5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.presetContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={3.5}
                weight="500"
                margin={[0, 0, hp(1), 0]}
              >
                {AppText.COMMON_VOLUMES}
              </ResponsiveText>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.presetButtons}>
                  {presetVolumes.map((preset, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.presetButton,
                        volumeValue === preset.value && volumeUnit === preset.unit && styles.selectedPreset
                      ]}
                      onPress={() => handlePresetSelect(preset.value, preset.unit)}
                    >
                      <ResponsiveText
                        color={getTextColor()}
                        size={3.2}
                        weight="500"
                      >
                        {preset.value} {preset.unit}
                      </ResponsiveText>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
            
            <View style={styles.inputContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={3.5}
                weight="500"
                margin={[0, 0, hp(1), 0]}
              >
                {AppText.CUSTOM_VOLUME}
              </ResponsiveText>
              <View style={styles.volumeInputContainer}>
                <TextInput
                  style={[styles.volumeInput, { 
                    color: getTextColor(),
                    borderColor: getDark_Theme()
                  }]}
                  value={volumeValue}
                  onChangeText={setVolumeValue}
                  placeholder={AppText.ENTER_VALUE}
                  placeholderTextColor={getTextColor()}
                  keyboardType="numeric"
                />
                <View style={styles.unitSelector}>
                  <TouchableOpacity 
                    style={[
                      styles.unitButton, 
                      volumeUnit === 'ml' && styles.selectedUnit
                    ]}
                    onPress={() => setVolumeUnit('ml')}
                  >
                    <ResponsiveText
                      color={volumeUnit === 'ml' ? colors.white : getTextColor()}
                      size={3.2}
                      weight="500"
                    >
                      ml
                    </ResponsiveText>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={[
                      styles.unitButton, 
                      volumeUnit === 'L' && styles.selectedUnit
                    ]}
                    onPress={() => setVolumeUnit('L')}
                  >
                    <ResponsiveText
                      color={volumeUnit === 'L' ? colors.white : getTextColor()}
                      size={3.2}
                      weight="500"
                    >
                      L
                    </ResponsiveText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, {backgroundColor: getDark_Theme()}]}
                onPress={() => {
                  setIsAddingSize(false);
                  setShowSizeModal(true);
                }}
              >
                <ResponsiveText
                  color={colors.white}
                  size={3.5}
                  weight="bold"
                >
                  {AppText.CANCEL}
                </ResponsiveText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.saveButton]}
                onPress={() => {
                  handleAddSize();
                  setShowSizeModal(true);
                }}
              >
                <ResponsiveText
                  color={colors.white}
                  size={3.5}
                  weight="bold"
                >
                  {AppText.ADD_VARIANT_BUTTON}
                </ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ProductSizeDropdown;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: 'red',
    flex: 1,
  },
  view: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex:1
  },
  sizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
    borderRadius: wp(2),
    borderWidth: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sizeModalContent: {
    width: '90%',
    maxHeight: hp(60),
    borderRadius: wp(2),
    padding: wp(4),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  sizeList: {
    maxHeight: hp(40),
    marginBottom: hp(2),
  },
  sizeItem: {
    padding: wp(4),
    borderRadius: wp(2),
    borderWidth: 1,
    marginBottom: hp(1),
  },
  addButton: {
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    borderRadius: wp(2),
    padding: wp(4),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  presetContainer: {
    marginBottom: hp(2),
  },
  presetButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: hp(1),
  },
  presetButton: {
    padding: wp(2),
    borderRadius: wp(1.5),
    borderWidth: 1,
    borderColor: colors.Light_theme_maincolour,
    marginRight: wp(2),
    marginBottom: hp(1),
  },
  selectedPreset: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  inputContainer: {
    marginBottom: hp(2),
  },
  volumeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  volumeInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: wp(1.5),
    padding: wp(2),
    fontSize: 16,
    marginRight: wp(2),
  },
  unitSelector: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(1.5),
    borderColor: colors.Light_theme_maincolour,
    overflow: 'hidden',
  },
  unitButton: {
    padding: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedUnit: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  modalButton: {
    padding: wp(2.5),
    borderRadius: wp(1.5),
    width: '45%',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});   