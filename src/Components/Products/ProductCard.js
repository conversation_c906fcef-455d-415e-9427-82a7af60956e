import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
import { globalpath } from '../../Custom/globalpath';

const ProductCard = ({ product, images ,  onPress }) => {
  console.log("product in the images is ", images)
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const navigation = useNavigation();
  const handlePress = () => {
    navigation.navigate('View_Product_Details', { product });
  };

  return (
    <View
      style={[styles.container, { borderColor: getDark_Theme() }]}
    >
      <Image
          source={
            Array.isArray(images) && images.length > 0 && images[0].image
              ? { uri: images[0].image }
              : globalpath.logo
          }
          style={styles.image}
          resizeMode="cover"
        />
      <View style={styles.contentContainer}>
        <ResponsiveText
          color={getTextColor()}
          size={3.4}
          weight="500"
          margin={[0, 0, hp(0.5), 0]}
          numberOfLines={2}
        >
          {product.name}
        </ResponsiveText>
        <ResponsiveText
          color={colors.Light_theme_maincolour}
          size={3.6}
          weight="bold"
          margin={[0, 0, hp(1), 0]}
        >
          ${product.price}
        </ResponsiveText>
        <View style={{}}>
        <TouchableOpacity
          style={[styles.viewDetailsButton, { backgroundColor: colors.Light_theme_maincolour }]}
          onPress={handlePress}
        >
          <ResponsiveText color={colors.white} size={3.2} weight="500">
            {AppText.SEE_DETAILS}
          </ResponsiveText>
        </TouchableOpacity>
        </View>
 
      </View>
    </View>
  );
};

export default ProductCard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderWidth: 1,
    borderRadius: wp(2),
    overflow: 'hidden',
    margin: wp(1),
    // backgroundColor:"pink",
    justifyContent: 'space-between' // This will push the button to the bottom

  },
  image: {
    width: '100%',
    height: hp(17),
  },
  contentContainer: {
    padding: wp(3),
  },
  viewDetailsButton: {
    paddingVertical: hp(1),
    borderRadius: wp(1.5),
    alignItems: 'center',
  },
}); 