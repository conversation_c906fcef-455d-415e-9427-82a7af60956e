import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import CustomTextInput from '../CustomTextInput';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Product_Upload_Image from './Product_Upload_Image';
import { add_product_category, update_list_product_category, delete_list_product_category } from '../../Services/API/Endpoints/Customer/Product';
import * as ImagePicker from 'react-native-image-picker';

const Add_Product_Modal = ({ visible, onClose, onSave, productToEdit = null }) => {
  const [categoryName, setCategoryName] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [loading, setLoading] = useState(false);

  const AppText = useAppText();
  const { getDark_Theme, backgroundColor, getTextColor } = useTheme();

  useEffect(() => {
    if (visible) {
      if (productToEdit) {
        setCategoryName(productToEdit.name);
        setSelectedImage(productToEdit.category_image ? { uri: productToEdit.category_image } : null);
      } else {
        setCategoryName('');
        setSelectedImage(null);
      }
    }
  }, [visible, productToEdit]);

  const handleSave = async () => {
    if (!categoryName.trim()) {
      Alert.alert('Validation Error', 'Please enter a category name.');
      return;
    }

    if (!selectedImage?.uri && !productToEdit) {
      Alert.alert('Validation Error', 'Please select an image.');
      return;
    }

    setLoading(true);
    try {
      const form = new FormData();
      form.append('name', categoryName.trim());

      if (selectedImage?.uri) {
        form.append('category_image', {
          uri: selectedImage.uri,
          name: selectedImage.fileName || 'category_image.jpg',
          type: 'image/jpeg',
        });
      }

      if (productToEdit) {
        await update_list_product_category(productToEdit.id, form);
        Alert.alert('Success', 'Category updated successfully!');
      } else {
        await add_product_category(form);
        Alert.alert('Success', 'Category added successfully!');
      }

      onSave();
      setCategoryName('');
      setSelectedImage(null);
      onClose();

    } catch (error) {
      console.error('❌ Error:', error);
      Alert.alert('Error', 'Operation failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!productToEdit) return;

    Alert.alert(
      'Delete Category',
      'Are you sure you want to delete this category?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              await delete_list_product_category(productToEdit.id);
              Alert.alert('Success', 'Category deleted successfully!');
              onSave();
              onClose();
            } catch (error) {
              console.error('❌ Error deleting category:', error);
              Alert.alert('Error', 'Failed to delete category.');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
    );
  };

  const handleImageSelect = (imageDetails) => {
    setSelectedImage(imageDetails);
  };

  const handleEditImagePick = () => {
    ImagePicker.launchImageLibrary({
      mediaType: 'photo',
      quality: 0.8,
    }, (response) => {
      if (response.didCancel) return;
      
      if (response.assets && response.assets[0]) {
        const imageDetails = {
          uri: response.assets[0].uri,
          fileName: response.assets[0].fileName || 'image.jpg',
          type: response.assets[0].type || 'image/jpeg',
          size: response.assets[0].fileSize,
        };
        setSelectedImage(imageDetails);
      }
    });
  };

  // Render edit mode UI
  if (productToEdit) {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="slide"
        onRequestClose={onClose}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : ''}
          style={{flex:1}}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, {backgroundColor}]}>
              <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />
              
              <View style={styles.editHeader}>
                <ResponsiveText size={5} weight="bold" color={getTextColor()}>
                  {AppText.EDIT_CATEGORY}
                </ResponsiveText>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Icon source={globalpath.cross} size={wp(5)} tintColor={getTextColor()} />
                </TouchableOpacity>
              </View>

              <CustomTextInput
                label={AppText.CATEGORY_NAME}
                placeholder={AppText.ENTER_YOUR_CATEGORY_NAME}
                value={categoryName}
                onChangeText={setCategoryName}
              />

              <View style={styles.editImageContainer}>
                <Image 
                  source={selectedImage ? { uri: selectedImage.uri } : { uri: productToEdit.category_image }}
                  style={styles.editImage}
                />
                <TouchableOpacity 
                  style={styles.changeImageButton}
                  onPress={handleEditImagePick}
                >
                  <Icon source={globalpath.edit} size={wp(4.5)} tintColor={colors.white} />
                </TouchableOpacity>
              </View>

              <View style={styles.editButtonsContainer}>
                <TouchableOpacity
                  style={[styles.updateButton]}
                  disabled={loading}
                  onPress={handleSave}
                >
                  {loading ? (
                    <ActivityIndicator color={colors.white} />
                  ) : (
                    <>
                      <Icon source={globalpath.check} size={wp(5)} tintColor={colors.white} margin={[0, wp(2), 0, 0]} />
                      <ResponsiveText color={colors.white} size={4}>
                        {AppText.UPDATE}
                      </ResponsiveText>
                    </>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.deleteIconButton}
                  disabled={loading}
                  onPress={handleDelete}
                >
                  <Icon source={globalpath.delete_icon} size={wp(6)} tintColor={colors.red} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }

  // Render original add mode UI
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{ flex: 1 }}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />

            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Icon source={globalpath.cross} size={wp(5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>

            <CustomTextInput
              label={AppText.CATEGORY_NAME}
              placeholder={AppText.ENTER_YOUR_CATEGORY_NAME}
              value={categoryName}
              onChangeText={setCategoryName}
            />

            <Product_Upload_Image onImageSelect={handleImageSelect} />

            <TouchableOpacity
              style={[
                styles.saveButton,
                {
                  backgroundColor:
                    categoryName.trim() && selectedImage?.uri
                      ? colors.Light_theme_maincolour
                      : colors.grey,
                  opacity: categoryName.trim() && selectedImage?.uri ? 1 : 0.5,
                },
              ]}
              disabled={!categoryName.trim() || !selectedImage?.uri || loading}
              onPress={handleSave}
            >
              {loading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                  {AppText.SAVE}
                </ResponsiveText>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default Add_Product_Modal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(5),
    paddingBottom: hp(3),
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  modalHeader: {
    flexDirection: 'row',
    marginBottom: hp(1),
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
  },
  closeButton: {
    padding: wp(2),
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  saveButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(2),
    marginBottom: hp(2),
  },
  imageContainer: {
    height: hp(20),
    borderRadius: wp(2),
    marginBottom: hp(2),
    overflow: 'hidden',
    backgroundColor: colors.lightGrey,
  },
  selectedImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imagePlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(1),
    borderWidth: 1,
    borderColor: colors.red,
  },
  editHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
    paddingHorizontal: wp(-1),
    marginTop: hp(2),
  },
  editImageContainer: {
    height: hp(25),
    width: '100%',
    borderRadius: wp(2),
    marginBottom: hp(3),
    position: 'relative',
    backgroundColor: colors.lightGrey,
  },
  editImage: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2),
    resizeMode: 'cover',
  },
  changeImageButton: {
    position: 'absolute',
    bottom: wp(3),
    right: wp(3),
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(2.5),
    borderRadius: wp(6),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  editButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(3),
    marginBottom: hp(3),
    paddingHorizontal: wp(2),
  },
  updateButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: colors.Light_theme_maincolour,
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(3),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 3,
  },
  deleteIconButton: {
    backgroundColor: colors.white,
    padding: wp(2.5),
    borderRadius: wp(6),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.red,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
});
