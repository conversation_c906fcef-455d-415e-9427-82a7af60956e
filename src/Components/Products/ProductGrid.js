import React from 'react';
import { StyleSheet, View, TouchableOpacity, ImageBackground, FlatList } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
import { globalpath } from '../../Custom/globalpath';
import Icon from '../../Custom/Icon';

const ProductGrid = ({ data, onProductPress, refreshing, onRefresh, onEditPress }) => {
  const navigation = useNavigation();

  const renderProductItem = ({ item }) => (
    <View style={styles.productCard}>
      <TouchableOpacity
        style={styles.dotsButton}
        onPress={() => onEditPress(item)}
      >
        <Icon source={globalpath.dots} size={wp(5)} tintColor={colors.white} />
      </TouchableOpacity>
      
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => navigation.navigate('Products_Details', { product_category: item })}
        style={styles.cardContent}
      >
        <ImageBackground
          source={
            item.category_image
              ? { uri: item.category_image }
              : globalpath.logo
          }
          style={styles.backgroundImage}
        >
          <View style={styles.titleContainer}>
            <ResponsiveText color={colors.white} textAlign="center" weight={'500'} size={4.5} numberOfLines={1}>
              {item.name}
            </ResponsiveText>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyComponent = () => (
    <View style={styles.productCard}>
      <ImageBackground
        source={globalpath.logo}
        style={styles.backgroundImage}
      >
        <View style={styles.titleContainer}>
          <ResponsiveText color={colors.white} textAlign="center" weight={'500'} size={5}>
            No Records Found
          </ResponsiveText>
        </View>
      </ImageBackground>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={data?.length === 0 && styles.emptyContentContainer}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}       // <-- add this
        onRefresh={onRefresh}          // <-- add this
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: wp(2),

    flex:1
  },
  productCard: {
    width: '48%',
    height: hp(20),
    margin: '1%',
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
  },
  titleContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: wp(2),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(10),
  },
  emptyContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  dotsButton: {
    position: 'absolute',
    top: wp(2),
    right: wp(2),
    zIndex: 1,
    padding: wp(2),
  },
  cardContent: {
    flex: 1,
  },
});

export default ProductGrid;
