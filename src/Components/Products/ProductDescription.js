import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const ProductDescription = ({ description }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { getDark_Theme, getTextColor } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <ResponsiveText
          color={getTextColor()}
          size={4}
          weight="bold"
        >
          {AppText.PRODUCT_DETAILS}
        </ResponsiveText>
        <Icon
          source={isExpanded ? globalpath.down  : globalpath.up}
          size={wp(4.5)}
          tintColor={getTextColor()}
        />
      </TouchableOpacity>
      {isExpanded && (
        <View style={styles.descriptionContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={3.5}
            weight="400"
            // lineHeight={hp(2.5)}
          >
            {description}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

export default ProductDescription;

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  descriptionContainer: {
    marginTop: hp(1),
  },
}); 