import { StyleSheet, View, TextInput } from 'react-native';
import React from 'react';
import { hp, wp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import useTheme from '../Redux/useTheme';
import { colors } from '../Custom/Colors';

const CustomTextInput = ({
  label,
  placeholder,
  value,
  onChangeText,
  keyboardType = 'default',
  secureTextEntry = false,
  editable = true,
  multiline = false,
  numberOfLines = 1,
  containerStyle,
  inputStyle,
  autoCapitalize = 'sentences',
}) => {
  const {
    getTextColor,
    getborderTextColor,
    backgroundColor,
    getDark_Theme
  } = useTheme();

  return (
    <View style={[styles.container, containerStyle]}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight={'500'}
        margin={[0, 0, hp(1), 0]}
      >
        {label}
      </ResponsiveText>
      <TextInput
        style={[
          styles.input,
          multiline && styles.multilineInput,
          {
            color: getTextColor(),
            borderColor: getDark_Theme(),
            backgroundColor: inputStyle?.backgroundColor || backgroundColor,
          },
          inputStyle,
        ]}
        placeholder={placeholder}
        placeholderTextColor={colors.grey}
        value={value}
        onChangeText={onChangeText}
        keyboardType={keyboardType}
        secureTextEntry={secureTextEntry}
        editable={editable}
        multiline={multiline}
        numberOfLines={numberOfLines}
        textAlignVertical={multiline ? 'top' : 'center'}
        autoCapitalize={autoCapitalize}
      />
    </View>
  );
};

export default CustomTextInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  input: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    fontSize: wp(3.8),
  },
  multilineInput: {
    height: hp(12),
    paddingTop: hp(1.5),
    paddingBottom: hp(1.5),
  },
});