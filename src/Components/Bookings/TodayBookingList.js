import React, {useCallback, useState} from 'react';
import {FlatList, RefreshControl, StyleSheet, View} from 'react-native';
// import TodayBookings from './TodayBookings'; // This is the card component
// import mockBookings, {Today_Bookings} from '../../Mocks/CustomerMock_data'; // Import mock data
import {hp, wp} from '../../Custom/Responsiveness';
import TodayBookings from './TodaysBookings';
import Loader from '../../Custom/loader';
// import TodayBookings from './TodaysBookings';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {list_customer_bookings} from '../../Services/API/Endpoints/Customer/BookAppointment';
import moment from 'moment';
import ResponsiveText from '../../Custom/RnText';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
 
const TodayBookingList = ({ searchQuery = '' }) => {
  // console.log('Today_Bookings:', Today_Bookings);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [myBookings, setMyBookings] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  
  const AppText=useAppText()
  useFocusEffect(
    useCallback(() => {
      fetchCustomerbooking();
    }, []),
  );
 
  const fetchCustomerbooking = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const today = getCurrentDate();
      console.log('today-->', today); // e.g., "2025-04-19"
      const data = await AsyncStorage.getItem('customerData');
      if (data) {
        const parsed = JSON.parse(data);
        // If it's an array, get the first customer
        const customerInfo = Array.isArray(parsed) ? parsed[0] : parsed;
 
        console.log(
          '✅ Customer object retrieved in the PersonalInfoProfile',
          customerInfo.id,
        );
        const response = await list_customer_bookings(
          customerInfo.id,
          today,
          today,
        );
        console.log('🟢 My Bookings Fetched fetched:', response);
 
        if (Array.isArray(response)) {
          const sortedServices = response.sort((a, b) => {
            const timeA = moment(a.start_time, 'HH:mm:ss');
            const timeB = moment(b.start_time, 'HH:mm:ss');
            return timeA - timeB;
          });
          setMyBookings(sortedServices);
          setFilteredServices(sortedServices);
        }
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
 
  const getCurrentDate = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Pad to 2 digits
    const day = String(date.getDate()).padStart(2, '0'); // Pad to 2 digits
 
    return `${year}-${month}-${day}`;
  };

  // Filter bookings based on search query
  const filteredBookings = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return myBookings;
    }
    
    return myBookings.filter(booking => {
      const searchLower = searchQuery.toLowerCase().trim();
      const customerName = booking.customer_name?.toLowerCase() || '';
      const shopName = booking.shop_name?.toLowerCase() || '';
      const serviceNames = booking.service_name?.map(service => 
        service.name?.toLowerCase() || ''
      ).join(' ') || '';

      return (
        customerName.includes(searchLower) ||
        shopName.includes(searchLower) ||
        serviceNames.includes(searchLower)
      );
    });
  }, [myBookings, searchQuery]);

  console.log('Today Search Query:', searchQuery);
  console.log('Today Filtered Bookings:', filteredBookings.length);

  const renderItem = ({item}) => {
    return (
      <TodayBookings
        item={item}
      />
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchCustomerbooking();
    setRefreshing(false);
  };

  
  return (
    <View>
    {loading ? (
<View style={Styles.LoaderAignment}>
  
<Loader />
</View>    ) : filteredBookings.length > 0 ? (
      <FlatList
        data={filteredBookings}
        renderItem={renderItem}
        keyExtractor={(item) => `today-booking-${item.id}-${item.date}-${item.start_time}`}
        contentContainerStyle={{paddingBottom: hp(25)}}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={fetchCustomerbooking}
            colors={[colors.Light_theme_maincolour]}
            tintColor={colors.Light_theme_maincolour}
          />
        }
      />
    ) : (
      <ResponsiveText
        weight={'500'}
        position={'absolute'}
        size={3.8}
        top={hp(30)}
        left={wp(25)}
        >
        {searchQuery ? AppText.NO_RESULTS_FOUND : AppText.NO_RECORD_FOUND}
      </ResponsiveText>
    )}
  </View>
  );
};
 
const Styles= StyleSheet.create({
  LoaderAignment:{alignItems:"center",marginTop:hp(30)}
});
export default TodayBookingList;