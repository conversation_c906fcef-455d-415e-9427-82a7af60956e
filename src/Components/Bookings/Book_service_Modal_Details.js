import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import Modal from 'react-native-modal';
import {hp, wp} from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';

const {height} = Dimensions.get('window');

const Book_service_Modal_Details = ({isVisible, onClose, item}) => {
  const AppText=useAppText()
  const {backgroundColor, getTextColor} = useTheme();
  console.log("The ITME IN BOOKING DETAILS FILE IS  ______",item)

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={[styles.ModalStyle, {backgroundColor}]}>
        <TouchableOpacity style={styles.closeModal} onPress={onClose}>
          <Icon
            source={globalpath.cross}
            style={styles.crossIcon}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Service Image */}
          {item?.service_image ? (
            <Image
              source={{uri: item.service_image}}
              style={styles.serviceImage}
            />
          ) : (
            <Icon source={globalpath.back1} size={hp(18)} resizeMode={'cover'}></Icon>
          )}

          {/* Name & Price */}
          <View style={styles.barberdetails}>
            <View>
              <ResponsiveText size={5} weight="bold" color={getTextColor()}>
                {item?.name || 'N/A'}
              </ResponsiveText>
              <ResponsiveText size={5} margin={[hp(.5), 0, 0, 0]} color={colors.c_green}>
                {new Intl.NumberFormat('en-IE', {
                  style: 'currency',
                  currency: 'EUR',
                }).format(parseFloat(item?.price || '0.00'))}
              </ResponsiveText>

            </View>
          </View>

          {/* Category & Subcategory */}
          <View style={styles.row}>
            <ResponsiveText size={3.7} color={getTextColor()} maxWidth={wp(40)} >
              {AppText.CATEGORY} : {item?.category_name || 'N/A'}
            </ResponsiveText>
            <ResponsiveText size={3.7} color={getTextColor()} maxWidth={wp(40)} numberOfLines={2}>
              {AppText.SUB_CATEGORY} : {item?.sub_category_name || 'N/A'}
            </ResponsiveText>
          </View>

          {/* Duration */}
          <View style={{marginVertical: hp(1.8)}}>
  <ResponsiveText size={4} color={getTextColor()} weight={'bold'}>
    {AppText.DURATION} : {item?.duration ? `${item.duration} ${AppText.MINS}` : 'N/A'}
  </ResponsiveText>
</View>

      
          <ResponsiveText size={4} color={getTextColor()} >{item?.description} </ResponsiveText>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default Book_service_Modal_Details;

const styles = StyleSheet.create({
  ModalStyle: {
    height: hp(60),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  closeModal: {
    alignSelf: 'flex-end',
  },
  crossIcon: {
    tintColor: 'red',
    height: hp(3),
    width: hp(3),
    resizeMode: 'contain',
    marginBottom:hp(2)
  },
  barberdetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(2),
    marginBottom: hp(1.5),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: hp(0.8),
  },
  serviceImage: {
    width: '100%',
    height: hp(20),
    borderRadius: 10,
    resizeMode: 'cover',
    marginBottom: hp(2),
  },
});
