import { Image, StyleSheet, Text, TextInput, TouchableOpacity, View , ScrollView } from 'react-native'
import React, { useState , useEffect } from 'react'
import ResponsiveText from '../../Custom/RnText'
import useAppText from '../../Custom/AppText'
import useTheme from '../../Redux/useTheme'
import { hp, wp } from '../../Custom/Responsiveness'
import Icon from '../../Custom/Icon'
import { globalpath } from '../../Custom/globalpath'
import { colors } from '../../Custom/Colors'
import { add_appointment_comments , get_customer_comments , get_barber_appointment ,update_comments } from '../../Services/API/Endpoints/Customer/comments';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { update_barber_appointment , update_customer_appointment } from '../../Services/API/Endpoints/barber/add_appointment';

const Book_Barber_Details = ({item , appointment}) => {
    console.log("The barber details is ",appointment)
    // console.log("BAR_BER DETAILS CARD ___",item)
    const AppText=useAppText()
    const { backgroundColor ,getTextColor,getDark_Theme} = useTheme();
    const [showSaveIcon, setShowSaveIcon] = useState(false);
    const [notesText, setNotesText] = useState('');

    const [comments, setComments] = useState([]);
    const [commentText, setCommentText] = useState('');
    const [isEditing, setIsEditing] = useState(false);
    const [editingCommentId, setEditingCommentId] = useState(null);



    const handleNotesChange = (text) => {
        setNotesText(text);
        setShowSaveIcon(text.length > 0);
    };
    const handleSaveNotes = () => {
        // Save your notes here
        console.log('Notes saved:', notesText);
        // You might want to add actual saving logic here
    };

    useEffect(() => {
            fetchComments();
          }, []);
          
          const fetchComments = async () => {
            try {
                if(appointment.customer_appointment){
                    const response = await get_customer_comments(appointment.id);
                    console.log('🟢 Comments response:', response); // Check if it's response.data or just response
                    setComments(response); // if response is an array
    
                }
                else{
                    const response = await get_barber_appointment(appointment.id);
                    console.log('🟢 Comments response:', response); // Check if it's response.data or just response
                    setComments(response); // if response is an array
    
                }
             
            } catch (error) {
              console.error('❌ Failed to fetch comments:', error);
            }
          };


    const handleSubmitComment = async () => {
        if (!commentText.trim()) return;
      
        try {
          const userData = await AsyncStorage.getItem('userData');
          const data = JSON.parse(userData);
          console.log('data-->', data)
          const { user_id } = JSON.parse(userData);
          console.log('user_id-->', user_id)
          
          const payload = {
            comment: commentText.trim(),
            customer_appointment: appointment.customer_appointment ? appointment.id : null,
            barber_appointment:  appointment.barber_appointment ? appointment.id : null,
            user:user_id,

        
          };
      
          if (isEditing && editingCommentId) {
            await update_comments(editingCommentId, payload);
          } else {
            await add_appointment_comments(payload);
          }
      
          setCommentText('');
          setIsEditing(false);
          setEditingCommentId(null);
          fetchComments(); // refresh list
        } catch (error) {
          console.error('❌ Failed to submit comment:', error);
        }
      };


  return (
    <View>
    <View style={[styles.DetailCard,{backgroundColor:backgroundColor,borderWidth:1,borderColor:getDark_Theme()}]}>
    <View style={styles.BarberDetail_Image}>
                    <ResponsiveText size={4.7} weight={'500'} color={getTextColor()}>
                        {AppText.BARBER_DETAILS}
                    </ResponsiveText>
                    {/* Map over the array within 'item' */}
                    {Array.isArray(item) && item.length > 0 && item.map((barber, index) => (
                        <View key={index}>
                            {/* Display the barber's image dynamically */}
                            {barber?.profile_image ? (
                                <Image source={{ uri: barber.profile_image }} style={styles.BarberImage} />
                            ) : (
                                <Icon source={globalpath.profile} style={styles.BarberImage} />
                            )}
                        </View>
                    ))}
                </View>


        <View style={styles.Barberinfo}>
        <Icon source={globalpath.user2} style={styles.Icon}></Icon>
        <ResponsiveText size={4} weight={'500'} color={getTextColor()}>
  {Array.isArray(item) && item.length > 0 ? item[0]?.full_name || 'N/A' : 'N/A'}
</ResponsiveText>        </View>
        <View style={styles.Barberinfo}>
        <Icon source={globalpath.call} style={styles.Icon}></Icon>
        <ResponsiveText size={4} weight={'500'} color={getTextColor()}>
  {Array.isArray(item) && item.length > 0 ? item[0]?.phone_number || 'N/A' : 'N/A'}
</ResponsiveText>        </View>
        <View style={styles.Barberinfo}>
        <Icon source={globalpath.order_location} style={styles.Icon}></Icon>
        <ResponsiveText size={4} weight={'500'} color={getTextColor()} maxWidth={wp(70)} numberOfLines={1}>
  {Array.isArray(item) && item.length > 0 ? item[0]?.address_city || 'N/A' : 'N/A'}
</ResponsiveText>        </View>
       
    </View>
 <View style={[styles.DetailCard,{backgroundColor:backgroundColor,borderWidth:1,borderColor:getDark_Theme()}]}>
    <View style={styles.TotalAmount}>
        <ResponsiveText size={4.5} weight={'500'} color={getTextColor()}>{AppText.TOTAL_AMOUNT}</ResponsiveText>
        <ResponsiveText size={4.5} weight={'500'} color={getTextColor()}>$500.00</ResponsiveText>

    </View>
        </View>

        <View style={[styles.DetailCard, { backgroundColor: backgroundColor, borderWidth: 1, borderColor: getDark_Theme() }]}>
                    <ResponsiveText size={4.3} weight={'500'} color={getTextColor()}>
                        Comments
                    </ResponsiveText>

                    {/* Comment List */}
                    <View
                        style={{
                            maxHeight: hp(25), // roughly 3 comment cards high
                            marginTop: hp(2),
                        }}
                        >
                        <ScrollView
                            showsVerticalScrollIndicator={true}
                            nestedScrollEnabled={true}
                            contentContainerStyle={{ paddingBottom: hp(2) }}
                        >
                          {comments.map((item) => {
                                const avatarLetter = item.user_email?.charAt(0)?.toUpperCase() || '?';

                                return (
                                    <View
                                    key={item.id}
                                    style={{
                                        flexDirection: 'row',
                                        backgroundColor: colors.lightGrey,
                                        borderRadius: wp(2),
                                        padding: wp(3),
                                        marginHorizontal: wp(1),
                                        marginBottom: hp(1.5),
                                        shadowColor: '#000',
                                        shadowOffset: { width: 0, height: 1 },
                                        shadowOpacity: 0.05,
                                        shadowRadius: 1,
                                        elevation: 2,
                                        alignItems: 'flex-start',
                                    }}
                                    >
                                    {/* Avatar */}
                                    <View
                                        style={{
                                        height: wp(10),
                                        width: wp(10),
                                        borderRadius: wp(5),
                                        backgroundColor: colors.c_green,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        marginRight: wp(3),
                                        }}
                                    >
                                        <ResponsiveText size={3.5} weight="600" color={colors.white}>
                                        {avatarLetter}
                                        </ResponsiveText>
                                    </View>

                                    {/* Content + Edit Icon */}
                                    <View style={{ flex: 1 }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <ResponsiveText size={3.5} weight="500" color={colors.black} style={{ flex: 1 }}
                                        
                                        maxWidth={wp(50)}
                                        >
                                            {item.comment}
                                        </ResponsiveText>

                                        {/* Edit Icon */}
                                        <TouchableOpacity onPress={() => handleEditComment(item)}>
                                            <Icon
                                            source={globalpath.edit} // use a proper pencil/edit icon here
                                            size={14}
                                            margin={[hp(0.4),wp(1),0,0]}
                                            />
                                        </TouchableOpacity>
                                        </View>

                                        {/* Timestamp */}
                                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: hp(1) }}>
                                        <ResponsiveText size={2.5} weight="300" color={colors.grey}>
                                            {new Date(item.created_at).toLocaleString('en-US', {
                                            day: '2-digit',
                                            month: 'short',
                                            year: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            })}
                                        </ResponsiveText>
                                        </View>
                                    </View>
                                    </View>
                                );
                                })}


                        </ScrollView>
                        </View>
                    {/* Comment Input + Add Button */}
                    <TextInput
                        placeholder="Write a comment..."
                        placeholderTextColor={colors.grey}
                        value={commentText}
                        onChangeText={setCommentText}
                        style={[
                            styles.Input_Notes,
                            {
                            color: colors.black,
                            height: hp(10),
                            marginTop: hp(2),
                            borderColor: isEditing ? colors.Light_theme_maincolour : colors.lightGrey,
                            borderWidth: 1.2,
                            },
                        ]}
                        multiline
                        />

                        <TouchableOpacity
                        style={[styles.Add_Service_Button_style, { backgroundColor: colors.Light_theme_maincolour }]}
                        onPress={handleSubmitComment}
                        >
                        <ResponsiveText color={colors.white}>
                            {isEditing ? 'Update Comment' : 'Add Comment'}
                        </ResponsiveText>
                        </TouchableOpacity>

                    </View>
   
   

        </View>
  )
}

export default Book_Barber_Details

const styles = StyleSheet.create({
    DetailCard:{
        backgroundColor:"red",
        paddingHorizontal:wp(5),
        paddingVertical:hp(2),
        elevation:5,
        marginHorizontal:wp(5),
        borderRadius:wp(3),
        marginTop:hp(2)
    },
    BarberDetail_Image:{
flexDirection:"row",
alignItems:"center",
justifyContent:"space-between"
    },
    BarberImage:{
        height:hp(7),
        width:wp(14),
        resizeMode:"contain"
    },
    Barberinfo:{
        alignItems:"center",
        flexDirection:"row",
        marginVertical:hp(0.4)
    },
    Icon:{
        height:hp(4),
        width:wp(7),
        resizeMode:"contain",
        marginRight:wp(3)
    },
     ADNIcon:{
        height:hp(3),
        width:wp(5),
        resizeMode:"contain",
        // marginRight:wp(3)
    },
    TotalAmount:{
        flexDirection:"row",
        alignItems:"center",
        justifyContent:"space-between",
    },
    ADNcard:{
        // backgroundColor:"red",
        paddingHorizontal:wp(5),
        paddingVertical:hp(2),
        // elevation:5,
        marginHorizontal:wp(5),
        borderRadius:wp(3),
        marginTop:hp(2)
    },
    Input_Notes: {
        height: hp(12), // Increased height for multiline
        textAlignVertical: 'top', // Align text to top for multiline
        justifyContent: "flex-start", // Changed to flex-start for multiline
        backgroundColor: colors.lightGrey,
        paddingHorizontal: wp(5),
        paddingVertical: hp(2),
        borderWidth: 0.7,
        marginHorizontal: wp(5),
        borderRadius: wp(1.5),
        // marginTop: hp(2),
        fontSize: hp(1.8),
    },
      Add_Service_Button_style: {
          alignSelf: "flex-end",
          paddingHorizontal: wp(2),
          paddingVertical: hp(1),
          backgroundColor: "red",
          marginRight: wp(5),
          borderRadius: wp(1),
          marginTop: hp(2)
      }
})