import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import Customer_Header from '../CustomerComponents/Customer_Header';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import {SafeAreaView} from 'react-native-safe-area-context';
import ResponsiveText from '../../Custom/RnText';
import {hp, wp} from '../../Custom/Responsiveness';
import {colors} from '../../Custom/Colors';
import Your_Book_Service from './Your_Book_Service';
import Book_Barber_Details from './Book_Barber_Details';
import Book_service_Modal_Details from './Book_service_Modal_Details';
import {get_barber_deatils_from_id} from '../../Services/API/Endpoints/Customer/BookAppointment';
import { get_service_details_from_id } from '../../Services/API/Endpoints/Customer/Get_Service_Detail';
import {useFocusEffect} from '@react-navigation/native';
import Loader from '../../Custom/loader';


const BookingDetails = ({route}) => {
  const {item} = route.params;
  console.log('The item in BookingDETAILS======', item);
  const barberId = item?.barber;
  console.log('The BARBER___ID IS - - - - - - - - -', barberId);
  const serviceId = item?.service_name?.[0]?.id;
  console.log('The SERVICE___ID IS - - - - - - - - -', serviceId);


  const [modalVisible, setModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [serviceDetails, setServiceDetails] = useState(null); // for service Details
  const [barberDetails, setBarberDetails] = useState(null);
  const [loading, setLoading] = useState(true);  // New loading state



  const AppText = useAppText();
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();

  const formatDate = dateString => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', {month: 'short'}); // "Mar"
    const year = date.getFullYear(); // 2025
    return `${month}-${String(day).padStart(2, '0')}-${year}`;
  };

  const formatTime = timeString => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours));
    date.setMinutes(parseInt(minutes));
    return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
  };

  useFocusEffect(
    useCallback(() => {
      setLoading(true); // Start loading

      if (barberId) {
        fetchBarberDetails(barberId);
      }
    }, [barberId]),
  );
  
  // For service details
  useFocusEffect(
    useCallback(() => {
      if (serviceId) {
        fetchServiceDetails(serviceId);
      }
    }, [serviceId]),
  );

  const fetchBarberDetails = async (id) => {
    try {
      const response = await get_barber_deatils_from_id(id);
      console.log('Barber Details:', response);
      setBarberDetails(response);
    } catch (err) {
      console.error('Failed to fetch barber info:', err);
    }
    console.log('the Barber Details recieved are --------',barberDetails)
  };
  
  const fetchServiceDetails = async (id) => {
    try {
      const response = await get_service_details_from_id(id);
      console.log('Service Details:', response);
      setServiceDetails(response);
    } catch (err) {
      console.error('Failed to fetch service info:', err);
    }
  };

  useEffect(() => {
    if (serviceDetails && barberDetails) {
      setLoading(false);  // Set loading to false when both details are fetched
    }
  }, [serviceDetails, barberDetails]);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: backgroundColor }}>
      <Customer_Header title={AppText.BOOKINGS} leftIconType="back" />

      {/* Only show the loader when loading is true */}
      {loading ? (
        <Loader />
      ) : (
        <ScrollView contentContainerStyle={{ paddingBottom: hp(25) }} showsVerticalScrollIndicator={false}>
          <KeyboardAvoidingView behavior={Platform.OS ? 'height' : 'padding'} style={{ flex: 1 }}>
            <View style={styles.main}>
              <View
                style={[
                  styles.AppointmentDetails,
                  {
                    backgroundColor: backgroundColor,
                    borderWidth: 1,
                    borderColor: getDark_Theme(),
                  },
                ]}>
                <ResponsiveText size={4.7} weight={'500'} color={getTextColor()} margin={[hp(0.5), 0, wp(2), 0]}>
                  {AppText.APPOINTMENT_SHEDULE}
                </ResponsiveText>
                <View style={styles.AppointmentDate_tme}>
                  <View style={styles.DateTimeContainer}>
                    <ResponsiveText size={4.5} color={colors.black}>
                      {formatDate(item.date)}
                    </ResponsiveText>
                  </View>
                  <View style={styles.DateTimeContainer}>
                    <ResponsiveText size={4.5} color={colors.black}>
                      {formatTime(item.start_time)}
                    </ResponsiveText>
                  </View>
                </View>
              </View>

              {/* Only render this after loading is complete */}
              <Your_Book_Service
                services={serviceDetails ? [serviceDetails] : []}
                onSeeDetails={(item) => {
                  setSelectedItem(item);
                  setModalVisible(true);
                }}
              />

              <Book_Barber_Details    appointment={item} item={barberDetails} />
              <Book_service_Modal_Details
                isVisible={modalVisible}
             
                onClose={() => setModalVisible(false)}
                item={serviceDetails}
              />
            </View>
          </KeyboardAvoidingView>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};
export default BookingDetails;

const styles = StyleSheet.create({
  main: {
    // flex:1,
  },
  AppointmentDetails: {
    marginHorizontal: wp(3.5),
    marginTop: hp(1),
    elevation: 5,
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
    borderRadius: wp(1.5),
  },
  AppointmentDate_tme: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor:"pink",
    marginHorizontal: wp(-1.5),
  },
  DateTimeContainer: {
    paddingHorizontal: wp(5),
    backgroundColor: colors.lightGrey4,
    paddingVertical: hp(0.7),
    borderRadius: wp(5),
    // marginLeft:wp(3),
    marginTop: hp(0.7),
    marginHorizontal: wp(1),
  },
});
