import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import BookingList from './BookingList';
import TodayBookingList from './TodayBookingList';
import BookingSearchBar from './BookingSearchBar';

const Booking_ToggleButton_Filter = () => {
  const AppText = useAppText();
  const { getTextColor } = useTheme();

  // State to track the selected button and search text
  const [selected, setSelected] = useState('TODAY');
  const [searchQuery, setSearchQuery] = useState('');

  // Handle search input
  const handleSearch = (text) => {
    setSearchQuery(text);
  };

  return (
    <View>
      <BookingSearchBar onSearch={handleSearch} />
      <View style={styles.ToogleRow_Filter}>
        <View style={styles.ToggleButton}>
          {/* ALL Button */}
          <TouchableOpacity
            style={[
              styles.Button_Style,
              selected === 'ALL' ? styles.selectedButton : styles.unselectedButton,
            ]}
            onPress={() => setSelected('ALL')}
          >
            <ResponsiveText
              style={[
                styles.Button_Label,
                selected === 'ALL' ? styles.selectedText : styles.unselectedText,
              ]}
              color={selected === 'ALL' ? colors.white : colors.lightGrey5}
            >
              {AppText.ALL}
            </ResponsiveText>
          </TouchableOpacity>

          {/* TODAY Button */}
          <TouchableOpacity
            style={[
              styles.Button_Style,
              selected === 'TODAY' ? styles.selectedButton : styles.unselectedButton,
            ]}
            onPress={() => setSelected('TODAY')}
          >
            <ResponsiveText
              style={[
                styles.Button_Label,
                selected === 'TODAY' ? styles.selectedText : styles.unselectedText,
              ]}
              color={selected === 'TODAY' ? colors.white : colors.lightGrey5}
            >
              {AppText.TODAY}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        {/* FILTER Button */}
        {/* <TouchableOpacity style={styles.filter}>
          <ResponsiveText style={styles.filterLabel} color={getTextColor()}>
            {AppText.FILTER}
          </ResponsiveText>
          <Icon source={globalpath.filter} style={styles.FilterIcon}  tintColor={getTextColor()}/>
          </TouchableOpacity> */}
      </View>
      {selected === 'ALL' ? (
        <BookingList searchQuery={searchQuery} />
      ) : (
        <TodayBookingList searchQuery={searchQuery} />
      )}
    </View>
  );
};

export default Booking_ToggleButton_Filter;

const styles = StyleSheet.create({
  ToogleRow_Filter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  ToggleButton: {
    backgroundColor:colors.lightGrey,
    paddingVertical: hp(0.5),
    paddingHorizontal: wp(0.5),
    borderWidth: wp(0.1),
    borderColor:colors.lightGrey5,
    width: wp(45),
    borderRadius: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(1),
  },
  Button_Style: {
    flex: 1,
    paddingVertical: hp(0.3),
    paddingHorizontal: wp(2),
    alignItems: 'center',
    borderRadius: wp(4),
  },
  selectedButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  unselectedButton: {
    backgroundColor: 'transparent',
  },
  Button_Label: {
    fontSize: hp(1.8),
  },
  selectedText: {
    color: colors.white,
  },
  unselectedText: {
    color: colors.black,
  },
  filter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterLabel: {
    fontSize: hp(2.4),
    fontWeight: 'bold',
    marginRight: wp(2.5),
  },
  FilterIcon:{
    height:hp(2),
    width:wp(5),
    resizeMode:"contain"
  }
 });
