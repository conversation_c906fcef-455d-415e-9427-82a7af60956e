import React, {useCallback, useState} from 'react';
import {FlatList, RefreshControl, StyleSheet, TouchableOpacity, View} from 'react-native';
import BookingCard from './BookingCard';
import {hp, wp} from '../../Custom/Responsiveness';
import {useFocusEffect} from '@react-navigation/core';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {customer_bookings_History} from '../../Services/API/Endpoints/Customer/BookAppointment';
import ResponsiveText from '../../Custom/RnText';
import Loader from '../../Custom/loader';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';

const BookingList = ({ searchQuery = '' }) => {
  const [bookingsHistory, setBookingsHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const AppText = useAppText();
  const [refreshing, setRefreshing] = useState(false);
  

  useFocusEffect(
    useCallback(() => {
      fetchCustomerBooking();
    }, []),
  );

  const fetchCustomerBooking = async () => {
    try {
      setLoading(true);
      const data = await AsyncStorage.getItem('customerData');
      if (data) {
        const parsed = JSON.parse(data);
        const customerInfo = Array.isArray(parsed) ? parsed[0] : parsed;
        console.warn('Booking history customer id', customerInfo.id);
  
        const response = await customer_bookings_History(customerInfo.id);
        console.log('📦 Booking history response:', response);
  
        if (response) {
          // Sort the bookings by date in ascending order (earliest first)
          const sortedBookings = response.sort((a, b) => {
            const dateA = new Date(a.date);  // Convert the date to a Date object
            const dateB = new Date(b.date);  // Convert the date to a Date object
            
            // Ensure valid date comparison by checking if both are valid
            if (isNaN(dateA) || isNaN(dateB)) {
              console.error('Invalid date:', a.date, b.date);
              return 0;
            }
  
            // Return the comparison result (most recent date first)
            return dateA - dateB;
          });
  
          setBookingsHistory(sortedBookings);  // Set the sorted bookings
        } else {
          setBookingsHistory([]);
        }
      }
    } catch (error) {
      console.error('❌ Error fetching bookings:', error);
    } finally {
      setLoading(false);
    }
  };
  console.log('the customer bookings history is --------', bookingsHistory);

  const formatTime = timeString => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours));
    date.setMinutes(parseInt(minutes));
    return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
  };

  const getDuration = (start, end) => {
    const [startH, startM] = start.split(':').map(Number);
    const [endH, endM] = end.split(':').map(Number);
    const startTotal = startH * 60 + startM;
    const endTotal = endH * 60 + endM;
    return `${endTotal - startTotal} mins`;
  };

  const formatDate = dateString => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', {month: 'long'});
    const year = date.getFullYear().toString().slice(-2); // '25' from '2025'
    return `${day} ${month} ${year}`;
  };

  // Filter bookings based on search query
  const filteredBookings = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return bookingsHistory;
    }
    
    return bookingsHistory.filter(booking => {
      const searchLower = searchQuery.toLowerCase().trim();
      const customerName = booking.customer_name?.toLowerCase() || '';
      const shopName = booking.shop_name?.toLowerCase() || '';
      const serviceNames = booking.service_name?.map(service => 
        service.name?.toLowerCase() || ''
      ).join(' ') || '';

      return (
        customerName.includes(searchLower) ||
        shopName.includes(searchLower) ||
        serviceNames.includes(searchLower)
      );
    });
  }, [bookingsHistory, searchQuery]);

  console.log('Search Query:', searchQuery);
  console.log('Filtered Bookings:', filteredBookings.length);

  const renderItem = ({item}) => (
    <BookingCard
      customerName={item.customer_name}
      salonName={item.shop_name}
      serviceName={item.service_name.map(service => service.name).join(', ')}
      date={formatDate(item.date)}
      time={formatTime(item.start_time)}
      duration={getDuration(item.start_time, item.end_time)}
      item={item}
    />
  );

  const onRefresh = async () => {
    try {
      setRefreshing(true);
      await fetchCustomerBooking();
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <View>
      {loading ? (
        <View style={styles.LoaderAignment}>
          <Loader />
        </View>
      ) : filteredBookings.length > 0 ? (
        <FlatList
          data={filteredBookings}
          renderItem={renderItem}
          keyExtractor={(item) => `booking-${item.id}-${item.date}-${item.start_time}`}
          contentContainerStyle={{paddingBottom: hp(25)}}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.Light_theme_maincolour]}
              tintColor={colors.Light_theme_maincolour}
            />
          }
        />
      ) : (
        <ResponsiveText
          weight={'500'}
          position={'absolute'}
          size={3.8}
          top={hp(30)}
          left={wp(25)}
        >
          {searchQuery ? AppText.NO_RESULTS_FOUND : AppText.NO_RECORD_FOUND}
        </ResponsiveText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  LoaderAignment:{alignItems:"center",marginTop:hp(30)}
});

export default BookingList;
