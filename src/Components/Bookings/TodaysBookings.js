import React, {useState, useCallback, useEffect} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import {colors} from '../../Custom/Colors';
import {useNavigation} from '@react-navigation/native';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Today_Booking_Modal from './Today_Booking_Modal';
import {list_customer_bookings} from '../../Services/API/Endpoints/Customer/BookAppointment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';

// Function to format the time
const formatTime = (timeString) => {
  const [hours, minutes] = timeString.split(':');
  const date = new Date();
  date.setHours(parseInt(hours));
  date.setMinutes(parseInt(minutes));
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Function to calculate the duration between start and end times
const getDuration = (start, end) => {
  const [startH, startM] = start.split(':').map(Number);
  const [endH, endM] = end.split(':').map(Number);
  const startTotal = startH * 60 + startM;
  const endTotal = endH * 60 + endM;
  return `${endTotal - startTotal}`;
};

const TodayBookings = ({item}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation();
  const AppText = useAppText();
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const handleBookingDetailsPress = () => {
    navigation.navigate('BookingDetails', { item });
  };

  // Format the times using the functions
  const formattedStartTime = formatTime(item.start_time);
  const formattedEndTime = formatTime(item.end_time);
  const duration = getDuration(item.start_time, item.end_time);

  return (
    <TouchableOpacity
      style={[
        styles.bookingCard,
        {
          backgroundColor: backgroundColor,
          borderWidth: 1,
          borderColor: getDark_Theme(),
        },
      ]}
      onPress={handleBookingDetailsPress}
    >
      <View style={styles.dateTime}>
        <View style={styles.dateScheduledTime}>
          <View style={styles.date}>
            <ResponsiveText color={'white'} maxWidth={wp(30)} numberOfLines={1}>
              {item?.service_name[0]?.name}
            </ResponsiveText>
          </View>
          <View style={styles.time}>
            <ResponsiveText color={'white'}>
              {formattedStartTime}
            </ResponsiveText>
          </View>
        </View>
      </View>

      <View style={styles.salonServiceDetail}>
        <ResponsiveText size={4.5} color={getTextColor()} weight={'600'}>
          {AppText.SALON}
        </ResponsiveText>
        <ResponsiveText size={4.5} color={getTextColor()} weight={'600'}>
          {item?.shop_name}
        </ResponsiveText>
      </View>

      {item?.shop_address && (
        <View style={styles.salonlocation}>
          <Icon source={globalpath.loc} style={styles.locationIcon} />
          <ResponsiveText color={getTextColor()} size={3.7} numberOfLines={1} maxWidth={wp(65)}>
            {item.shop_address}
          </ResponsiveText>
        </View>
      )}

      <View style={styles.CustomerImage_Name}>
        <Icon source={globalpath.customerimage} style={styles.customerimage} />
        <ResponsiveText maxWidth={wp(55)} numberOfLines={1} size={5}>
          {item?.customer_name}
        </ResponsiveText>
      </View>

      <View style={styles.remainingTime}>
        <View style={styles.timer}>
          <ResponsiveText color={colors.c_green}>{`${duration} ${AppText.MINS}`}</ResponsiveText>
        </View>

        <TouchableOpacity
          style={[
            styles.seedetails,
            { backgroundColor: colors.lightGrey, borderColor: getDark_Theme() },
          ]}
          onPress={openModal}
        >
          <ResponsiveText style={styles.label1} color={colors.lightGrey5}>
            {AppText.SEE_DETAILS}
          </ResponsiveText>
          <Icon source={globalpath.eye2} style={styles.eye} />
        </TouchableOpacity>
      </View>

      <Today_Booking_Modal
        isVisible={modalVisible}
        onClose={closeModal}
        item={item} // Pass the item data to the modal
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  bookingCard: {
    marginTop: hp(2.5),
    marginHorizontal: wp(1),
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(1),
    elevation: hp(0.5),
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 2.84,
    borderRadius: wp(2),
  },
  dateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(1),
  },
  dateScheduledTime: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginHorizontal: wp(3),
  },
  date: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(0.5),
    backgroundColor: colors.c_green,
    borderRadius: wp(1),
  },
  time: {
    paddingHorizontal: wp(5),
    paddingVertical: hp(0.5),
    backgroundColor: colors.grey,
    borderRadius: wp(1),
  },
  salonServiceDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: wp(4),
    marginVertical: hp(1),
  },
  salonlocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: wp(4),
    marginTop: hp(0.5),
  },
  locationIcon: {
    height: hp(2),
    width: wp(2.5),
    resizeMode: 'contain',
    marginRight: wp(2),
  },
  CustomerImage_Name: {
    marginTop: hp(1.3),
    marginHorizontal: wp(4),
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerimage: {
    height: hp(8),
    width: wp(16),
    resizeMode: 'contain',
    marginRight: wp(4),
  },
  remainingTime: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: wp(5),
    marginTop: hp(2),
  },
  timer: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.6),
    borderWidth: wp(0.1),
    borderRadius: wp(1.5),
    borderColor: colors.c_green,
  },
  seedetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.6),
    borderWidth: wp(0.1),
    borderRadius: wp(1),
    borderColor: colors.lightGrey5,
  },
  label1: {
    fontSize: hp(1.5),
    fontWeight: 'bold',
  },
  eye: {
    height: hp(2),
    width: wp(4),
    resizeMode: 'contain',
    marginHorizontal: wp(0.5),
  },
});

export default TodayBookings;
