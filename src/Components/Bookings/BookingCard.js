import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import {colors} from '../../Custom/Colors';
import {useNavigation} from '@react-navigation/native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
const BookingCard = ({
  customerName,
  salonName,
  serviceName,
  date,
  time,
  duration,
  item,
}) => {
  console.log('The Item Recieved in the BookingCARD IS _________', item);
  const navigation = useNavigation();
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const bookingData = {
    customerName,
    salonName,
    serviceName,
    date,
    time,
    duration,
  };

  return (
    <TouchableOpacity
      style={[
        styles.bookingCard,
        {
          backgroundColor: backgroundColor,
          borderWidth: 1,
          borderColor: getDark_Theme(),
        },
      ]}
      onPress={() => {
        // Navigate to the 'BookingDetails'
        navigation.navigate('BookingDetails', {item});
      }}>
      <View style={styles.dateTime}>
        <View style={styles.dateScheduledTime}>
          <View style={styles.date}>
            <ResponsiveText color={'white'}>{date}</ResponsiveText>
          </View>
          <View style={styles.time}>
            <ResponsiveText color={'white'}>{time}</ResponsiveText>
          </View>
        </View>
        <View style={styles.remainingTime}>
          <View style={styles.timer}>
            <ResponsiveText color={colors.c_green}>{duration}</ResponsiveText>
          </View>
        </View>
      </View>
      <ResponsiveText
        size={4.5}
        color={getTextColor()}
        maxWidth={wp(60)}
        numberOfLines={1}
        margin={[hp(1), 0, 0, wp(3)]}>
        {customerName}
      </ResponsiveText>
      <View style={styles.salonServiceDetail}>
        <ResponsiveText size={4.5} color={getTextColor()}>
          {AppText.SALON}
        </ResponsiveText>
        <ResponsiveText
          size={4}
          maxWidth={wp(37)}
          numberOfLines={1}
          color={getTextColor()}>
          {salonName}
        </ResponsiveText>
      </View>
      <View style={styles.salonServiceDetail}>
        <ResponsiveText size={4.5} color={getTextColor()}>
          {AppText.SERVICE}
        </ResponsiveText>
        <ResponsiveText
          size={4}
          maxWidth={wp(37)}
          numberOfLines={1}
          color={getTextColor()}>
          {serviceName}
        </ResponsiveText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  bookingCard: {
    // height: hp(18),
    elevation: hp(0.5),
    borderRadius: wp(2),
    // backgroundColor: colors.white,
    marginTop: hp(2.5),
    marginHorizontal: wp(1),
    elevation: hp(0.5),
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.25,
    shadowRadius: 2.84,
    elevation: 5,
    paddingHorizontal: wp(2),
    paddingVertical: hp(1.5),
  },
  dateTime: {
    // backgroundColor:"red",
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(1),
    marginHorizontal: wp(3),
  },
  dateScheduledTime: {
    // flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  date: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(0.5),
    backgroundColor: colors.c_green,
    borderRadius: wp(1),
  },
  label: {
    fontSize: hp(1.9),
    fontWeight: 'bold',
  },
  label3: {
    fontSize: hp(2.2),
    fontWeight: 'bold',
  },
  time: {
    paddingHorizontal: wp(5),
    paddingVertical: hp(0.5),
    backgroundColor: colors.grey,
    borderRadius: wp(1),
    marginHorizontal: wp(2),
  },
  remainingTime: {
    flex: 0.7,
    justifyContent: 'flex-end',
    flexDirection: 'row',
    // marginRight: wp(5),
  },
  timer: {
    paddingHorizontal: wp(1),
    paddingVertical: hp(0.6),
    borderWidth: wp(0.1),
    borderRadius: wp(1),
    borderColor: colors.c_green,
  },
  customerName: {
    fontSize: hp(2.5),
    marginTop: hp(1),
    marginHorizontal: wp(3.5),
    marginBottom: hp(1),
  },
  salonServiceDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: wp(3.5),
    marginTop: hp(0.5),
  },
});

export default BookingCard;
