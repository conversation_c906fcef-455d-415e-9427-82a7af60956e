// components/BottomHalfModal.js
import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Text,
  Button,
  TouchableOpacity,
} from 'react-native';
import Modal from 'react-native-modal';
// import { colors } from '../Custom/Colors'; // Adjust import path if needed
import {hp, wp} from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import {colors} from '../../Custom/Colors';
import moment from 'moment';
import useAppText from '../../Custom/AppText';

const {height} = Dimensions.get('window');

const Today_Booking_Modal = ({isVisible, onClose, item}) => {
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();
  const startTime = moment(item.start_time, 'HH:mm:ss');
  const endTime = moment(item.end_time, 'HH:mm:ss');
  const AppText = useAppText();
  const minutes = moment.duration(endTime.diff(startTime)).asMinutes();
  console.log(' toady booking modal item is ====', item);
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={[styles.ModalStyle, {backgroundColor: backgroundColor}]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: hp(2),
          }}>
          <View
            style={{
              backgroundColor: colors.lightGrey6,
              paddingHorizontal: wp(3),
              paddingVertical: hp(0.5),
              borderRadius: wp(6),
            }}>
           <ResponsiveText size={4} alignSelf={'center'} color={colors.black}>
  {AppText.BOOKING_TIME}: {moment(item.start_time, 'HH:mm:ss').format('hh:mm A')}
</ResponsiveText>
          </View>
          <TouchableOpacity style={styles.closeModal} onPress={onClose}>
            <Icon
              source={globalpath.cross}
              style={styles.crossIcon}
              tintColor={getTextColor()}></Icon>
          </TouchableOpacity>
        </View>

        <View style={styles.barberdetails}>
          <Icon style={styles.Profile} source={globalpath.customerimage}></Icon>
          <View>
            <ResponsiveText
              size={5.5}
              weight={'bold'}
              color={getTextColor()}
              maxWidth={wp(60)}
              numberOfLines={2}>
              {item?.customer_name}
            </ResponsiveText>
            {/* Replace with your fields */}
            <View style={styles.servicestyle}>
              <ResponsiveText size={3.5} color={colors.white}>
                {item?.service_name[0]?.name}
              </ResponsiveText>
            </View>
          </View>
        </View>
        <View style={styles.locationdetails}>
          <ResponsiveText
            size={4.2}
            weight={'300'}
            color={getTextColor()}
            maxWidth={wp(30)}
            numberOfLines={2}>
            {item?.shop_name}
          </ResponsiveText>

          {item?.shop_address && (
            <View style={styles.RowStyle}>
              <Icon source={globalpath.loc}   style={styles.locationIcon} />
              <ResponsiveText
                size={3.5}
                color={getTextColor()}
                maxWidth={wp(45)}
                numberOfLines={2}>
                {item.shop_address}
              </ResponsiveText>
            </View>
          )}
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <ResponsiveText
            size={5}
            weight={'bold'}
            color={getTextColor()}
            margin={[0, 0, 0, wp(2)]}>
            {AppText.DURATION}:
          </ResponsiveText>
          <View style={styles.timer}>
            <ResponsiveText style={styles.label} color={colors.c_green}>
              {`${minutes} ${AppText.DURATION_MINUTES}`}
            </ResponsiveText>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default Today_Booking_Modal;

const styles = StyleSheet.create({
  ModalStyle: {
    height: hp(33),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  container: {
    height: height * 0.5,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#ccc',
    borderRadius: 10,
    alignSelf: 'center',
    marginBottom: 10,
  },
  closeModal: {
    alignSelf: 'flex-end',
  },
  Profile: {
    height: hp(10),
    width: wp(20),
    resizeMode: 'contain',
    marginRight: wp(5),
  },
  barberdetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  crossIcon: {
    tintColor: 'red',
  },
  servicestyle: {
    backgroundColor: colors.c_green,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.4),
    borderRadius: wp(1.5),
    marginTop: hp(1),
    alignItems: 'center',
    justifyContent: 'center',
  },

  locationdetails: {
    marginVertical: hp(1.8),
    flexDirection: 'row',
    // alignItems:"center",
    justifyContent: 'space-between',
    marginHorizontal: wp(3),
  },
  locationIcon: {
    height: hp(2.8),
    width: wp(2.5),
    marginRight: wp(1),
    resizeMode: 'contain',
    position: 'absolute',
    left: -wp(4),
    top: -1,
  },
  RowStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timer: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.6),
    borderWidth: wp(0.1),
    borderRadius: wp(3),
    borderColor: colors.c_green,
    marginHorizontal: wp(2),
  },
  label: {
    fontSize: hp(1.9),
    fontWeight: 'bold',
  },
});
