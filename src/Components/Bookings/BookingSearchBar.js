import {StyleSheet, Text, TextInput, View} from 'react-native';
import React from 'react';
import {globalpath} from '../../Custom/globalpath';
import useAppText from '../../Custom/AppText';
import {useNavigation} from '@react-navigation/core';
import {hp, wp} from '../../Custom/Responsiveness';
import useTheme from '../../Redux/useTheme';
import Icon from '../../Custom/Icon';
import {colors} from '../../Custom/Colors';

const BookingSearchBar = ({ onSearch }) => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();
  return (
    <View>
      <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
        <Icon
          source={globalpath.search}
          size={20}
          tintColor={getTextColor()}
          margin={[0, wp(2), 0, 0]}
        />
        <TextInput
          style={[styles.searchInput, {color: getTextColor()}]}
          placeholder={AppText.SEARCH_BOOKINGS}
          placeholderTextColor={colors.grey}
          onChangeText={onSearch}
        />
      </View>
    </View>
  );
};

export default BookingSearchBar;

const styles = StyleSheet.create({
  searchContainer: {
    height: hp(5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
});
