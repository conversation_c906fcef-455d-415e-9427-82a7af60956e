import {FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {hp, wp} from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Icon from '../../Custom/Icon';
import {globalpath} from '../../Custom/globalpath';
import Book_service_Modal_Details from './Book_service_Modal_Details';
import {colors} from '../../Custom/Colors';

const Your_Book_Service = ({title, services}) => {
  console.log('services in your book service FLAtlist', services);
  const AppText = useAppText();
  const {backgroundColor, getTextColor} = useTheme();

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const handleSeeDetails = item => {
    if (!item) return;
    console.log('the item is ---', item);
    setSelectedItem(item);
    setModalVisible(true);
  };

  const renderItem = ({item}) => {
    if (!item) return null;
    
    return (
      <View style={styles.profileimage}>
        <Icon
          source={
            item?.service_image ? {uri: item?.service_image} : globalpath.logo
          }
          size={wp(15)}
          borderRadius={wp(30)}
          resizeMode={'cover'}
          margin={[0,wp(0),wp(2),wp(0)]}
        />
        {/* Title from item */}
        <ResponsiveText size={3.8} color={getTextColor()} maxWidth={wp(30)} numberOfLines={1}>
          {item?.name || 'Service Name'}
        </ResponsiveText>

        <ResponsiveText color={getTextColor()}>
            {new Intl.NumberFormat('en-IE', {
              style: 'currency',
              currency: 'EUR',
            }).format(parseFloat(item?.price || '0.00'))}
        </ResponsiveText>

        <TouchableOpacity
          style={styles.seedetails}
          onPress={() => handleSeeDetails(item)}>
          <ResponsiveText style={styles.label1} color={getTextColor()}>
            {AppText.SEE_DETAILS}
          </ResponsiveText>
          <Icon source={globalpath.eye2} style={styles.eye} />
        </TouchableOpacity>
      </View>
    );
  };

  // Handle empty or null services array
  const EmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <ResponsiveText color={getTextColor()} size={3.5}>
        No services available
      </ResponsiveText>
    </View>
  );

  return (
    <View style={styles.BookedServices}>
      {/* Custom or default title */}
      <ResponsiveText size={4.7} weight={'500'} color={getTextColor()}>
        {title || AppText.YOUR_BOOK_SERVICE}
      </ResponsiveText>

      <FlatList
        data={services || []}
        keyExtractor={(item, index) => (item?.id?.toString() || index.toString())}
        renderItem={renderItem}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={EmptyComponent}
      />

      {selectedItem && (
        <Book_service_Modal_Details
          isVisible={modalVisible}
          onClose={() => setModalVisible(false)}
          item={selectedItem}
        />
      )}
    </View>
  );
};

export default Your_Book_Service;

const styles = StyleSheet.create({
  BookedServices: {
    marginHorizontal: wp(5),
    marginTop: hp(2),
  },
  profileimage: {
    marginTop: hp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(5), // Spacing between items
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2),
    width: wp(90),
  },
  image: {
    height: hp(10),
    width: wp(17),
    resizeMode: 'contain',
  },
  label1: {
    fontSize: hp(1.5),
    fontWeight: 'bold',
  },
  seedetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.6),
    borderRadius: wp(1),
    borderColor: colors.lightGrey5,
  },
  eye: {
    height: hp(2),
    width: wp(4),
    resizeMode: 'contain',
    marginHorizontal: wp(0.5),
  },
  listContainer: {
    paddingHorizontal: 10,
  },
});
