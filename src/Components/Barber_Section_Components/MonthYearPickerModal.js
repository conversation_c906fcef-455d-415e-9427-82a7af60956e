import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Modal, TouchableOpacity, ScrollView } from 'react-native';
import { wp, hp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const MonthYearPickerModal = ({ visible, onClose, onSave, initialDate }) => {
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();

  const currentDate = new Date();
  // Always use the initialDate if provided, otherwise use current date
  const initialYear = initialDate ? new Date(initialDate).getFullYear() : currentDate.getFullYear();
  const initialMonth = initialDate ? new Date(initialDate).getMonth() : currentDate.getMonth();

  const [selectedYear, setSelectedYear] = useState(initialYear);
  const [selectedMonth, setSelectedMonth] = useState(initialMonth);

  // Update selected month/year when initialDate changes
  useEffect(() => {
    if (initialDate) {
      const date = new Date(initialDate);
      setSelectedYear(date.getFullYear());
      setSelectedMonth(date.getMonth());
    }
  }, [initialDate]);

  // Generate a wider range of years (current year - 20 to current year + 20)
  const years = [];
  const currentYear = currentDate.getFullYear();
  for (let i = currentYear - 20; i <= currentYear + 20; i++) {
    years.push(i);
  }

  // Month names
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const handleSave = () => {
    // Create a date object for the first day of the selected month and year
    const selectedDate = new Date(selectedYear, selectedMonth, 1);
    onSave(selectedDate);
    onClose();
  };

  // Check if a year is the current year
  const isCurrentYear = (year) => {
    return year === currentDate.getFullYear();
  };

  // Check if a month is the current month in the current year
  const isCurrentMonth = (monthIndex) => {
    return monthIndex === currentDate.getMonth() && selectedYear === currentDate.getFullYear();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor, borderColor: getDark_Theme() }]}>
          <View style={styles.headerContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={5}
              weight="bold"
            >
              {AppText.SELECT_MONTH_YEAR || "Select Month & Year"}
            </ResponsiveText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon source={globalpath.cross} size={wp(4.5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          </View>

          {/* Current Month/Year Button */}
          <TouchableOpacity
            style={styles.currentDateButton}
            onPress={() => {
              setSelectedMonth(currentDate.getMonth());
              setSelectedYear(currentDate.getFullYear());
            }}
          >
            <Icon
              source={globalpath.calendar}
              size={wp(4)}
              tintColor={colors.Light_theme_maincolour}
              style={styles.currentDateIcon}
            />
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3.5}
              weight="bold"
            >
              Current Month
            </ResponsiveText>
          </TouchableOpacity>

          <View style={styles.pickerContainer}>
            {/* Year Selection */}
            <View style={styles.sectionContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="bold"
                margin={[0, 0, hp(1), 0]}
              >
                Year
              </ResponsiveText>
              <View style={styles.yearPickerContainer}>
                <TouchableOpacity
                  style={styles.yearNavigationButton}
                  onPress={() => setSelectedYear(selectedYear - 1)}
                >
                  <Icon
                    source={globalpath.Right}
                    size={wp(4)}
                    tintColor={getTextColor()}
                  />
                </TouchableOpacity>

                <View style={styles.yearDisplay}>
                  <ResponsiveText
                    color={getTextColor()}
                    size={5}
                    weight="bold"
                  >
                    {selectedYear}
                  </ResponsiveText>
                  {isCurrentYear(selectedYear) && (
                    <View style={styles.currentYearIndicator} />
                  )}
                </View>

                <TouchableOpacity
                  style={styles.yearNavigationButton}
                  onPress={() => setSelectedYear(selectedYear + 1)}
                >
                  <Icon
                    source={globalpath.left}
                    size={wp(4)}
                    tintColor={getTextColor()}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Month Selection */}
            <View style={styles.sectionContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="bold"
                margin={[0, 0, hp(1), 0]}
              >
                Month
              </ResponsiveText>
              <View style={styles.monthGrid}>
                {months.map((month, index) => (
                  <TouchableOpacity
                    key={month}
                    style={[
                      styles.monthItem,
                      selectedMonth === index && styles.selectedItem,
                      isCurrentMonth(index) && styles.currentItem
                    ]}
                    onPress={() => setSelectedMonth(index)}
                  >
                    <ResponsiveText
                      color={selectedMonth === index ? colors.white : getTextColor()}
                      size={3.5}
                      weight={isCurrentMonth(index) ? "bold" : "normal"}
                    >
                      {month.substring(0, 3)}
                    </ResponsiveText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: getDark_Theme() }]}
              onPress={onClose}
            >
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.CANCEL || "Cancel"}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
            >
              <ResponsiveText color={colors.white} size={4}>
                {AppText.SAVE || "Save"}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default MonthYearPickerModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    padding: wp(5),
    borderRadius: wp(3),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  closeButton: {
    padding: wp(1),
  },
  currentDateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1),
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: colors.Light_theme_maincolour,
    borderRadius: wp(2),
    backgroundColor: 'rgba(78, 156, 175, 0.1)',
  },
  currentDateIcon: {
    marginRight: wp(2),
  },
  pickerContainer: {
    marginBottom: hp(2),
  },
  sectionContainer: {
    marginBottom: hp(2),
  },
  yearPickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1),
    paddingHorizontal: wp(2),
  },
  yearNavigationButton: {
    padding: wp(2),
    backgroundColor: "#E9ECF2",
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: wp(5),
  },
  yearDisplay: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1),
    paddingHorizontal: wp(4),
    position: 'relative',
  },
  currentYearIndicator: {
    position: 'absolute',
    bottom: 0,
    height: hp(0.5),
    width: '80%',
    backgroundColor: colors.Light_theme_maincolour,
    borderRadius: wp(1),
  },
  monthGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  monthItem: {
    width: '23%', // 4 columns with a little space
    paddingVertical: hp(1.5),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: hp(1),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  selectedItem: {
    backgroundColor: colors.Light_theme_maincolour,
    borderColor: colors.Light_theme_maincolour,
  },
  currentItem: {
    borderColor: colors.Light_theme_maincolour,
    borderWidth: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    flex: 1,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});
