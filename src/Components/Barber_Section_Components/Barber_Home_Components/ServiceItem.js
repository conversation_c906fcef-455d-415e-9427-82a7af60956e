import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import { globalpath } from '../../../Custom/globalpath'; // Assuming you store your icons here
import Icon from '../../../Custom/Icon';

const ServiceItem = ({ service, isLast, isSelected, onPress }) => {
  const { getTextColor, getDark_Theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        [styles.card,{borderColor:getDark_Theme()}],
        isSelected && styles.cardSelected,
        !isLast && { marginBottom: hp(1.5) }
      ]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.cardContent}>
        <View style={styles.colorDotWrapper}>
          <View style={[styles.colorDot, { backgroundColor: service.color || '#32CD32' }]} />
        </View>

        <View style={styles.detailsWrapper}>
          <ResponsiveText
            size={4}
            weight="500"
            color={getTextColor()}
            numberOfLines={2}
            margin={[0, 0, hp(0.5), 0]}
          >
            {service.name}
          </ResponsiveText>

          <View style={styles.row}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Icon
              source={globalpath.repeat} // Make sure this icon exists
              size={wp(4.2)}
              tintColor={colors.lightGrey5}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText size={3.5} color={colors.grey} margin={[0, wp(4), 0, 0]}>
              {service.duration}
            </ResponsiveText>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Icon
              source={globalpath.tag} // Make sure this icon exists
              size={wp(4.2)}
              tintColor={colors.lightGrey5}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText size={3.8} weight="600" color={colors.Light_theme_maincolour}>
              {service.price}
            </ResponsiveText>
            </View>
          </View>
        </View>

        {isSelected && (
          <Image
            source={globalpath.check} // Make sure this icon exists
            style={styles.tickIcon}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ServiceItem;

const styles = StyleSheet.create({
  card: {
    // backgroundColor: colors.white,
    borderRadius: wp(3),
    padding: wp(3.5),
    marginHorizontal: wp(2),
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 4,
    borderWidth:1
  },
  cardSelected: {
    borderColor: colors.Light_theme_maincolour,
    borderWidth: 1.5,
    // backgroundColor: "#f7f7f7",
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorDotWrapper: {
    marginRight: wp(2),
    alignSelf: 'flex-start', // ✅ Keeps it pinned at the top
    marginTop: hp(0.6), // ✅ Keeps it pinned at the top
  },

  colorDot: {
    width: wp(3),
    height: wp(3),
    borderRadius: wp(1.5),
  },
  detailsWrapper: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:"space-between"
  },
  tickIcon: {
    width: wp(5),
    height: wp(5),
    resizeMode: 'contain',
    tintColor: colors.Light_theme_maincolour,
    marginLeft: wp(2),
  },
});
