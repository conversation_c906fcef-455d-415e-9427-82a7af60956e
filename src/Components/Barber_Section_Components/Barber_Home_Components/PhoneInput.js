import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const PhoneInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.PHONE}
      placeholder={AppText.ENTER_PHONE}
      value={value}
      onChangeText={onChangeText}
      keyboardType="phone-pad"
    />
  );
};

export default PhoneInput; 