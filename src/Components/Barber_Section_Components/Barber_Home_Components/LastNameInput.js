import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const LastNameInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.LAST_NAME}
      placeholder={AppText.ENTER_LAST_NAME}
      value={value}
      onChangeText={onChangeText}
    />
  );
};

export default LastNameInput; 