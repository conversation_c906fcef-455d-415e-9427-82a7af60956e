import React, { useState, useCallback, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import CustomerNameDropdown from './CustomerNameDropdown';
import DateInput from './DateInput';
import TimeInput from './TimeInput';
import AddNewContent from './AddNewContent';
import AddServiceContent from './AddServiceContent';
import SelectedServicesList from './ SelectedServicesList';
import PreviousBookContent from './PreviousBookContent';
import { get_barber_deatils } from '../../../Services/API/Endpoints/barber/dashboard';
import { barber_appointment } from '../../../Services/API/Endpoints/barber/add_appointment';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import ManageBreaks from './ManageBreaks';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BreaksBarberDropdown from '../../Admin_Appointment_Component/breaks_barber_dropdown';


const AddAppointmentContent = ({
  onClose,
  selectedDate,
  setSelectedDate,
  onAppointmentAdded,
  salonData,
  userRole,
  selectedBarber: initialSelectedBarber
}) => {
  const AppText = useAppText();
  const { getDark_Theme, getTextColor } = useTheme();
  const [showAddNew, setShowAddNew] = useState(false);
  const [showAddService, setShowAddService] = useState(false);
  const [showPreviousBook, setShowPreviousBook] = useState(false);
  const [selectedServices, setSelectedServices] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [shopid, setShopID] = useState(null);
  const [barbers, setBarbers] = useState([]);
  const [selectedServiceDurations, setSelectedServiceDurations] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [allcustomers, setAllcustomers] = useState([]);
  const [customerSearchText, setCustomerSearchText] = useState('');
  const [selectedCustomerName, setSelectedCustomerName] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showManageBreaks, setShowManageBreaks] = useState(false);
  const [selectedBarber, setSelectedBarber] = useState(initialSelectedBarber || null);
  const [selectedCustomerDetails, setSelectedCustomerDetails] = useState(null);

  const handleCustomerChange = (id) => {
    setSelectedCustomer(id);
    if (id) {
      const customerObj = allcustomers?.find(c => c.id === id);
      setSelectedCustomerDetails(customerObj);
    } else {
      setSelectedCustomerDetails(null);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (userRole === 'barber') {
        fetchbarberdetails();
      }
    }, [userRole]),
  );

  const fetchbarberdetails = async () => {
    try {
      setLoading(true);
      setRefreshing(true);

      const response = await get_barber_deatils();
      console.log('🟢Barber details fetched:', response);

      if (response && response.length > 0) {
        setShopID(response[0].shop);
        setBarbers(response[0]);
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const formatTime = (time) => {
    if (!time) return '';
    return new Date(time).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  useEffect(() => {
    console.log("startTime ------ endTime", formatTime(startTime), formatTime(endTime));
  }, [startTime, endTime]);

  // Update selectedBarber when initialSelectedBarber changes
  useEffect(() => {
    if (initialSelectedBarber) {
      setSelectedBarber(initialSelectedBarber);
      console.log("Setting selected barber from prop:", initialSelectedBarber);

      // If we have a selected barber from props, automatically open ManageBreaks
      if (initialSelectedBarber.openManageBreaks) {
        console.log("Auto-opening ManageBreaks for barber:", initialSelectedBarber);
        setShowManageBreaks(true);
      }
    }
  }, [initialSelectedBarber]);

  const handleSaveServices = (services) => {
    setSelectedServices(services);

    // Initialize selectedServiceDurations with the newly selected services
    // This ensures we start with a clean state that matches the selected services
    const initialDurations = services.map(service => ({
      id: service.id,
      modif_duration: service.duration ? service.duration : '00:15' // Default to 15 minutes if no duration
    }));
    console.log("Initializing service durations:", initialDurations);
    setSelectedServiceDurations(initialDurations);

    setShowAddService(false);
  };

  const handleAddNew = () => {
    setShowAddNew(true);
  };

  const handleNewCustomerAdded = (customerData) => {
    setShowAddNew(false);
    if (customerData && customerData.id) {
      setSelectedCustomer(customerData.id);
      setSelectedCustomerDetails(customerData);
      // Update allcustomers list with the new customer
      setAllcustomers(prev => [customerData, ...prev]);
      setCustomerSearchText(customerData.full_name);
    }
    // If no customerData is provided (back button was clicked), just return to the main screen
  };

  const handleManageBreaks = () => {
    setShowManageBreaks(true);
  };

  const handleAddService = () => {
    setShowAddService(true);
  };

  const handleBack = () => {
    setShowAddNew(false);
    setShowAddService(false);
    setShowPreviousBook(false);
    setShowManageBreaks(false);

    // If we came from clicking a barber (has openManageBreaks flag), close the entire modal
    if (initialSelectedBarber?.openManageBreaks) {
      console.log("Closing entire modal because we came from barber click");
      onClose();
    }
  };

  if (showAddNew) {
    return <AddNewContent onBack={handleNewCustomerAdded} />;
  }

  if (showManageBreaks) {
    return <ManageBreaks
      onBack={handleBack}
      salonData={salonData}
      onClose={onClose}
      onAppointmentAdded={onAppointmentAdded}
      userRole={userRole}
      selectedBarber={selectedBarber}
    />;
  }

  if (showAddService) {
    return (
      <AddServiceContent
        onClose={handleBack}
        onSave={handleSaveServices}
        selectedServices={selectedServices}
        onUpdateDurations={setSelectedServiceDurations}
      />
    );
  }

  if (showPreviousBook) {
    return <PreviousBookContent onBack={handleBack} selectedCustomer={selectedCustomer} />;
  }

  const handleRemoveService = (id) => {
    const updatedServices = selectedServices.filter(service => service.id !== id);
    console.log("Updated services after removal:", updatedServices);
    setSelectedServices(updatedServices);

    // Also update the selectedServiceDurations to ensure removed services aren't included in the payload
    if (selectedServiceDurations && selectedServiceDurations.length > 0) {
      const updatedDurations = selectedServiceDurations.filter(service => service.id !== id);
      console.log("Updated service durations after removal:", updatedDurations);
      setSelectedServiceDurations(updatedDurations);
    }
  };

  function formatToYYYYMMDD(dateInput) {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return null;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  const handleSave = async () => {
    setLoading(true);
    try {
      // Validate customer selection
      if (!selectedCustomer) {
        Alert.alert(
          "Missing Customer",
          "Please select a customer to proceed with the appointment.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      // Validate date selection
      if (!selectedDate) {
        Alert.alert(
          "Missing Date",
          "Please select an appointment date to continue.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      // Validate start time
      if (!startTime) {
        Alert.alert(
          "Missing Start Time",
          "Please select a start time for the appointment.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      // Validate services selection
      if (!selectedServiceDurations || selectedServiceDurations.length === 0) {
        Alert.alert(
          "No Services Selected",
          "Please select at least one service for the appointment.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      // For admin role, validate barber selection
      if (userRole === 'admin' && !selectedBarber) {
        Alert.alert(
          "Missing Barber",
          "Please select a barber for the appointment.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      const formattedDate = typeof selectedDate === 'string'
        ? selectedDate
        : formatToYYYYMMDD(selectedDate);

      let payload;

      const userData = await AsyncStorage.getItem('userData');
      const data = JSON.parse(userData);
      console.log('data-->', data)
      const { user_id } = JSON.parse(userData);
      console.log('user_id-->', user_id)


      if (userRole === 'admin') {
        payload = {
          customer: selectedCustomer,
          shop: salonData?.id,
          services: selectedServiceDurations,
          barber: selectedBarber.id,
          payment_way: "cash",
          status: "booked",
          payment_id: "",
          start_time: formatTime(startTime),
          date: formattedDate,
          created_by: user_id,
          transferred_by:null

        };
      } else {
        payload = {
          customer: selectedCustomer,
          shop: shopid,
          services: selectedServiceDurations,
          barber: barbers.id,
          payment_way: "cash",
          status: "booked",
          payment_id: "",
          start_time: formatTime(startTime),
          date: formattedDate,
          created_by: user_id,
          transferred_by:null
        };
      }

      console.log('📤 Sending FormData:', payload);

      const response = await barber_appointment(payload);
      console.log('✅  RESPONSE:', response);

      if (onAppointmentAdded) {
        onAppointmentAdded();
      }
      setLoading(false);

      Alert.alert(
        'Appointment Created',
        'Your appointment has been successfully scheduled!',
        [
          {
            text: 'OK',
            onPress: () => {
              onClose();
            },
          },
        ]
      );
      onClose();
    } catch (error) {
      console.error('❌ Appointment Booking Error:', error);
      const errorMessage =
        error?.response?.data?.error || 'Something went wrong while creating the appointment. Please try again.';
      Alert.alert('Booking Error', errorMessage);

    } finally {
      setLoading(false);
    }

  };

  return (
    <>
      <View style={styles.header}>
        <ResponsiveText
          color={getTextColor()}
          size={4.5}
          weight="600"
        >
          {AppText.ADD_APPOINTMENT}
        </ResponsiveText>
        <TouchableOpacity onPress={onClose}>
          <Icon
            source={globalpath.cross}
            size={wp(6)}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: hp(7) }}
      >
        <View style={styles.buttonContainer}>
          {/* <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={handleManageBreaks}
          >
            <View style={styles.addButtonContent}>
              <Icon
                source={globalpath.add}
                size={wp(4.5)}
                tintColor={colors.white}
              />
              <ResponsiveText
                color={colors.white}
                size={4}
                weight="500"
                style={styles.addButtonText}
              >
                {AppText.MANAGE_BREAKS}
              </ResponsiveText>
            </View>
          </TouchableOpacity> */}
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.c_green }]}
            onPress={handleAddService}
          >
            <View style={styles.addButtonContent}>
              <Icon
                source={globalpath.add}
                size={wp(4.5)}
                tintColor={colors.white}
              />
              <ResponsiveText
                color={colors.white}
                size={4}
                weight="500"
                style={styles.addButtonText}
              >
                {AppText.ADD_SERVICE}
              </ResponsiveText>
            </View>
          </TouchableOpacity>
        </View>

        <CustomerNameDropdown
          value={selectedCustomer}
          onChange={handleCustomerChange}
          searchText={customerSearchText}
          setSearchText={setCustomerSearchText}
          onShowPreviousBook={() => setShowPreviousBook(true)}
          setAllcustomers={setAllcustomers}
          allcustomers={allcustomers}
          handleAddNew={handleAddNew}
        />

        {selectedCustomerDetails && (
          <>
            <View style={[styles.infoContainer, { borderColor: getDark_Theme() }]}>
              <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[0, 0, hp(1), 0]}>
                {AppText.EMAIL}
              </ResponsiveText>
              <View style={[styles.disabledInput, { borderColor: getDark_Theme() }]}>
                <ResponsiveText color={getTextColor()} size={3.8}>
                  {selectedCustomerDetails.email}
                </ResponsiveText>
              </View>
            </View>

            <View style={[styles.infoContainer, { borderColor: getDark_Theme() }]}>
              <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[0, 0, hp(1), 0]}>
                {AppText.PHONE_NUMBER}
              </ResponsiveText>
              <View style={[styles.disabledInput, { borderColor: getDark_Theme() }]}>
                <ResponsiveText color={getTextColor()} size={3.8}>
                  {selectedCustomerDetails.phone_number}
                </ResponsiveText>
              </View>
            </View>
          </>
        )}

        {userRole === 'admin' && (
          <BreaksBarberDropdown
            shopId={salonData?.id}
            onSelect={setSelectedBarber}
            salonData={salonData}
            initialBarber={selectedBarber}
          />
        )}

        <DateInput
          value={selectedDate}
          onChange={setSelectedDate}
        />

        <TimeInput
          startTime={startTime}
          endTime={endTime}
          onStartTimeChange={setStartTime}
          onEndTimeChange={setEndTime}
        />

        {selectedServices.length > 0 && (
          <View style={styles.servicesCard}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="600"
              margin={[0, 0, hp(1), 0]}
            >
              {AppText.SELECTED_SERVICES}
            </ResponsiveText>
            <SelectedServicesList
              services={selectedServices}
              getTextColor={getTextColor}
              onRemoveService={handleRemoveService}
              onUpdateDurations={setSelectedServiceDurations}
            />
          </View>
        )}

        <TouchableOpacity style={styles.button} onPress={handleSave}>
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <ResponsiveText
              color={colors.white}
              size={4.5}
              weight="600"
            >
              {AppText.SAVE}
            </ResponsiveText>
          )}
        </TouchableOpacity>
      </ScrollView>
    </>
  );
};

export default AddAppointmentContent;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
    marginTop: hp(1.5),
  },
  scrollView: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: hp(2),
    marginBottom: hp(1),
    // backgroundColor: 'red',
    // marginHorizontal: wp(2),
  },
  addButton: {
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(0.5),
    borderRadius: wp(1),
    marginLeft:wp(3)
  },
  addButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start'
  },
  addButtonText: {
    marginLeft: wp(2),
  },
  confirmButton: {
    padding: wp(2),
    borderRadius: wp(6)
  },
  servicesCard: {
    borderColor: colors.grey,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
  },
  serviceItem: {
    marginBottom: hp(1),
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
    marginBottom: hp(1),
  },
  serviceName: {
    flex: 1,
    marginRight: wp(2),
  },
  servicePriceContainer: {
    alignItems: 'flex-end',
  },
  button: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
  },
  infoContainer: {
    marginBottom: hp(2),
  },
  disabledInput: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(4),
    justifyContent: 'center',
  },
});