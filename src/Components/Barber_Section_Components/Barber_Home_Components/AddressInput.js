import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const AddressInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.ADDRESS}
      placeholder={AppText.ENTER_ADDRESS}
      value={value}
      onChangeText={onChangeText}
      multiline={true}
      numberOfLines={2}
    />
  );
};

export default AddressInput; 