import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import Modal from 'react-native-modal';
import { wp, hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import AddAppointmentContent from './AddAppointmentContent';

const AddAppointmentModal = ({ visible, onClose, selectedDate, setSelectedDate, onAppointmentAdded, salonData, userRole, selectedBarber }) => {
  const { backgroundColor, getDark_Theme } = useTheme();
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  console.log("AddAppointmentModal received selectedBarber:", selectedBarber);

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={[styles.container, { backgroundColor }]}>
        {/* Drag Bar */}
        <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />

        <AddAppointmentContent
          onClose={onClose}
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          onAppointmentAdded={onAppointmentAdded}
          salonData={salonData}
          userRole={userRole}
          selectedBarber={selectedBarber}
        />
      </View>
    </Modal>
  );
};

export default AddAppointmentModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    height: '70%'
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
    marginBottom: hp(1),
  }
});