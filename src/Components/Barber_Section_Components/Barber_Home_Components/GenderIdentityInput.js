import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const GenderIdentityInput = ({ value, onChange }) => {
  const AppText = useAppText();
  const { getTextColor, getDark_Theme, backgroundColor } = useTheme();

  const options = [
    { label: AppText.MALE, value: 'male' },
    { label: AppText.FEMALE, value: 'female' }
  ];

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.GENDER_IDENTITY}
      </ResponsiveText>
      <View style={[styles.optionsContainer, { borderColor: getDark_Theme() }]}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.option,
              {
                backgroundColor: value === option.value ? colors.Light_theme_maincolour : backgroundColor,
                borderColor: getDark_Theme(),
              }
            ]}
            onPress={() => onChange(option.value)}
          >
            <Icon
              source={option.value === 'male' ? globalpath.male : globalpath.female}
              size={wp(5)}
              tintColor={value === option.value ? colors.white :colors.darkGrey}
            />
            <ResponsiveText
              color={value === option.value ? colors.white : getTextColor()}
              size={3.8}
              style={styles.optionText}
            >
              {option.label}
            </ResponsiveText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default GenderIdentityInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  option: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1.5),
    borderRightWidth: 1,
  },
  optionText: {
    marginLeft: wp(2),
  },
}); 