import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const NoteInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.NOTE}
      placeholder={AppText.ENTER_NOTE}
      value={value}
      onChangeText={onChangeText}
      multiline={true}
      numberOfLines={4}
    />
  );
};

export default NoteInput; 