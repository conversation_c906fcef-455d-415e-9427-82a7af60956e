import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import AppointmentSlot from './AppointmentSlot';
import useTheme from '../../../Redux/useTheme';


const timeSlots = Array.from({ length: 12 }, (_, i) => {
  const hour = Math.floor(i / 2) + 10;
  const minute = i % 2 === 0 ? '00' : '30';
  return {
    time: `${hour}:${minute}`,
    hour,
    minute: minute === '00' ? 0 : 30
  };
});

const getTimeSlotSpan = (appointment) => {
  const startParts = appointment.startTime.split(':').map(Number);
  const endParts = appointment.endTime.split(':').map(Number);
  const startMinutes = startParts[0] * 60 + (startParts[1] || 0);
  const endMinutes = endParts[0] * 60 + (endParts[1] || 0);
  return Math.ceil((endMinutes - startMinutes) / 30);
};

const TimeSlotGrid = ({ currentBarbers, appointments }) => {
  const getAppointmentsForTimeSlot = (timeSlot, barberIndex) => {
    const barber = currentBarbers[barberIndex];
    return appointments.filter(apt => {
      if (apt.barber !== barber) return false;
      
      const [startHour, startMinute] = apt.startTime.split(':').map(Number);
      const [endHour, endMinute] = apt.endTime.split(':').map(Number);
      const slotMinutes = timeSlot.hour * 60 + timeSlot.minute;
      const startMinutes = startHour * 60 + (startMinute || 0);
      const endMinutes = endHour * 60 + (endMinute || 0);
      
      return slotMinutes >= startMinutes && slotMinutes < endMinutes;
    });
  };

  const {getDark_Theme} = useTheme()
  return (
    <ScrollView style={styles.container}>
      {timeSlots.map((timeSlot) => (
        <View key={timeSlot.time} style={[styles.timeSlotRow,{borderBottomColor:getDark_Theme(),borderTopColor:getDark_Theme()}]}>
          <View style={[styles.timeColumn,{borderRightColor:getDark_Theme()}]}>
            <ResponsiveText size={3} color={colors.grey} textAlign={'center'} >
              {timeSlot.time}
              {timeSlot.minute === 0 ? '\nAM' : ''}
            </ResponsiveText>
          </View>
          <View style={styles.appointmentsRow}>
            {[0, 1].map(barberIndex => (
              <View key={barberIndex} style={[
                styles.appointmentColumn,
                barberIndex === 0 && [styles.appointmentColumnWithBorder,{borderRightColor:getDark_Theme()}]
              ]}>
                {getAppointmentsForTimeSlot(timeSlot, barberIndex).map(apt => (
                  <AppointmentSlot
                    key={`${apt.customerName}-${apt.startTime}`}
                    appointment={apt}
                    style={{
                      height: hp(7 * getTimeSlotSpan(apt))
                    }}
                  />
                ))}
              </View>
            ))}
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  timeSlotRow: {
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopWidth: 0.5,

    minHeight: hp(8),
  },
  timeColumn: {
    width: wp(18),
    justifyContent: 'center',
    borderRightWidth: 0.5,
  },
  appointmentsRow: {
    flex: 1,
    flexDirection: 'row',
  },
  appointmentColumn: {
    flex: 1,
    padding: wp(1),
  },
  appointmentColumnWithBorder: {
    borderRightWidth: 0.5,
    // borderRightColor: colors.grey,
  },
});

export default TimeSlotGrid;
