import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Modal,
  FlatList,
  Image,
  Text
} from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';

const ShopSelector = ({
  currentShop,
  allShops,
  onShopSelect,
  isTouchable = true
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const { getTextColor, backgroundColor, getDark_Theme } = useTheme();
  const [selectedShop, setSelectedShop] = useState(currentShop);

  useEffect(() => {
    if (currentShop) {
      setSelectedShop(currentShop);
    }
  }, [currentShop]);

  const handleShopSelect = (shop) => {
    setSelectedShop(shop);
    onShopSelect(shop);
    setModalVisible(false);
  };

  const renderShopItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.shopItem,
        selectedShop?.id === item.id && styles.selectedShopItem
      ]}
      onPress={() => handleShopSelect(item)}
    >
      <Image
        source={
          item.shop_image
            ? { uri: item.shop_image }
            : globalpath.logo
        }
        style={styles.shopImage}
      />
      <View style={styles.shopInfo}>
        <ResponsiveText
          size={3.8}
          weight={selectedShop?.id === item.id ? "600" : "400"}
          color={selectedShop?.id === item.id ? colors.Light_theme_maincolour : getTextColor()}
        >
          {item.name}
        </ResponsiveText>
        <ResponsiveText
          size={3.2}
          color={colors.grey}
        >
          {item.address_city || '-'}
        </ResponsiveText>
      </View>
      {selectedShop?.id === item.id && (
        <Icon
          source={globalpath.check}
          size={wp(5)}
          tintColor={colors.Light_theme_maincolour}
        />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selectorButton,
          { borderColor: getDark_Theme() },
          !isTouchable && styles.disabledSelector
        ]}
        onPress={isTouchable ? () => setModalVisible(true) : null}
        disabled={!isTouchable}
      >
        <Image
          source={
            selectedShop?.shop_image
              ? { uri: selectedShop.shop_image }
              : globalpath.logo
          }
          style={styles.currentShopImage}
        />
        <ResponsiveText
          size={3.8}
          weight="500"
          color={getTextColor()}
          style={styles.shopName}
          numberOfLines={1}
        >
          {selectedShop?.name || "Select Shop"}
        </ResponsiveText>
        {isTouchable ? (
          <Icon
            source={globalpath.down_arrow}
            size={wp(3.5)}
            tintColor={getTextColor()}
          />
        ) : null}
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              { backgroundColor: backgroundColor }
            ]}
          >
            <View style={styles.modalHeader}>
              <ResponsiveText
                size={4}
                weight="600"
                color={getTextColor()}
              >
                Select Shop
              </ResponsiveText>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Icon
                  source={globalpath.close}
                  size={wp(5)}
                  tintColor={getTextColor()}
                />
              </TouchableOpacity>
            </View>

            <FlatList
              data={allShops}
              renderItem={renderShopItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.shopList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => (
                <View style={styles.emptyList}>
                  <ResponsiveText
                    size={4}
                    color={colors.grey}
                  >
                    No shops available
                  </ResponsiveText>
                </View>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default ShopSelector;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: wp(2),
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(6),
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.8),
    backgroundColor: 'transparent',
  },
  disabledSelector: {
    opacity: 0.9,
  },
  currentShopImage: {
    width: wp(6),
    height: wp(6),
    borderRadius: wp(3),
    marginRight: wp(2),
  },
  shopName: {
    flex: 1,
    marginRight: wp(2),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    paddingBottom: hp(4),
    maxHeight: hp(70),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  shopList: {
    paddingHorizontal: wp(4),
  },
  shopItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1.5),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  selectedShopItem: {
    backgroundColor: colors.lightGrey,
  },
  shopImage: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    marginRight: wp(3),
  },
  shopInfo: {
    flex: 1,
  },
  emptyList: {
    alignItems: 'center',
    paddingVertical: hp(4),
  },
});
