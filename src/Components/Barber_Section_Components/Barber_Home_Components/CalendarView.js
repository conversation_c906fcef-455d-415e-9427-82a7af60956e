import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Text, Dimensions, TouchableOpacity, Image } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';
import { wp, hp } from '../../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';
import * as Animatable from 'react-native-animatable';
import useAppText from '../../../Custom/AppText';
import { globalpath } from '../../../Custom/globalpath';
import Icon from '../../../Custom/Icon';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

const TIME_SLOTS = Array.from({ length: 48 }, (_, i) => {
  const baseHour = 8; // Starting from 8 AM
  const totalHours = Math.floor(i / 2);
  const hour = (baseHour + totalHours) % 24;
  const minute = (i % 2) * 30;
  const suffix = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return `${displayHour}:${minute.toString().padStart(2, '0')} ${suffix}`;
});

// Define a fixed slot height for consistency across all devices
const SLOT_HEIGHT = 80; // Fixed height in pixels for each 30-minute slot
const QUARTER_SLOT_HEIGHT = SLOT_HEIGHT / 2; // Height for 15-minute intervals

const getTimeIndex = (timeStr) => {
  // Convert time string like "08:00:00" to index
  const [hours, minutes] = timeStr.split(':').map(Number);
  const totalMinutes = hours * 60 + minutes;
  const baseMinutes = 8 * 60; // 8 AM in minutes
  return ((totalMinutes - baseMinutes) / 15); // Each slot is 15 minutes for proper positioning
};

const getDarkerShade = (color) => {
  if (!color) return colors.c_green;
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const darkerR = Math.floor(r * 0.8);
  const darkerG = Math.floor(g * 0.8);
  const darkerB = Math.floor(b * 0.8);
  return `#${darkerR.toString(16).padStart(2, '0')}${darkerG.toString(16).padStart(2, '0')}${darkerB.toString(16).padStart(2, '0')}`;
};

const TimeSlot = ({ time }) => {
  const { getTextColor } = useTheme();
  return (
    <View style={styles.timeSlotContainer}>
      <ResponsiveText color={getTextColor()} size={3} weight={'500'}>{time}</ResponsiveText>
      <View style={styles.darkLine} />
      <View style={[styles.lightLine, { backgroundColor: colors.iconcolour }]} />
    </View>
  )
}

const BreakSlot = ({ breakData }) => {
  const { getDark_Theme } = useTheme();
  const AppText = useAppText();
  const startIndex = getTimeIndex(breakData.start_time);
  const endIndex = getTimeIndex(breakData.end_time);
  const duration = endIndex > -1 && startIndex > -1 ? (endIndex - startIndex) * (SLOT_HEIGHT / 2) : (SLOT_HEIGHT / 2);

  // Calculate if this is a short break (less than 30 minutes)
  const isShortBreak = duration < (SLOT_HEIGHT / 2 + 1);

  return (
    <View
      style={[
        styles.breakCard,
        {
          borderColor: getDark_Theme(),
          top: startIndex * (SLOT_HEIGHT / 2) + 40, // Fixed 40px offset for consistent alignment
          height: duration - 5, // Minimum height for breaks
        },
      ]}
    >
      {isShortBreak ? (
        // Horizontal layout for short breaks
        <View style={styles.shortBreakContent}>
          <ResponsiveText
            size={2.5}
            weight="600"
            color={colors.greyBlack}
            numberOfLines={1}
          >
            {breakData.status.toUpperCase()}
          </ResponsiveText>
          <ResponsiveText
            size={2.2}
            color={colors.grey}
            numberOfLines={1}
            style={styles.shortBreakText}
          >
            {AppText.NOT_AVAILABLE}
          </ResponsiveText>
        </View>
      ) : (
        // Vertical layout for normal breaks
        <>
          <ResponsiveText
            size={3.5}
            weight="600"
            color={colors.greyBlack}
            numberOfLines={1}
            textAlign={"center"}
          >
            {breakData.status.toUpperCase()}
          </ResponsiveText>
          <ResponsiveText
            size={2.9}
            color={colors.grey}
            numberOfLines={1}
            textAlign={"center"}
          >
            {AppText.NOT_AVAILABLE}
          </ResponsiveText>
        </>
      )}
    </View>
  );
};

const CalendarView = ({ currentBarbers, appointments, breaks, selectedDate }) => {
  console.log("breaks inside the calendar view ", breaks)
  const { getTextColor, backgroundColor } = useTheme();
  const navigation = useNavigation();
  const [userRole, setUserRole] = useState(null);

  useEffect(() => {
    const getUserRole = async () => {
      try {
        const userData = await AsyncStorage.getItem('userData');
        if (userData) {
          const { role } = JSON.parse(userData);
          setUserRole(role);
        }
      } catch (error) {
        console.error('Error getting user role:', error);
      }
    };

    getUserRole();
  }, []);

  // Get current date in YYYY-MM-DD format
  const getCurrentDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Format date to YYYY-MM-DD
  const formatToYYYYMMDD = (dateInput) => {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return getCurrentDate();

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  const handlecardpress = (appointment) => {
    console.log("Card Pressed", appointment);
    navigation.navigate('Barber_Pendings_Jobs', { appointment });
  }

  // Function to format time from "HH:MM:SS" to "HH:MM AM/PM"
  const formatTime = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${suffix}`;
  };

  // Function to get color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'booked': return '#E1EBEE';
      case 'completed': return '#E8F5E9';
      case 'cancelled': return '#FFEBEE';
      default: return '#E1EBEE';
    }
  };

  // Filter breaks for selected date
  const dateToFilter = selectedDate ? formatToYYYYMMDD(selectedDate) : getCurrentDate();
  console.log("Filtering breaks for date:", dateToFilter);
  const currentDateBreaks = breaks.filter(breakItem => breakItem.date === dateToFilter);

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: backgroundColor }}
      showsVerticalScrollIndicator={false}
      nestedScrollEnabled={true}
    >
      <View style={styles.container}>
        <View style={styles.timeColumn}>
          {TIME_SLOTS.map((slot, index) => (
            <TimeSlot key={index} time={slot} />
          ))}
        </View>

        {currentBarbers.map((barberName, index) => (
          <View key={index} style={[
            styles.userColumn,
            { width: currentBarbers.length === 1 ? '80%' : '40%' }
          ]}>
            <View style={styles.userHeader} />
            {TIME_SLOTS.map((_, i) => (
              <View key={i} style={styles.slotWithLines}>
                <View style={styles.darkLine} />
                <View style={[styles.lightLine, { backgroundColor: colors.iconcolour }]} />
              </View>
            ))}

            {appointments
              .filter(appt => appt.barber_name === barberName)
              .map((appointment, i) => {
                const startIndex = getTimeIndex(appointment.start_time);
                const endIndex = getTimeIndex(appointment.end_time);
                const duration = endIndex > -1 && startIndex > -1 ? (endIndex - startIndex) * (SLOT_HEIGHT / 2) : (SLOT_HEIGHT / 2);

                // Calculate if this is a short appointment (less than 30 minutes)
                const isShortAppointment = duration < (SLOT_HEIGHT / 2 + 1);

                return (
                  <TouchableOpacity
                    onPress={() => handlecardpress(appointment)}
                    activeOpacity={0.8}
                    key={i}
                    style={[
                      styles.eventCard,
                      {
                        top: startIndex * (SLOT_HEIGHT / 2) + 40, // Fixed 40px offset for consistent alignment
                        height: duration - 5, // Minimum height for appointments
                        backgroundColor: (appointment.status === "completed" && appointment.payment_recieved === true) ? colors.calendar :
                          (appointment.new_customer === true ? colors.c_green : colors.Light_theme_maincolour),
                        borderLeftColor: getDarkerShade((appointment.status === "completed" && appointment.payment_recieved === true) ? colors.calendar :
                          (appointment.new_customer === true ? colors.c_green : colors.Light_theme_maincolour)),
                      },
                    ]}
                  >

                    <View style={styles.appointmentCardContainer}>
                      {/* Icons at top right */}
                      <View style={styles.appointmentIcons}>
                        <Icon
                          source={globalpath.calendar_insurance}
                          size={wp(3.9)}
                          tintColor={'white'}
                          style={[
                            styles.appointmentIcon,
                            {bottom:wp(0.3)},
                            appointment.payment_way === 'online' ? styles.iconProminent : styles.iconDimmed
                          ]}
                        />
                        <Icon
                          source={globalpath.calendar_user}
                          size={wp(3.5)}
                          tintColor={'white'}
                          style={[
                            styles.appointmentIcon,
                            appointment.customer_appointment ? styles.iconProminent : styles.iconDimmed
                          ]}
                        />
                      </View>

                      {/* Appointment content */}
                      <View >
                        <ResponsiveText
                          size={isShortAppointment ? 3 : 3.5}
                          weight="500"
                          color={(appointment.status === "completed" && appointment.payment_recieved === true) ? colors.greyBlack : colors.white}
                          numberOfLines={userRole === 'admin' ? 1 : undefined}
                          maxWidth={userRole === 'admin' ? wp(20) : undefined}
                          marginTop={isShortAppointment ? -wp(1.5) : 0}
                        >
                          {appointment.customer_name}
                        </ResponsiveText>
                        {!isShortAppointment && (
                          <View>
                            {appointment.service_name && appointment.service_name.map((service, index) => (
                              <View key={index} style={styles.serviceContainer}>
                                <View
                                  style={[
                                    styles.serviceColorSquare,
                                    { backgroundColor: service.color && service.color !== "" && service.color !== null
                                      ? service.color
                                      : `#${Math.floor(Math.random()*16777215).toString(16)}`
                                    }
                                  ]}
                                />
                                <ResponsiveText
                                  size={3}
                                  color={(appointment.status === "completed" && appointment.payment_recieved === true) ? colors.greyBlack : colors.white}
                                  numberOfLines={1}
                                >
                                  {service.name || 'Service'}
                                </ResponsiveText>
                              </View>
                            ))}
                          </View>
                        )}
                        {isShortAppointment && (
                          <View>
                            {appointment.service_name && appointment.service_name.length > 0 ? (
                              <View style={styles.shortServiceContainer}>
                                <View
                                  style={[
                                    styles.serviceColorSquare,
                                    { backgroundColor: appointment.service_name[0].color && appointment.service_name[0].color !== "" && appointment.service_name[0].color !== null
                                      ? appointment.service_name[0].color
                                      : `#${Math.floor(Math.random()*16777215).toString(16)}`
                                    }
                                  ]}
                                />
                                <ResponsiveText
                                  size={2.5}
                                  color={(appointment.status === "completed" && appointment.payment_recieved === true) ? colors.greyBlack : colors.white}
                                  numberOfLines={1}
                                  style={styles.shortServiceText}
                                >
                                  {appointment.service_name.length > 1
                                    ? `${appointment.service_name[0].name} +${appointment.service_name.length - 1}`
                                    : appointment.service_name[0].name || 'Service'}
                                </ResponsiveText>
                              </View>
                            ) : (
                              <View style={styles.shortServiceContainer}>
                                <View style={styles.serviceColorSquare} />
                                <ResponsiveText
                                  size={2.5}
                                  color={(appointment.status === "completed" && appointment.payment_recieved === true) ? colors.greyBlack : colors.white}
                                  numberOfLines={1}
                                  style={styles.shortServiceText}
                                >
                                  Service
                                </ResponsiveText>
                              </View>
                            )}
                          </View>
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                );
              })}

            {/* Render break slots for selected date */}
            {currentDateBreaks
              .filter(breakItem => breakItem.barber_name === barberName)
              .map((breakItem, i) => (
                <BreakSlot key={`break-${i}`} breakData={breakItem} />
              ))}
          </View>
        ))}
      </View>
    </ScrollView>
  );
};


const styles = StyleSheet.create({
  container: { flexDirection: 'row' },
  timeColumn: {
    width: wp(17.5),
    paddingTop: 40, // Fixed padding for consistent alignment
  },
  timeSlotContainer: {
    height: SLOT_HEIGHT,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingTop: 4,
    paddingLeft: wp(2),
    position: 'relative'
  },
  timeText: { fontSize: 12, },
  darkLine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 1.5,
    backgroundColor: colors.darkGrey
  },
  lightLine: {
    position: 'absolute',
    top: SLOT_HEIGHT / 2, // Exactly half of the slot height
    left: 0,
    right: 0,
    height: 1,
    // backgroundColor: colors.grey
  },
  slotWithLines: {
    height: SLOT_HEIGHT,
    position: 'relative'
  },
  userColumn: {
    // width: width / 2,
    borderLeftWidth: 1,
    borderColor: '#ccc',
    position: 'relative',
    // backgroundColor:"pink"
    width: "40%",

  },
  userHeader: {
    height: 40, // Keep this fixed at 40px for consistency
    // justifyContent: 'center',
    // alignItems: 'center',
    // backgroundColor: '#f4f4f4',
    // borderBottomWidth: 1,
    // borderColor: '#ccc'
  },
  userText: { fontWeight: 'bold' },
  eventCard: {
    position: 'absolute',
    left: wp(1.2),
    right: wp(1.2),
    borderRadius: wp(0.9),
    padding: wp(2.5),
    justifyContent: 'center',
    borderLeftWidth: 3,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    marginTop:hp(0.35)


  },
  eventLabel: {
    marginBottom: hp(0.5)
  },
  eventService: {
    opacity: 0.8
  },
  breakCard: {
    position: 'absolute',
   left: wp(1.2),
    right: wp(1.2),
    borderRadius: wp(0.9),
    padding: wp(2.5),
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    // borderColor: colors.grey,
    backgroundColor: '#F5F5F5',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    marginTop: hp(0.35),
    // backgroundColor:"red"
  },
  shortAppointmentContent: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
    paddingHorizontal: wp(0.5),
  },
  shortServiceText: {
    marginLeft: wp(1),
    fontStyle: 'italic'
  },
  shortBreakContent: {
    paddingHorizontal: wp(0.5),
  },
  shortBreakText: {
    marginLeft: wp(1),
    fontStyle: 'italic'
  },
  appointmentCardContainer: {
    flex: 1,
    position: 'relative',
  },
  appointmentIcons: {
    position: 'absolute',
    top: 0,
    right: 0,
    flexDirection: 'row',
    zIndex: 1,
  },
  appointmentIcon: {
    marginLeft: wp(1),
    // backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: wp(1),
    // padding: wp(0.5),
  },
  iconProminent: {
    opacity: 1,
  },
  iconDimmed: {
    opacity: 0.3,
  },
  serviceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp(0.5),
  },
  shortServiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceColorSquare: {
    width: wp(2),
    height: wp(2),
    marginRight: wp(1),
    borderRadius: wp(0.5),
  }
});

export default CalendarView;
