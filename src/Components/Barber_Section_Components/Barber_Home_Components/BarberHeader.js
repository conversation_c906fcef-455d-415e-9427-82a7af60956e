import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import useTheme from '../../../Redux/useTheme';


const BarberHeader = ({ currentBarbers, onPrevious, onNext, onBarberPress, userRole, barberObjects, hasMoreBarbers, hasPreviousBarbers }) => {
  const { getDark_Theme, backgroundColor, getTextColor , barber_header} = useTheme();

  // Determine if we should show one or two barbers based on user role
  const showTwoBarbers = userRole === 'admin' && currentBarbers.length > 1;

  return (
    <View style={[styles.container, {backgroundColor: backgroundColor}]}>
      <TouchableOpacity
        onPress={hasPreviousBarbers ? onPrevious : null}
        style={[
          [styles.navigationButton,{ backgroundColor: barber_header(),}],
          {
            borderColor: getDark_Theme(),
            opacity: hasPreviousBarbers ? 1 : 0.3
          }
        ]}
        disabled={!hasPreviousBarbers}
      >
        <Icon
          source={globalpath.Right}
          size={wp(3.5)}
          tintColor={getTextColor()}
        />
      </TouchableOpacity>
      <View style={styles.barberNamesContainer}>
        <View style={{
          flexDirection: 'row',
          width: '100%',
                 backgroundColor: barber_header(),
          justifyContent: 'center'
        }}>
          {currentBarbers.map((barber, index) => {
            // Skip rendering the second barber if not showing two barbers
            if (index > 0 && !showTwoBarbers) return null;

            return (
              <View
                key={barber}
                style={[
                  styles.barberNameWrapper,
                  {
                    width: showTwoBarbers ? '50%' : '100%',
                    backgroundColor: 'transparent' // Remove background from individual wrappers
                  }
                ]}
              >
                <TouchableOpacity
                  style={styles.barberNameTouchable}
                  onPress={() => onBarberPress && onBarberPress(barber)}
                >
                  <ResponsiveText size={4} weight="500" style={styles.barberName} numberOfLines={1} color={getTextColor()}>
                    {barber}
                  </ResponsiveText>
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      </View>
      <TouchableOpacity
        onPress={hasMoreBarbers ? onNext : null}
        style={[
                 [styles.navigationButton,{ backgroundColor: barber_header(),}],
          {
            borderColor: getDark_Theme(),
            opacity: hasMoreBarbers ? 1 : 0.3
          }
        ]}
        disabled={!hasMoreBarbers}
      >
        <Icon
          source={globalpath.left}
          size={wp(3.5)}
            tintColor={getTextColor()}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(2),
    paddingHorizontal: wp(4),
    backgroundColor: colors.white,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.grey,

  },
  navigationButton: {
    padding: wp(2),
    backgroundColor:"#E9ECF2",
borderWidth:1,
    borderRadius: wp(5),
  },
  barberNamesContainer: {
    flexDirection: 'row',
    flex: 1,
    marginHorizontal: wp(4),
    justifyContent: 'center',
  },
  barberNameWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor:"#E9ECF2",
    paddingHorizontal:wp(2),
    paddingVertical:hp(0.9),
    // No margin or border radius to ensure no spacing between barbers
  },
  barberName: {
    textAlign: 'center',
  },
  barberNameTouchable: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // divider: {
  //   width: 1,
  //   height: '100%',
  //   backgroundColor: colors.grey,
  //   marginHorizontal: wp(2),
  // },
});

export default BarberHeader;
