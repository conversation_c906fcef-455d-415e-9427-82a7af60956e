import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, Alert, ActivityIndicator } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import BreaksBarberDropdown from '../../Admin_Appointment_Component/breaks_barber_dropdown';
import BreaksTimeInput from '../../Admin_Appointment_Component/breaks_time_input';
import BreaksDateInput from '../../Admin_Appointment_Component/breaks_date_input';
import CustomTextInput from '../../CustomTextInput';
import { get_barber_breaks, get_barber_deatils, Manage_breaks } from '../../../Services/API/Endpoints/barber/dashboard';
import StatusDropdown from '../../Admin_Appointment_Component/StatusDropdown';

const ManageBreaks = ({ onBack, salonData, onClose, onAppointmentAdded, userRole, selectedBarber: initialSelectedBarber }) => {
  console.log("userRole in manage breaks", userRole)
  console.log("salonData in manage breaks", salonData?.id)
  console.log("initialSelectedBarber in manage breaks", initialSelectedBarber)
  const AppText = useAppText();
  const { getTextColor, getDark_Theme } = useTheme();
  const [shopId, setShopId] = useState(null);
  const [selectedBarber, setSelectedBarber] = useState(initialSelectedBarber || null);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [status, setStatus] = useState('');
  const [barberDetails, setBarberDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  // Store whether we came from a barber click (has openManageBreaks flag)
  const [cameFromBarberClick] = useState(initialSelectedBarber?.openManageBreaks || false);

  useEffect(() => {
    if (userRole === 'barber') {
      fetchBarberDetails();
    }
  }, [userRole]);

  // Set the selected barber when initialSelectedBarber changes
  useEffect(() => {
    console.log("initialSelectedBarber changed:", initialSelectedBarber);
    if (initialSelectedBarber) {
      console.log("Setting selectedBarber to:", initialSelectedBarber);
      setSelectedBarber(initialSelectedBarber);
    }
  }, [initialSelectedBarber]);

  const fetchBarberDetails = async () => {
    try {
      const response = await get_barber_deatils();
      console.log("Barber details:", response);
      if (response && response.length > 0) {
        setBarberDetails(response[0]);
        setShopId(response[0].shop);
      }
    } catch (error) {
      console.error('Failed to fetch barber details:', error);
    }
  };

  function formatToYYYYMMDD(dateInput) {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return null;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  const formatTime = (time) => {
    if (!time) return '';
    return new Date(time).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const handleSave = async () => {
    setLoading(true); // Start loading

    try {
      // Validate barber selection for admin role
      if (userRole === 'admin' && !selectedBarber) {
        Alert.alert(
          "Missing Barber",
          "Please select a barber to proceed.",
          [{ text: "OK" }]
        );
        setLoading(false); // End loading

        return;
      }

      // Validate date selection
      if (!selectedDate) {
        Alert.alert(
          "Missing Date",
          "Please select a break date to continue.",
          [{ text: "OK" }]
        );
        setLoading(false); // End loading

        return;
      }

      // Validate start time
      if (!startTime) {
        Alert.alert(
          "Missing Start Time",
          "Please select a start time for the break.",
          [{ text: "OK" }]
        );
        setLoading(false); // End loading

        return;
      }

      // Validate end time
      if (!endTime) {
        Alert.alert(
          "Missing End Time",
          "Please select an end time for the break.",
          [{ text: "OK" }]
        );
        setLoading(false); // End loading

        return;
      }

      // Validate status
      if (!status || typeof status !== 'string') {
        Alert.alert(
          "Missing Status",
          "Please provide a valid status for the break.",
          [{ text: "OK" }]
        );
        setLoading(false); // End loading

        return;
      }

      let payload;
      if (userRole === 'admin') {
        payload = {
          shop: selectedBarber.shop || salonData?.id,
          barber: selectedBarber.id,
          start_time: formatTime(startTime),
          end_time: formatTime(endTime),
          status: status,
          date: typeof selectedDate === 'string' ? selectedDate : formatToYYYYMMDD(selectedDate),
        };
      } else {
        // For barber role
        payload = {
          shop: barberDetails.shop,
          barber: barberDetails.id,
          start_time: formatTime(startTime),
          end_time: formatTime(endTime),
          status: status,
          date: typeof selectedDate === 'string' ? selectedDate : formatToYYYYMMDD(selectedDate),
        };
      }

      console.log('📤 Sending FormData:', payload);

      const response = await Manage_breaks(payload);
      console.log('✅ Response of manage_breaks:', response);
      if (onAppointmentAdded) {
        onAppointmentAdded();
      }

      // If we came from a barber click, close the parent modal
      if (cameFromBarberClick) {
        console.log("Closing parent modal because we came from barber click");
        onClose(); // This will close the parent modal
      } else {
        // Just close the ManageBreaks component
        onBack();
      }

      Alert.alert(
        'Break Scheduled',
        'Your break has been successfully scheduled!',
        [
          {
            text: 'OK',
            onPress: () => {
              // No need to do anything here as we've already closed the appropriate components
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Error scheduling break:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false); // End loading
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
      keyboardVerticalOffset={Platform.OS === 'ios' ? hp(28) : 0}
    >
      <View style={[styles.header, { borderBottomColor: getDark_Theme() }]}>
        <TouchableOpacity
          onPress={() => {
            // If we came from a barber click, close the parent modal
            if (cameFromBarberClick) {
              console.log("Closing parent modal from back button because we came from barber click");
              onClose(); // This will close the parent modal
            } else {
              // Just close the ManageBreaks component
              onBack();
            }
          }}
        >
          <Icon
            source={globalpath.goback}
            size={wp(4.5)}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>
        <View style={styles.titleContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={4.5}
            weight="600"
            style={styles.titleText}
          >
            {AppText.MANAGE_BREAKS}
          </ResponsiveText>
        </View>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {userRole === 'admin' && (
          <BreaksBarberDropdown
            shopId={shopId}
            onSelect={setSelectedBarber}
            salonData={salonData}
            initialBarber={selectedBarber}
          />
        )}

        <BreaksTimeInput
          startTime={startTime}
          endTime={endTime}
          onStartTimeChange={setStartTime}
          onEndTimeChange={setEndTime}
        />

        <BreaksDateInput
          value={selectedDate}
          onChange={setSelectedDate}
          salonData={salonData}
        />

        <StatusDropdown onSelect={setStatus} />

        <TouchableOpacity style={styles.button} onPress={handleSave}>
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <ResponsiveText
              color={colors.white}
              size={4.5}
              weight="600"
          >
              {AppText.SAVE}
            </ResponsiveText>
          )}
        </TouchableOpacity>

      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ManageBreaks;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
    marginTop: hp(1.5),
    borderBottomWidth: 1,
    paddingBottom: hp(1),
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  titleText: {
    textAlign: 'center',
  },
  placeholder: {
    width: wp(6),
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: wp(4),
    paddingBottom: hp(4),
  },
  button: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    marginTop: hp(2),
  },
});