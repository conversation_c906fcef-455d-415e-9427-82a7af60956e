import React, { useCallback, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, FlatList, ActivityIndicator, RefreshControl } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import DashedLine from '../../CustomerComponents/Book_Now_Components/DashedLine';
import { colors } from '../../../Custom/Colors';
import { get_appointment_history } from '../../../Services/API/Endpoints/barber/dashboard';
import { useFocusEffect } from '@react-navigation/native';
import moment from 'moment';

const PreviousBookContent = ({ onBack, selectedCustomer }) => {
  const { backgroundColor ,getDark_Theme} = useTheme();
  const [appointmentHistory, setAppointmentHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  const fetchAppointmentHistory = useCallback(async () => {
    try {
      setError(null);
      if (!refreshing) setLoading(true);
      
      const response = await get_appointment_history(selectedCustomer);

      setAppointmentHistory(response);
    } catch (err) {
      console.error('Failed to fetch appointment history:', err);
      setError('Failed to load appointments. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedCustomer, refreshing]);

  useFocusEffect(
    useCallback(() => {
      fetchAppointmentHistory();
    }, [fetchAppointmentHistory])
  );

  const AppText = useAppText();
  const { getTextColor, theme } = useTheme();

  const renderAppointmentItem = ({ item, index }) => {

    const formattedDate = moment(item.date).format('dddd M/D/YYYY');
    const startTime = moment(item.start_time, 'HH:mm:ss').format('h:mm A');
    const endTime = moment(item.end_time, 'HH:mm:ss').format('h:mm A');
    
    const serviceNames = item.service_name?.map(service => service.name).join(', ') || 'No service';
    const status = item.status || 'Not started';
    const statusColor = getStatusColor(status);

    return (
      <View style={[
        [styles.appointmentCard,{borderColor:getDark_Theme()}],
        { 
          backgroundColor: backgroundColor,
          marginTop: index === 0 ? hp(1) : hp(1.4)
        }
      ]}>
        <View style={styles.timelineContainer}>
          <View style={styles.timelineDot} />
          <View style={styles.timelineLine} />
        </View>
        
        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <View style={styles.dateTimeContainer}>
              <ResponsiveText 
                size={3.8} 
                weight="700" 
                color={getTextColor()}
              >
                {formattedDate}
              </ResponsiveText>
              <ResponsiveText 
                size={3.5} 
                weight="500" 
                color={colors.grey}
                style={styles.timeText}
              >
                {`${startTime} - ${endTime}`}
              </ResponsiveText>
            </View>
            
            <View style={[styles.statusBadge, { backgroundColor: `${statusColor}15` }]}>
              <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
              <ResponsiveText 
                size={3.5} 
                color={statusColor}
                weight="600"
              >
                {status}
              </ResponsiveText>
            </View>
          </View>

          <View style={styles.serviceContainer}>
            <ResponsiveText 
              size={4} 
              weight="600" 
              color={getTextColor()}
              style={styles.serviceName}
              numberOfLines={3}
              maxWidth={wp(70)}
            >
              {serviceNames}
            </ResponsiveText>
          </View>

          <View style={styles.detailsContainer}>
            <View style={styles.detailItem}>
              <Icon
                source={globalpath.user}
                size={wp(3.6)}
                tintColor={colors.grey}
                style={styles.detailIcon}
              />
              <ResponsiveText 
                size={3.5} 
                weight="500" 
                color={colors.grey}
              >
                {item.barber_name || 'Not assigned'}
              </ResponsiveText>
            </View>

            <View style={styles.detailItem}>
              <Icon
                source={globalpath.watch}
                size={wp(3.8)}
                tintColor={colors.lightGrey5}
                style={styles.detailIcon}
              />
              <ResponsiveText 
                size={3.5} 
                weight="500" 
                color={colors.grey}
              >
                {calculateDuration(item.start_time, item.end_time)}
              </ResponsiveText>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const LoadingView = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
      <ResponsiveText 
        size={4} 
        color={colors.grey} 
        weight="500"
        style={styles.loadingText}
      >
        {AppText.LOADING}
      </ResponsiveText>
    </View>
  );

  const ErrorView = () => (
    <View style={styles.errorContainer}>
      <ResponsiveText size={4} color={colors.error} weight="600">
        {error}
      </ResponsiveText>
      <TouchableOpacity 
        style={styles.retryButton} 
        onPress={fetchAppointmentHistory}
      >
        <ResponsiveText size={4} color={colors.Light_theme_maincolour} weight="600">
          {AppText.RETRY}
        </ResponsiveText>
      </TouchableOpacity>
    </View>
  );

  const EmptyView = () => (
    <View style={styles.emptyState}>
      <Icon 
        source={globalpath.calendar} 
        size={wp(15)} 
        tintColor={colors.grey}
      />
      <ResponsiveText 
        size={4} 
        weight="500" 
        color={colors.grey}
        style={styles.emptyText}
      >
        {AppText.NO_APPOINTMENTS_FOUND}
      </ResponsiveText>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: backgroundColor }]}>
      <View style={styles.header}>
        <View style={{justifyContent:"flex-start"}}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Icon
            source={globalpath.goback}
            size={wp(4)}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>
        </View>
        <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
        <ResponsiveText
          color={getTextColor()}
          size={4.5}
          weight="600"
          style={styles.headerTitle}
          textAlign={"center"}
        >
          {AppText.CLIENT_INFO}
        </ResponsiveText>
        </View>
      </View>

      <View style={styles.contentHeader}>
        <ResponsiveText size={4.2} weight="700" color={getTextColor()}>
          {AppText.APPOINTMENT_HISTORY}
        </ResponsiveText>
        <ResponsiveText 
          size={4} 
          weight="600" 
          color={colors.primary}
          style={styles.subHeader}
        >
          {AppText.PREV_BOOKED}
        </ResponsiveText>
      </View>

      {loading && !refreshing ? (
        <LoadingView />
      ) : error ? (
        <ErrorView />
      ) : (
        <FlatList
          data={appointmentHistory}
          renderItem={renderAppointmentItem}
          keyExtractor={(item, index) => `${item.id}-${item.date}-${index}`}
          contentContainerStyle={[
            styles.listContent,
            appointmentHistory.length === 0 && styles.emptyListContent
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={fetchAppointmentHistory}
              colors={[colors.Light_theme_maincolour]}
              tintColor={colors.Light_theme_maincolour}
            />
          }
          ListEmptyComponent={EmptyView}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

// Helper functions
const getStatusColor = (status) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return colors.success;
    case 'in progress':
      return colors.warning;
    case 'cancelled':
      return colors.error;
    default:
      return colors.grey;
  }
};

const calculateDuration = (start, end) => {
  if (!start || !end) return 'N/A';
  
  const startMoment = moment(start, 'HH:mm:ss');
  const endMoment = moment(end, 'HH:mm:ss');
  const duration = moment.duration(endMoment.diff(startMoment));
  
  const hours = duration.hours();
  const minutes = duration.minutes();
  
  if (hours === 0) {
    return `${minutes} min`;
  }
  
  return `${hours} hr${hours > 1 ? 's' : ''} ${minutes > 0 ? `${minutes} min` : ''}`;
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: wp(1),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: hp(2),
    paddingBottom: hp(3),
  },
  backButton: {
    padding: wp(2),
    marginRight: wp(2),
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginRight: wp(8),
  },
  contentHeader: {
    marginBottom: hp(2),
  },
  subHeader: {
    marginTop: hp(0.5),
  },
  listContent: {
    paddingBottom: hp(4),
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  appointmentCard: {
    flexDirection: 'row',
    borderRadius: wp(4),
    // marginHorizontal: -wp(2),
    marginBottom: hp(1.5),
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth:1
  },
  timelineContainer: {
    width: wp(7),
    alignItems: 'center',
    paddingTop: hp(2),
  },
  timelineDot: {
    width: wp(3),
    height: wp(3),
    borderRadius: wp(1.5),
    backgroundColor: colors.Light_theme_maincolour,
    zIndex: 1,
  },
  timelineLine: {
    width: 1,
    flex: 1,
    backgroundColor: colors.lightGrey1,
    // marginTop: hp(1),
  },
  cardContent: {
    flex: 1,
    padding: wp(4),
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  dateTimeContainer: {
    flex: 1,
    marginRight: wp(3),
  },
  timeText: {
    marginTop: hp(0.5),
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: wp(10),
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
  },
  statusDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginRight: wp(1.5),
  },
  serviceContainer: {
    marginTop: hp(2),
    paddingBottom: hp(2),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey2,
  },
  serviceName: {
    lineHeight: hp(2.8),
  },
  detailsContainer: {
    flexDirection: 'row',
    marginTop: hp(2),
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailIcon: {
    marginRight: wp(2),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: hp(2),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(5),
  },
  retryButton: {
    marginTop: hp(2),
    padding: wp(3),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.Light_theme_maincolour,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(10),
  },
  emptyText: {
    textAlign: 'center',
    marginTop: hp(2),
  },
});

export default PreviousBookContent;