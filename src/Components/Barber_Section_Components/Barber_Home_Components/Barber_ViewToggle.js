import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';

const Barber_ViewToggle = ({ onToggle }) => {
  const AppText = useAppText();
  const { getTextColor , barber_header } = useTheme();
  const [selected, setSelected] = useState('DAILY');

  const handleToggle = (type) => {
    setSelected(type);
    onToggle?.(type);
  };

  return (
    <View style={styles.ToogleRow_Filter}>
      <View style={[styles.ToggleButton,{backgroundColor: barber_header()}]}>
        <TouchableOpacity
          style={[
            styles.Button_Style,
            selected === 'DAILY' ? styles.selectedButton : styles.unselectedButton,
          ]}
          onPress={() => handleToggle('DAILY')}
        >
          <ResponsiveText
            style={[
              selected === 'DAILY' ? styles.selectedText : styles.unselectedText,
            ]}
            color={selected === 'DAILY' ? colors.white : colors.lightGrey5}
            size={3.7}
          >
            {AppText.DAILY}
          </ResponsiveText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.Button_Style,
            selected === 'WEEKLY' ? styles.selectedButton : styles.unselectedButton,
          ]}
          onPress={() => handleToggle('WEEKLY')}
        >
          <ResponsiveText
            style={[
              selected === 'WEEKLY' ? styles.selectedText : styles.unselectedText,
            ]}
            color={selected === 'WEEKLY' ? colors.white : colors.lightGrey5}
            size={3.7}

          >
            {AppText.WEEKLY}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Barber_ViewToggle;

const styles = StyleSheet.create({
  ToogleRow_Filter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  ToggleButton: {
    // backgroundColor: colors.lightGrey,
    paddingVertical: hp(0.5),
    paddingHorizontal: wp(0.9),
    borderWidth: wp(0.1),
    borderColor: colors.lightGrey5,
    width: "62%",
    borderRadius: wp(6),
    flexDirection: 'row',
    alignItems: 'center',
    // marginTop: hp(1),
    marginHorizontal:wp(2)
  },
  Button_Style: {
    flex: 1,
    paddingVertical: hp(0.5),
    paddingHorizontal: wp(1.6),
    alignItems: 'center',
    borderRadius: wp(6),
    marginHorizontal:wp(1)
  },
  selectedButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  unselectedButton: {
    backgroundColor: 'transparent',
  },

  selectedText: {
    color: colors.white,
  },
  unselectedText: {
    color: colors.black,
  },
}); 