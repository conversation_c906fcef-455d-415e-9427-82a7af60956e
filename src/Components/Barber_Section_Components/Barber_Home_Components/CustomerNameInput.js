import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const CustomerNameInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.CUSTOMER_NAME}
      placeholder={AppText.ENTER_YOUR_NAME}
      value={value}
      onChangeText={onChangeText}
    />
  );
};

export default CustomerNameInput; 