import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const SelectedServicesList = ({ services, onRemoveService, onUpdateDurations }) => {
  const AppText = useAppText();
  const { getTextColor } = useTheme();
  const [durations, setDurations] = useState(() => {
    const initialDurations = {};
    services.forEach(service => {
      initialDurations[service.id] = parseInt(service.duration);
    });
    console.log("hasssan", initialDurations)

    return initialDurations;
  });

  const [notes, setNotes] = useState(() => {
    const initialNotes = {};
    services.forEach(service => {
      initialNotes[service.id] = '';
    });
    return initialNotes;
  });


  const handleNoteChange = (id, text) => {
    setNotes(prev => ({
      ...prev,
      [id]: text,
    }));
  };


  useEffect(()=>{
    console.log("momnnnnssss", durations)
  },[durations])

  const handleIncrement = (id) => {
    setDurations(prev => ({
      ...prev,
      [id]: prev[id] + 15,
    }));
  };

  const handleDecrement = (id) => {
    setDurations(prev => ({
      ...prev,
      [id]: Math.max(15, prev[id] - 15),
    }));
  };

  const formatDurationDisplay = (minutes) => {
    if (minutes < 60) return `${minutes} min`;
    const hrs = Math.floor(minutes / 60).toString().padStart(2, '0');
    const mins = (minutes % 60).toString().padStart(2, '0');
    return `${hrs}:${mins}`;
  };

  const formatDurationForPayload = (minutes) => {
    const hrs = Math.floor(minutes / 60).toString().padStart(2, '0');
    const mins = (minutes % 60).toString().padStart(2, '0');
    return `${hrs}:${mins}`;
  };

  const formattedServices = services.map(service => ({
    id: service.id,
    modif_duration: formatDurationForPayload(durations[service.id]),
    note: notes[service.id] || '',
  }));

  console.log('Final payload:', formattedServices);

  // Clean up durations when services change
  useEffect(() => {
    // Remove any durations for services that no longer exist
    const updatedDurations = { ...durations };
    const serviceIds = services.map(service => service.id);

    // Remove durations for services that are no longer in the services array
    Object.keys(updatedDurations).forEach(id => {
      if (!serviceIds.includes(parseInt(id))) {
        delete updatedDurations[id];
      }
    });

    if (Object.keys(updatedDurations).length !== Object.keys(durations).length) {
      setDurations(updatedDurations);
    }

    // Format services for the payload
    const formattedServices = services.map(service => ({
      id: service.id,
      modif_duration: formatDurationForPayload(durations[service.id]),
    }));

    console.log('Final payload after update:', formattedServices);
    onUpdateDurations && onUpdateDurations(formattedServices); // Send up to parent
  }, [durations, services]); // Trigger when either durations or services change



  if (!services || services.length === 0) return null;

  return (
    <View style={styles.container}>
      {services.map((service) => (
        <View key={service.id} style={styles.serviceItem}>
          <View style={styles.serviceHeader}>
            {/* Service Name */}
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              style={styles.serviceName}
              numberOfLines={1}
              maxWidth={wp(40)}
            >
              {service.name}
            </ResponsiveText>
        <View style={styles.iconContainer}>

            {/* Decrement Button */}
            <TouchableOpacity onPress={() => handleDecrement(service.id)} style={styles.iconButton}>
              <Icon source={globalpath.Right} size={wp(3)} color={colors.grey} />
            </TouchableOpacity>

            {/* Duration */}
            <ResponsiveText
              color={colors.grey}
              size={3.5}
              style={styles.durationText}
            >
                {formatDurationDisplay(durations[service.id])}

            </ResponsiveText>

            {/* Increment Button */}
            <TouchableOpacity onPress={() => handleIncrement(service.id)} style={styles.iconButton}>
              <Icon source={globalpath.left} size={wp(3)} color={colors.grey} />
            </TouchableOpacity>

            {/* Remove Button */}
            <TouchableOpacity onPress={() => onRemoveService(service.id)} style={styles.iconButton}>
              <Icon source={globalpath.min} size={wp(4)} color={colors.red} />
            </TouchableOpacity>
            </View>
          </View>
        <CustomTextInput
            label={AppText.NOTE}
            placeholder={AppText.ENTER_NOTE}
            value={notes[service.id] || ''}
            onChangeText={(text) => handleNoteChange(service.id, text)}
            multiline={true}
            numberOfLines={4}
        />
        </View>
      ))}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: wp(2),
    padding: wp(3),
  },
  serviceItem: {
    marginBottom: hp(1),
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
    marginBottom: hp(1),
    justifyContent:"space-between"
  },
  serviceName: {
    flex: 1,
    marginRight: wp(2),
  },
  durationText: {
    marginHorizontal: wp(2),
  },
  iconButton: {
    paddingHorizontal: wp(1),
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default SelectedServicesList;
