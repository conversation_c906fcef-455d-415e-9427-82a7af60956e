// ChooseBarberDropdown.js
import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { wp, hp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';

const mockBarbers = [
  { label: 'Ali Barber', value: 'ali' },
  { label: 'Sara Stylist', value: 'sara' },
  { label: 'John Clippers', value: 'john' },
];

const ChooseBarberDropdown = ({ onSelect }) => {
  const [value, setValue] = useState(null);

  const handleChange = (item) => {
    setValue(item.value);
    onSelect && onSelect(item.value);
  };
    const { getDark_Theme, backgroundColor ,getTextColor} = useTheme();
    const AppText = useAppText();

  return (
    <View style={styles.container}>
      <Dropdown
        style={[styles.dropdown,{backgroundColor:backgroundColor}]}
        placeholderStyle={[styles.placeholderStyle,{color:getTextColor()}]}
        selectedTextStyle={[styles.selectedTextStyle,{color:getTextColor()}]}
        itemTextStyle={[styles.itemTextStyle,{color:getTextColor()}]}
        containerStyle={{ backgroundColor: backgroundColor }} // dropdown list container
        activeColor={colors.lightGrey} // 🔹 Background color of selected item
        data={mockBarbers}
        labelField="label"
        valueField="value"
        placeholder={AppText.CHOOSE_BARBER}
        value={value}
        onChange={handleChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: hp(1),
    
  },
  dropdown: {
    height: hp(5),
    borderColor: colors.grey,
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(2),
    backgroundColor: '#fff',
  },
  placeholderStyle: {
    fontSize: wp(3.5),
    // color: colors.grey,
  },
  selectedTextStyle: {
    fontSize: wp(3.5),
    // color: colors.black,
  },
  itemTextStyle: {
    fontSize: wp(3.5),

  },
});

export default ChooseBarberDropdown;
