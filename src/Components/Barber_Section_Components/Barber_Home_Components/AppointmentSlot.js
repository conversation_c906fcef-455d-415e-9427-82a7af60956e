import React from 'react';
import { StyleSheet, View } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';

const getAppointmentCardColor = (service) => {
  const colorMap = {
    'MEN\'S CUT': '#e8f5e9',
    'RIFINITURA': '#fff3e0',
    'COMBO': '#e3f2fd',
    'TAGLIO UOMO': '#e8f5e9',
    'TAGLIO': '#e8f5e9',
  };
  return colorMap[service] || '#e8f5e9';
};

const getAppointmentBorderColor = (service) => {
  const colorMap = {
    'MEN\'S CUT': '#4caf50',
    'RIFINITURA': '#ff9800',
    'COMBO': '#2196f3',
    'TAGLIO UOMO': '#4caf50',
    'TAGLIO': '#4caf50',
  };
  return colorMap[service] || '#4caf50';
};

const AppointmentSlot = ({ appointment, style }) => (
  <View style={[
    styles.container,
    {
      backgroundColor: getAppointmentCardColor(appointment.service),
      borderLeftColor: getAppointmentBorderColor(appointment.service)
    },
    style
  ]}>
    <ResponsiveText size={3.5} weight="600" style={styles.customerName}>
      {appointment.customerName}
    </ResponsiveText>
    <ResponsiveText size={3} color={colors.grey} style={styles.serviceText}>
      {appointment.service}
    </ResponsiveText>
  </View>
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#e8f5e9',
    borderRadius: wp(1.5),
    padding: wp(2.5),
    marginHorizontal: wp(1),
    marginVertical: hp(0.5),
    borderLeftWidth: 3,
    borderLeftColor: '#4caf50',
    // minHeight: hp(7),
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    
  },
  customerName: {
    marginBottom: hp(0.5),
  },
  serviceText: {
    opacity: 0.8,
  },
});

export default AppointmentSlot;
