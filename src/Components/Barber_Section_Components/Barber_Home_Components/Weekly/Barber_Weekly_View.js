import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Modal} from 'react-native';
import ResponsiveText from '../../../../Custom/RnText';
import Icon from '../../../../Custom/Icon';
import {globalpath} from '../../../../Custom/globalpath';
import {colors} from '../../../../Custom/Colors';
import {hp, wp} from '../../../../Custom/Responsiveness';
import useTheme from '../../../../Redux/useTheme';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import MonthlyCalendarView from './MonthlyCalendarView';
import MonthlyBarberHeader from './MonthlyBarberHeader';
import {
  get_barber_breaks,
  get_barber_deatils,
  list_barber_appointments,
  get_shops_barbers
} from '../../../../Services/API/Endpoints/barber/dashboard';
import Loader from '../../../../Custom/loader';
import AddAppointmentModal from '../AddAppointmentModal';
import ManageBreaks from '../ManageBreaks';
import AddAppointmentContent from '../AddAppointmentContent';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomAlert from '../../../../Components/CustomAlert';

const Barber_Weekly_View = ({salonData, userRole, selectedMonth: propSelectedMonth}) => {
  console.log("salonData in weekly view", salonData?.id, userRole)
  const navigation = useNavigation();
  const {getTextColor , barber_header } = useTheme();
  const [weekDates, setWeekDates] = useState([]);
  const [allMonthWeeks, setAllMonthWeeks] = useState([]);
  const [currentWeekIndex, setCurrentWeekIndex] = useState(0);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentBarberPairIndex, setCurrentBarberPairIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [barbers, setBarbers] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [shopId, setShopId] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const c_green = colors.c_green;
  const [breaks, setBreaks] = useState([]);
  const [selectedBarber, setSelectedBarber] = useState(null);
  const [showManageBreaks, setShowManageBreaks] = useState(false);

  // Log when showManageBreaks changes
  useEffect(() => {
    console.log("Weekly view: showManageBreaks changed to:", showManageBreaks);
  }, [showManageBreaks]);
  const [showAddAppointment, setShowAddAppointment] = useState(false);
  const [showCustomAlert, setShowCustomAlert] = useState(false);

  const handleAppointmentAdded = useCallback(async () => {
    console.log("Appointment/Break added in weekly view, refreshing data...");
    setLoading(true);
    try {
      if (userRole === 'admin' && salonData?.id) {
        const formattedDate = formatToYYYYMMDD(selectedDate);
        await Promise.all([
          get_shops(salonData.id),
          fetch_Appointments(formattedDate, salonData.id),
          fetchBarberBreaks(salonData.id)
        ]);
      } else if (shopId) {
        const formattedDate = formatToYYYYMMDD(selectedDate);
        await Promise.all([
          get_shops(shopId),
          fetch_Appointments(formattedDate, shopId),
          fetchBarberBreaks(shopId)
        ]);
      }
    } catch (error) {
      console.error('Error refreshing appointments:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedDate, shopId, userRole, salonData?.id]);

  const onRefresh = useCallback(() => {
    console.log('Refreshing appointments in weekly view...');
    setRefreshing(true);
    setLoading(true);
    fetchbarberdetails()
      .then(() => {
        setRefreshing(false);
        setLoading(false);
      })
      .catch((error) => {
        console.error('Error during refresh:', error);
        setRefreshing(false);
        setLoading(false);
      });
  }, [selectedDate, userRole, salonData?.id]);

  const getCurrentBarberPair = () => {
    if (barbers.length === 0) return [];

    // If user is a barber and we're at index 0, only show the logged-in barber
    if (userRole === 'barber' && currentBarberPairIndex === 0) {
      // Find the logged-in barber (should be at index 0)
      const loggedInBarber = barbers.find(barber => barber.isLoggedInBarber);
      if (loggedInBarber) {
        return [loggedInBarber.name];
      }
    }

    // For admin, show two barbers at a time
    if (userRole === 'admin') {
      const startIndex = currentBarberPairIndex * 2;
      const endIndex = Math.min(startIndex + 2, barbers.length);
      return barbers.slice(startIndex, endIndex).map(barber => barber.name);
    }

    // For barber, show only one barber at a time
    const index = currentBarberPairIndex;
    if (index < barbers.length) {
      return [barbers[index].name];
    }

    return [];
  };
  const handlePrevious = () => {
    if (barbers.length <= 1) return; // If there is only 1 barber, don't allow previous

    // If we're going back to index 0 and user is a barber, we'll show only the logged-in barber
    if (currentBarberPairIndex === 1 && userRole === 'barber') {
      setCurrentBarberPairIndex(0);
      return;
    }

    setCurrentBarberPairIndex(prev => (prev - 1 < 0 ? 0 : prev - 1));
  };

  const handleNext = () => {
    if (barbers.length <= 1) return; // If there is only 1 barber, don't allow next

    // Special case: If we're at index 0 as a barber user (showing only logged-in barber)
    // We want to move to showing the next barber
    if (userRole === 'barber' && currentBarberPairIndex === 0) {
      setCurrentBarberPairIndex(1);
      return;
    }

    // Calculate the maximum index based on the user role
    let maxIndex;
    if (userRole === 'admin') {
      // For admin, we show 2 barbers at a time, so max index is (total barbers / 2) rounded up - 1
      maxIndex = Math.ceil(barbers.length / 2) - 1;
    } else {
      // For barber, we show 1 barber at a time, so max index is (total barbers - 1)
      maxIndex = barbers.length - 1;
    }

    setCurrentBarberPairIndex(prev => {
      return prev + 1 > maxIndex ? maxIndex : prev + 1;
    });
  };

  useFocusEffect(
    useCallback(() => {
      // Only proceed if we have the required data
      if (!userRole) return;
      if (userRole === 'admin' && !salonData?.id) {
        console.log('Waiting for salonData.id...');
        return;
      }

      fetchbarberdetails();
    }, [userRole, salonData?.id, selectedDate])
  );

  const fetchbarberdetails = async () => {
    try {
      setLoading(true);
      setRefreshing(true);

      let targetId;
      if (userRole === 'admin') {
        if (!salonData?.id) {
          console.error('Admin user but no salonData.id available');
          return;
        }
        targetId = salonData.id;
      } else {
        const response = await get_barber_deatils();
        if (!response[0]?.shop) {
          console.error('No shop ID found in barber details');
          return;
        }
        targetId = response[0].shop;
      }

      setShopId(targetId);

      const current_date = formatToYYYYMMDD(selectedDate);

      // Execute all fetches in parallel
      await Promise.all([
        get_shops(targetId),
        fetch_Appointments(current_date, targetId),
        fetchBarberBreaks(targetId)
      ]);

    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchBarberBreaks = async (id) => {
    try {
      const response = await get_barber_breaks(id);
      console.log('🟠 Breaks fetched in weekly view:', response);
      if (Array.isArray(response)) {
        setBreaks(response);
      } else {
        console.error('Invalid breaks response:', response);
        setBreaks([]);
      }
    } catch (error) {
      console.error('❌ Failed to fetch breaks:', error);
      setBreaks([]);
    }
  };

  function formatToYYYYMMDD(dateInput) {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return null;

    // Get the date in local timezone
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  const get_shops = async (id) => {
      try {
        const response = await get_shops_barbers(id);
        console.log('🟢Barber details fetched:', response);

        // Process the response to extract unique barbers
        const uniqueBarbers = [];
        const barberMap = new Map();

        // If user is a barber, get the logged-in barber details from AsyncStorage
        let loggedInBarber = null;
        if (userRole === 'barber') {
          try {
            const barberData = await AsyncStorage.getItem('barberData');
            if (barberData) {
              const parsed = JSON.parse(barberData);
              loggedInBarber = Array.isArray(parsed) ? parsed[0] : parsed;
              console.log('✅ Logged-in barber retrieved:', loggedInBarber);
            }
          } catch (error) {
            console.log('❌ Error fetching logged-in barber details:', error);
          }
        }

        // If user is a barber, put the logged-in barber at index 0
        if (userRole === 'barber' && loggedInBarber) {
          // First add the logged-in barber
          uniqueBarbers.push({
            id: loggedInBarber.id,
            name: loggedInBarber.full_name,
            full_name: loggedInBarber.full_name,
            shop: loggedInBarber.shop,
            isLoggedInBarber: true, // Flag to identify the logged-in barber
            ...loggedInBarber
          });
          barberMap.set(loggedInBarber.id, true);
        }

        // Then add the rest of the barbers
        response.forEach(barber => {
          if (!barberMap.has(barber.id)) {
            barberMap.set(barber.id, true);
            uniqueBarbers.push({
              id: barber.id,
              name: barber.full_name,
              full_name: barber.full_name,
              shop: barber.shop,
              isLoggedInBarber: false,
              ...barber
            });
          }
        });

        setBarbers(uniqueBarbers);
      } catch (error) {
        console.error('❌ Failed to fetch breaks:', error);
      }
    };

  const fetch_Appointments = async (currentDate, id) => {
    console.log('Fetching appointments for date:', currentDate, 'shop id:', id);
    const start_date = currentDate;
    const end_date = currentDate;

    try {
      const response = await list_barber_appointments(start_date, end_date, id);
      console.log('Appointments fetched in weekly view:', response);
      if (Array.isArray(response)) {
        setAppointments(response);
      } else {
        console.error('Invalid appointments response:', response);
        setAppointments([]);
      }
    } catch (error) {
      console.error('❌ Failed to fetch appointments:', error);
      setAppointments([]);
      throw error; // Re-throw to be caught by the caller
    }
  };
  // Effect to handle selected month changes
  useEffect(() => {
    if (propSelectedMonth) {
      generateMonthWeeks(propSelectedMonth);
    } else {
      // If no month is selected, use current month
      const currentDate = new Date();
      generateMonthWeeks(currentDate);
    }
  }, [propSelectedMonth]);

  // Effect to update week dates when selected date changes
  useEffect(() => {
    if (weekDates.length === 0 && allMonthWeeks.length > 0) {
      // Initialize with the first week if weekDates is empty
      setWeekDates(allMonthWeeks[0]);
      setCurrentWeekIndex(0);
    } else if (selectedDate) {
      // Find the week that contains the selected date
      const weekIndex = findWeekIndexForDate(selectedDate);
      if (weekIndex !== -1) {
        setWeekDates(allMonthWeeks[weekIndex]);
        setCurrentWeekIndex(weekIndex);
      }
    }
  }, [selectedDate, allMonthWeeks]);

  // Find the week index that contains the given date
  const findWeekIndexForDate = (date) => {
    return allMonthWeeks.findIndex(week =>
      week.some(weekDate =>
        weekDate.toDateString() === date.toDateString()
      )
    );
  };

  // Generate all weeks for the given month
  const generateMonthWeeks = (monthDate) => {
    const year = monthDate.getFullYear();
    const month = monthDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);

    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Start from the Monday before or on the first day of the month
    const day = firstDay.getDay(); // 0 (Sun) to 6 (Sat)
    const diffToMonday = day === 0 ? -6 : 1 - day;
    const firstMonday = new Date(firstDay);
    firstMonday.setDate(firstDay.getDate() + diffToMonday);

    // Generate weeks until we pass the last day of the month
    const weeks = [];
    let currentDate = new Date(firstMonday);

    while (currentDate <= lastDay || weeks.length === 0 || currentDate.getDay() !== 1) {
      // Start a new week on Monday
      if (currentDate.getDay() === 1 || weeks.length === 0) {
        const week = [];
        for (let i = 0; i < 7; i++) {
          const date = new Date(currentDate);
          date.setDate(currentDate.getDate() + i);
          week.push(date);
        }
        weeks.push(week);
      }

      // Move to next Monday
      currentDate.setDate(currentDate.getDate() + 7);
    }

    setAllMonthWeeks(weeks);

    // Find the week that contains today or the selected date
    const today = new Date();
    const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;

    if (isCurrentMonth) {
      // If it's the current month, find the week with today's date
      const weekWithToday = weeks.findIndex(week =>
        week.some(date => date.toDateString() === today.toDateString())
      );

      if (weekWithToday !== -1) {
        setWeekDates(weeks[weekWithToday]);
        setCurrentWeekIndex(weekWithToday);
        // Also update selected date to today
        setSelectedDate(today);
      } else {
        // Fallback to first week
        setWeekDates(weeks[0]);
        setCurrentWeekIndex(0);
      }
    } else {
      // For other months, start with the first week
      setWeekDates(weeks[0]);
      setCurrentWeekIndex(0);
      // Set selected date to the first day of the month
      setSelectedDate(firstDay);
    }
  };

  // Navigate to previous week
  const goToPreviousWeek = () => {
    if (currentWeekIndex > 0) {
      const newIndex = currentWeekIndex - 1;
      setCurrentWeekIndex(newIndex);
      setWeekDates(allMonthWeeks[newIndex]);

      // Set selected date to the first day of the week
      if (allMonthWeeks[newIndex] && allMonthWeeks[newIndex].length > 0) {
        setSelectedDate(allMonthWeeks[newIndex][0]);
      }
    }
  };

  // Navigate to next week
  const goToNextWeek = () => {
    if (currentWeekIndex < allMonthWeeks.length - 1) {
      const newIndex = currentWeekIndex + 1;
      setCurrentWeekIndex(newIndex);
      setWeekDates(allMonthWeeks[newIndex]);

      // Set selected date to the first day of the week
      if (allMonthWeeks[newIndex] && allMonthWeeks[newIndex].length > 0) {
        setSelectedDate(allMonthWeeks[newIndex][0]);
      }
    }
  };

  const isToday = date => date.toDateString() === new Date().toDateString();
  const isSelected = date =>
    date.toDateString() === selectedDate.toDateString();

  const handleDatePress = useCallback(async (date) => {
    setSelectedDate(date);
    setLoading(true);
    try {
      const formattedDate = formatToYYYYMMDD(date);
      if (userRole === 'admin' && salonData?.id) {
        await Promise.all([
          fetch_Appointments(formattedDate, salonData.id),
          fetchBarberBreaks(salonData.id)
        ]);
      } else if (shopId) {
        await Promise.all([
          fetch_Appointments(formattedDate, shopId),
          fetchBarberBreaks(shopId)
        ]);
      }
    } catch (error) {
      console.error('Error fetching appointments for date:', error);
    } finally {
      setLoading(false);
    }
  }, [shopId, userRole, salonData?.id]);

  // Format the week date range (e.g., "1 Jan - 7 Jan 2023")
  const getWeekDateRange = () => {
    if (!weekDates || weekDates.length === 0) return '';

    const startDate = weekDates[0];
    const endDate = weekDates[6];

    // Check if the dates are in the same month and year
    const sameMonth = startDate.getMonth() === endDate.getMonth();
    const sameYear = startDate.getFullYear() === endDate.getFullYear();

    if (sameMonth && sameYear) {
      // Format: "1 - 7 January 2023"
      return `${startDate.getDate()} - ${endDate.getDate()} ${startDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}`;
    } else if (sameYear) {
      // Format: "28 December - 3 January 2023"
      return `${startDate.getDate()} ${startDate.toLocaleDateString('en-GB', { month: 'long' })} - ${endDate.getDate()} ${endDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}`;
    } else {
      // Format: "30 December 2022 - 5 January 2023"
      return `${startDate.getDate()} ${startDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })} - ${endDate.getDate()} ${endDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}`;
    }
  };

  const weekDateRange = getWeekDateRange();
  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSelectedBarber(null); // Reset selected barber when modal is closed
  };

  const handleAddPress = () => {
    setIsModalVisible(true);
  };

  // Function to handle date changes from the modal
  const handleDateChange = (newDate) => {
    // Ensure we're working with Date objects
    const dateObj = typeof newDate === 'string' ? new Date(newDate) : newDate;
    setSelectedDate(dateObj);
  };

  const handleBarberPress = (barberName) => {
    console.log("Weekly view - Barber pressed:", barberName);
    console.log("Weekly view - All barbers:", JSON.stringify(barbers));

    // Find the barber object by name
    const barberObj = barbers.find(b => b.name === barberName);
    console.log("Weekly view - Found barber object:", barberObj);

    // If user is a barber, check if they're trying to manage breaks for another barber
    if (userRole === 'barber' && barberObj && !barberObj.isLoggedInBarber) {
      setShowCustomAlert(true);
      return;
    }

    if (barberObj) {
      console.log("Weekly view - Setting selected barber and showing appointment modal");
      // Add openManageBreaks flag to the barber object
      const barberWithFlag = {
        ...barberObj,
        openManageBreaks: true
      };
      setSelectedBarber(barberWithFlag);
      setIsModalVisible(true);
    } else {
      console.log("Weekly view - Barber object not found for name:", barberName);
      // Try to find by full_name instead of name
      const barberByFullName = barbers.find(b => b.full_name === barberName);
      if (barberByFullName) {
        // If user is a barber, check if they're trying to manage breaks for another barber
        if (userRole === 'barber' && !barberByFullName.isLoggedInBarber) {
          setShowCustomAlert(true);
          return;
        }

        console.log("Weekly view - Found barber by full_name:", barberByFullName);
        // Add openManageBreaks flag to the barber object
        const barberWithFlag = {
          ...barberByFullName,
          openManageBreaks: true
        };
        setSelectedBarber(barberWithFlag);
        setIsModalVisible(true);
      } else {
        // If we still can't find the barber, try a more flexible approach
        console.log("Weekly view - Trying flexible matching for barber name:", barberName);
        for (const barber of barbers) {
          console.log(`Weekly view - Checking barber: ${barber.name} / ${barber.full_name}`);
          if (barber.name?.includes(barberName) || barber.full_name?.includes(barberName)) {
            // If user is a barber, check if they're trying to manage breaks for another barber
            if (userRole === 'barber' && !barber.isLoggedInBarber) {
              setShowCustomAlert(true);
              return;
            }

            console.log("Weekly view - Found partial match:", barber);
            // Add openManageBreaks flag to the barber object
            const barberWithFlag = {
              ...barber,
              openManageBreaks: true
            };
            setSelectedBarber(barberWithFlag);
            setIsModalVisible(true);
            break;
          }
        }
      }
    }
  };

  const handleCloseManageBreaks = () => {
    setShowManageBreaks(false);
    setSelectedBarber(null);
  };

  const handleCloseAddAppointment = () => {
    setShowAddAppointment(false);
    setSelectedBarber(null);
  };

  return (
    <View style={styles.container}>

      <View style={styles.wrapper}>
        <View style={styles.calendarCard}>
          {/* Top Row: Date with Navigation Arrows */}
          <View style={styles.topRow}>
            <TouchableOpacity
              onPress={goToPreviousWeek}
              disabled={currentWeekIndex === 0}
              style={[
                [styles.dateNavigationButton,{backgroundColor:barber_header()}],
                { opacity: currentWeekIndex === 0 ? 0.5 : 1 }
              ]}
            >
              <Icon
                source={globalpath.Right}
                size={wp(3)}
                tintColor={getTextColor()}
              />
            </TouchableOpacity>

            <ResponsiveText size={4.3} weight="600" color={getTextColor()}>
              {weekDateRange}
            </ResponsiveText>

            <TouchableOpacity
              onPress={goToNextWeek}
              disabled={currentWeekIndex === allMonthWeeks.length - 1}
              style={[
                   [styles.dateNavigationButton,{backgroundColor:barber_header()}],
                { opacity: currentWeekIndex === allMonthWeeks.length - 1 ? 0.5 : 1 }
              ]}
            >
              <Icon
                source={globalpath.left}
                size={wp(3)}
                tintColor={getTextColor()}
              />
            </TouchableOpacity>
          </View>

          {/* Week Navigation and Days Row */}
          <View style={styles.weekNavigationContainer}>
            {/* <TouchableOpacity
              onPress={goToPreviousWeek}
              disabled={currentWeekIndex === 0}
              style={[
                styles.weekNavigationButton,
                { opacity: currentWeekIndex === 0 ? 0.5 : 1 }
              ]}
            >
              <Icon
                source={globalpath.Right}
                size={wp(4)}
                tintColor={getTextColor()}
              />
            </TouchableOpacity> */}

            <View style={styles.daysRow}>
              {weekDates.map((date, index) => {
                const isCurrent = isToday(date);
                const isPicked = isSelected(date);
                const bgColor = isCurrent
                  ? colors.Light_theme_maincolour
                  : isPicked
                  ? c_green
                  : 'transparent';
                const textColor =
                  bgColor === 'transparent' ? getTextColor() : '#fff';

                return (
                  <TouchableOpacity
                    key={index}
                    style={styles.dayItem}
                    onPress={() => handleDatePress(date)}>
                    <View style={[styles.pill, {backgroundColor: bgColor}]}>
                      <ResponsiveText size={3} weight="500" color={textColor}>
                        {date.toLocaleDateString('en-US', {weekday: 'short'})}
                      </ResponsiveText>
                      <ResponsiveText size={3.1} weight="600" color={textColor}>
                        {date.getDate()}
                      </ResponsiveText>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>

            {/* <TouchableOpacity
              onPress={goToNextWeek}
              disabled={currentWeekIndex === allMonthWeeks.length - 1}
              style={[
                styles.weekNavigationButton,
                { opacity: currentWeekIndex === allMonthWeeks.length - 1 ? 0.5 : 1 }
              ]}
            >
              <Icon
                source={globalpath.left}
                size={wp(4)}
                tintColor={getTextColor()}
              />
            </TouchableOpacity> */}
          </View>
        </View>

      </View>

      {/* {
        loading ? null : (
          barbers.length === 0 && appointments.length === 0 && breaks.length === 0 ? (
            <View style={{ alignItems: 'center', marginTop: hp(5) }}>
              <ResponsiveText size={4} weight="600" color={colors.grey}>
                No Data Available
              </ResponsiveText>
            </View>
          ) : (
            <>
              <MonthlyBarberHeader
                currentBarbers={getCurrentBarberPair()}
                onPrevious={handlePrevious}
                onNext={handleNext}
              />
              <MonthlyCalendarView
                currentBarbers={getCurrentBarberPair()}
                selectedDate={selectedDate}
                appointments={appointments}
                onRefresh={onRefresh}
                refreshing={refreshing}
                breaks={breaks}
              />
            </>
          )
        )
      } */}

      {
  loading ? null : (
    barbers.length === 0 ? (
      <View style={{ alignItems: 'center', marginTop: hp(5) }}>
        <ResponsiveText size={4} weight="600" color={colors.grey}>
          No Data Available
        </ResponsiveText>
      </View>
    ) : (
      <>
        <MonthlyBarberHeader
          currentBarbers={getCurrentBarberPair()}
          onPrevious={handlePrevious}
          onNext={handleNext}
          onBarberPress={handleBarberPress}
          userRole={userRole}
          barberObjects={barbers}
          hasMoreBarbers={userRole === 'admin'
            ? currentBarberPairIndex < Math.ceil(barbers.length / 2) - 1
            : currentBarberPairIndex < barbers.length - 1}
          hasPreviousBarbers={currentBarberPairIndex > 0}
        />
        {
          appointments.length === 0 && breaks.length === 0 ? (
            <View style={{ alignItems: 'center', marginTop: hp(5) }}>
              <ResponsiveText size={4} weight="600" color={colors.grey}>
                No Data Available
              </ResponsiveText>
            </View>
          ) : (
            <MonthlyCalendarView
              currentBarbers={getCurrentBarberPair()}
              selectedDate={selectedDate}
              appointments={appointments}
              onRefresh={onRefresh}
              refreshing={refreshing}
              breaks={breaks}
            />
          )
        }
      </>
    )
  )
}


      {loading ? <Loader/> :undefined}

      {showManageBreaks && (
        <Modal
          visible={showManageBreaks}
          animationType="slide"
          transparent={true}
          onRequestClose={handleCloseManageBreaks}
          statusBarTranslucent={true}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <ManageBreaks
                onBack={handleCloseManageBreaks}
                salonData={salonData}
                onClose={handleCloseManageBreaks}
                onAppointmentAdded={handleAppointmentAdded}
                userRole={userRole}
                selectedBarber={selectedBarber}
              />
            </View>
          </View>
        </Modal>
      )}


      {showAddAppointment && (
        <Modal
          visible={showAddAppointment}
          animationType="slide"
          transparent={true}
          onRequestClose={handleCloseAddAppointment}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <AddAppointmentContent
                onClose={handleCloseAddAppointment}
                selectedDate={selectedDate}
                setSelectedDate={handleDateChange}
                onAppointmentAdded={handleAppointmentAdded}
                salonData={salonData}
                userRole={userRole}
                selectedBarber={selectedBarber}
              />
            </View>
          </View>
        </Modal>
      )}

      <AddAppointmentModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        selectedDate={selectedDate}
        setSelectedDate={handleDateChange}
        onAppointmentAdded={handleAppointmentAdded}
        salonData={salonData}
        userRole={userRole}
        selectedBarber={selectedBarber}
      />

      <CustomAlert
        visible={showCustomAlert}
        title="Permission Denied"
        message="You can only manage breaks for your own schedule."
        onClose={() => setShowCustomAlert(false)}
        type="error"
        buttonText="I Understand"
      />

      {/* Floating Add Button */}
      <TouchableOpacity
        style={styles.floatingAddButton}
        onPress={handleAddPress}
        activeOpacity={0.9}
      >
        <Icon
          source={globalpath.add}
          size={wp(10)}
          tintColor={colors.white}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Barber_Weekly_View;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  wrapper: {
    paddingHorizontal: wp(1.5),
    marginTop: hp(1.3),
    marginHorizontal: wp(1.5),
  },
  calendarCard: {
    borderWidth: 1,
    borderColor: '#4E9CAF50',
    borderRadius: wp(1.5),
    padding: wp(4),
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: "space-between",
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  dateNavigationButton: {
    padding: wp(2),
    // backgroundColor: "#E9ECF2",
    borderWidth: 1,
    borderColor: '#4E9CAF50',
    borderRadius: wp(5),
    marginHorizontal: wp(2),
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: hp(2),
    right: wp(4),
    width: wp(11),
    height: wp(11),
    borderRadius: wp(6),
    backgroundColor: colors.Light_theme_maincolour,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 100,
  },
  weekNavigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  weekNavigationButton: {
    padding: wp(2),
    backgroundColor: "#E9ECF2",
    borderWidth: 1,
    borderColor: '#4E9CAF50',
    borderRadius: wp(5),
    marginHorizontal: wp(1),
  },
  daysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(0.5),
    flex: 1,
  },
  dayItem: {
    flex: 1,
    alignItems: 'center',
  },
  pill: {
    backgroundColor: 'transparent',
    paddingVertical: hp(1),
    paddingHorizontal: wp(2.4),
    borderRadius: wp(1.5),
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  modalContent: {
    width: wp(90),
    maxHeight: hp(80),
    backgroundColor: colors.white,
    borderRadius: wp(3),
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  testButton: {
    position: 'absolute',
    right: wp(4),
    width: wp(25),
    height: wp(11),
    borderRadius: wp(6),
    backgroundColor: colors.Light_theme_maincolour,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 100,
  },
});
