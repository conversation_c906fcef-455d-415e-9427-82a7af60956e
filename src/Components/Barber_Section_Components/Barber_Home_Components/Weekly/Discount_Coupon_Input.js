import React, { useState } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, Image } from 'react-native';
import useTheme from '../../../../Redux/useTheme';
import useAppText from '../../../../Custom/AppText';
import ResponsiveText from '../../../../Custom/RnText';
import Icon from '../../../../Custom/Icon';
import { hp, wp } from '../../../../Custom/Responsiveness';
import { colors } from '../../../../Custom/Colors';
import { globalpath } from '../../../../Custom/globalpath';
// import { colors } from '../../../../Custom/Colors';
// import { colors } from '../../../../Custom/Colors';
// import useTheme from '../../../Redux/useTheme';
// import useAppText from '../../../Custom/AppText';
// import ResponsiveText from '../../../Custom/RnText';
// import { colors } from '../../../Custom/Colors';
// import { hp, wp } from '../../../Custom/Responsiveness';
// import { globalpath } from '../../../Custom/globalpath';
// import Icon from '../../../Custom/Icon';

const Discount_Coupon_Input = ({ onApplyCoupon }) => {
  const { getTextColor } = useTheme();
  const AppText = useAppText();
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState(null);

  const handleApplyCoupon = () => {
    // In a real app, you would validate the coupon code with your backend
    // For now, we'll just use a simple validation
    if (couponCode === '20OFF') {
      setAppliedCoupon(20);
      onApplyCoupon(20);
    } else if (couponCode === '30OFF') {
      setAppliedCoupon(30);
      onApplyCoupon(30);
    } else {
      // Handle invalid coupon
      alert('Invalid coupon code');
    }
  };

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4.2}
        weight="bold"
        margin={[hp(-2), 0, hp(0.5), 0]}
      >
        {AppText.APPLY_DISCOUNT}
      </ResponsiveText>

      <View style={styles.inputContainer}>
        <Icon
          source={globalpath.coupon}
          resizeMode="contain"
          tintColor={colors.Light_theme_maincolour}
          size={wp(7)}
          margin={[0,wp(2),0,wp(0)]}
        />
        <TextInput
          style={[styles.input, { color: getTextColor() }]}
          placeholder={AppText.ENTER_COUPON_CODE}
          placeholderTextColor={colors.grey}
          value={couponCode}
          onChangeText={setCouponCode}
          editable={!appliedCoupon}
        />
        {!appliedCoupon ? (
          <TouchableOpacity
            style={styles.applyButton}
            onPress={handleApplyCoupon}
          >
            <ResponsiveText
              color={colors.white}
              size={3.5}
              weight="500"
            >
              {AppText.APPLY}
            </ResponsiveText>
          </TouchableOpacity>
        ) : (
          <View style={styles.appliedCoupon}>
            <ResponsiveText
              color={"green"}
              size={3.5}
              weight="500"
            >
              {appliedCoupon}% OFF
            </ResponsiveText>
          </View>
        )}
      </View>
      <ResponsiveText color={getTextColor()} margin={[hp(1),0,0,0]}>{AppText.DISCOUNT_IS_ACTIVE}</ResponsiveText>
    </View>
  );
};

export default Discount_Coupon_Input;

const styles = StyleSheet.create({
  container: {
    padding: wp(5),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: wp(2),
    paddingHorizontal: wp(3),
    height: hp(6),
  },
  icon: {
    width: wp(5),
    height: wp(5),
    marginRight: wp(2),
  },
  input: {
    flex: 1,
    fontSize: wp(3.5),
    paddingVertical: 0,
    marginRight: wp(2),
  },
  applyButton: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(6),
    paddingVertical: hp(1.6),
    borderRadius: wp(1.7),
    marginRight: wp(-2),
    position:"absolute",
    right:wp(2)
  },
  appliedCoupon: {
    backgroundColor: colors.green + '20',
    paddingHorizontal: wp(6),
    paddingVertical: hp(1.5),
    borderRadius: wp(1.5),
    marginRight: wp(-2),
  },
}); 