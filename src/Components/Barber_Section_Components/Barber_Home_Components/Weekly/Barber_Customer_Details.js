import { StyleSheet, Text, TextInput, TouchableOpacity, View , ScrollView , Alert } from 'react-native'
import React, { useState , useEffect } from 'react'
import useAppText from '../../../../Custom/AppText';
import ResponsiveText from '../../../../Custom/RnText';
import Icon from '../../../../Custom/Icon';
import { hp, wp } from '../../../../Custom/Responsiveness';
import useTheme from '../../../../Redux/useTheme';
import { globalpath } from '../../../../Custom/globalpath';
import { colors } from '../../../../Custom/Colors';
import Discount_Coupon_Input from './Discount_Coupon_Input';
import AssignBarberModal from '../../../Salons/AssignBarberModal';
import Your_Book_Service from '../../../Bookings/Your_Book_Service';
import AddServiceModal from '../../../Salons/AddServiceModal';
import NoteInput from '../NoteInput';
import { useNavigation } from '@react-navigation/native';
import { add_appointment_comments , get_customer_comments , get_barber_appointment ,update_comments } from '../../../../Services/API/Endpoints/Customer/comments';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { update_barber_appointment , update_customer_appointment } from '../../../../Services/API/Endpoints/barber/add_appointment';




const Barber_customer__Details = ({ appointment , customer, phone, email, services,barberdetails }) => {
    console.log("The barber details is ",barberdetails)
    const navigation = useNavigation(); // Only if not passed as a prop

    const AppText = useAppText();
    const { backgroundColor, getTextColor, getDark_Theme } = useTheme();

    // State declarations
    const [modalVisible, setModalVisible] = useState(false);
    const [totalAmount, setTotalAmount] = useState("0.00");

    const [note, setNote] = useState();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedBarbers, setSelectedBarbers] = useState([]);
    const [isCurrentBarber, setIsCurrentBarber] = useState(false);
    const [userRole, setUserRole] = useState(null);
    const [notesText, setNotesText] = useState('');
    const [showSaveIcon, setShowSaveIcon] = useState(false);

    // Check if the logged-in user is a barber and if they are the barber for this appointment
    useEffect(() => {
        const checkBarberPermission = async () => {
            try {
                // Get user role
                const userData = await AsyncStorage.getItem('userData');
                if (userData) {
                    const { role } = JSON.parse(userData);
                    setUserRole(role);

                    // If user is a barber, check if they are the barber for this appointment
                    if (role === 'barber') {
                        const barberData = await AsyncStorage.getItem('barberData');
                        if (barberData) {
                            const parsed = JSON.parse(barberData);
                            const loggedInBarber = Array.isArray(parsed) ? parsed[0] : parsed;
                            console.log("loggedInBarber --->", loggedInBarber)
                            // Check if the logged-in barber's ID matches the appointment's barber ID
                            if (loggedInBarber && appointment && loggedInBarber.id === appointment.barber) {
                                setIsCurrentBarber(true);
                                console.log('✅ Logged-in barber is the barber for this appointment');
                            } else {
                                setIsCurrentBarber(false);
                                console.log('❌ Logged-in barber is NOT the barber for this appointment');
                            }
                        }
                    } else {
                        // If user is admin, they can perform all actions
                        setIsCurrentBarber(true);
                    }
                }
            } catch (error) {
                console.error('Error checking barber permission:', error);
            }
        };

        checkBarberPermission();
    }, [appointment]);

    const [comments, setComments] = useState([]);
    const [commentText, setCommentText] = useState('');
    const [isEditing, setIsEditing] = useState(false);
    const [editingCommentId, setEditingCommentId] = useState(null);
    




    useEffect(() => {
        fetchComments();
      }, []);
      
      const fetchComments = async () => {
        try {
            if(appointment.customer_appointment){
                const response = await get_customer_comments(appointment.id);
                console.log('🟢 Comments response:', response); // Check if it's response.data or just response
                setComments(response); // if response is an array

            }
            else{
                const response = await get_barber_appointment(appointment.id);
                console.log('🟢 Comments response:', response); // Check if it's response.data or just response
                setComments(response); // if response is an array

            }
         
        } catch (error) {
          console.error('❌ Failed to fetch comments:', error);
        }
      };
      
      

      
      const handleSubmitComment = async () => {
        if (!commentText.trim()) return;
      
        try {
          const userData = await AsyncStorage.getItem('userData');
          const data = JSON.parse(userData);
          console.log('data-->', data)
          const { user_id } = JSON.parse(userData);
          console.log('user_id-->', user_id)
          
          const payload = {
            comment: commentText.trim(),
            customer_appointment: appointment.customer_appointment ? appointment.id : null,
            barber_appointment:  appointment.barber_appointment ? appointment.id : null,
            user:user_id,

        
          };
      
          if (isEditing && editingCommentId) {
            await update_comments(editingCommentId, payload);
          } else {
            await add_appointment_comments(payload);
          }
      
          setCommentText('');
          setIsEditing(false);
          setEditingCommentId(null);
          fetchComments(); // refresh list
        } catch (error) {
          console.error('❌ Failed to submit comment:', error);
        }
      };

      useEffect(() => {
        const total = calculateTotalPrice(services); // or whatever your service list is
        setTotalAmount(total);
      }, [services]);


      const calculateTotalPrice = (services) => {
        if (!services || !Array.isArray(services) || services.length === 0) {
          console.log('No services found or empty services array');
          return "0.00";
        }
      
        // Log services for debugging
        console.log('🧾 Calculating total price for services:', services.map(s => ({
          name: s.name,
          price: s.price
        })));
      
        const total = services.reduce((sum, service) => {
          // Parse and validate price
          const price = service.price ? parseFloat(service.price) : 0;
          console.log(`➕ Service: ${service.name}, Price: ${price}`);
          return sum + (isNaN(price) ? 0 : price);
        }, 0);
      
        console.log(`✅ Total price calculated: $${total.toFixed(2)}`);
        return total.toFixed(2);
      };
      
      
      

    const handleBarberSelect = (barbers) => {
        setSelectedBarbers(barbers);
        console.log('✅ Selected Barbers of the Assigned barber:', barbers[0].id , barbers[0].full_name);
        updateAppointmentStatus(barbers[0].id ,barbers[0].full_name )

    };

    // Function to update the appointment status
        const updateAppointmentStatus = async (id, name) => {
            try {
            const appointmentId = appointment.id;
        
            if (appointment.barber_appointment === true) {
                // For barber appointments
                const userData = await AsyncStorage.getItem('userData');
                const data = JSON.parse(userData);
                console.log('data-->', data)
                const { user_id } = JSON.parse(userData);
                console.log('user_id-->', user_id)
          
                const payload = {
                barber: id,
                barber_name: name,
                transferred_by: user_id
                };
        
                console.log("🔄 Updating Barber Appointment");
                console.log("PATCH Payload:", payload);
                console.log("Appointment ID:", appointmentId);
        
                // Uncomment when ready
                await update_barber_appointment(appointmentId, payload);
                Alert.alert(
                    'Success',
                    'Successfully assigned the barber.',
                    [
                    {
                        text: 'OK',
                        onPress: () => navigation.goBack() // ✅ Go back on alert confirm
                    }
                    ]
                );
            } else {
                // For customer appointments
                const payload = {
                ...appointment,
                barber: id,
                barber_name: name
                };
        
                console.log("🔄 Updating Customer Appointment");
                console.log("PATCH Payload:", payload);
                console.log("Appointment ID:", appointmentId);
        
                await update_customer_appointment(appointmentId, payload);
                // ✅ Show success alert
                Alert.alert(
                    'Success',
                    'Successfully assigned the barber.',
                    [
                    {
                        text: 'OK',
                        onPress: () => navigation.goBack() // ✅ Go back on alert confirm
                    }
                    ]
                );
            }
        
            console.log('✅ Successfully updated appointment with barber:', name);
            } catch (error) {
            console.error('❌ Failed to update appointment status:', error);
            }
        };
  


        // ... other state declarations remain the same ...
       
    
        const handleNotesChange = (text) => {
            setNotesText(text);
            setShowSaveIcon(text.length > 0);
        };
    
        const handleSaveNotes = () => {
            // Save your notes here
            console.log('Notes saved:', notesText);
            // You might want to add actual saving logic here
        };

        const handleEditComment = (comment) => {
            setCommentText(comment.comment); // populate input
            setEditingCommentId(comment.id); // track which one is being edited
            setIsEditing(true);
          };
          
    

    return (
        <View>
            {/* Customer Details Card */}
            <View style={[styles.DetailCard, { backgroundColor: backgroundColor, borderWidth: 1, borderColor: getDark_Theme() }]}>
                <View style={styles.BarberDetail_Image}>
                    <ResponsiveText size={4.7} weight={'500'} color={getTextColor()}>{AppText.CUSTOMER}</ResponsiveText>
                    <Icon source={globalpath.profile} style={styles.BarberImage}></Icon>
                </View>

                <View style={styles.Barberinfo}>
                    <Icon source={globalpath.user2} style={styles.Icon}></Icon>
                    <ResponsiveText size={4} weight={'500'} color={getTextColor()}>{customer} </ResponsiveText>
                </View>

                <View style={styles.Barberinfo}>
                    <Icon source={globalpath.call} style={styles.Icon}></Icon>
                    <ResponsiveText size={4} weight={'500'} color={getTextColor()}>{phone}</ResponsiveText>
                </View>

                <View style={styles.Barberinfo}>
                    <Icon source={globalpath.order_location} style={styles.Icon}></Icon>
                    <ResponsiveText size={4} weight={'500'} color={getTextColor()} maxWidth={wp(70)} numberOfLines={1}>
                       {email}
                    </ResponsiveText>
                </View>
            </View>

            {/* Add Service Button */}
            {/* <TouchableOpacity
                style={[styles.Add_Service_Button_style, { backgroundColor: colors.grey }]}
                onPress={() => setModalVisible(true)}
            >
                <ResponsiveText color={colors.white}>{AppText.ADD_SERVICE}</ResponsiveText>
            </TouchableOpacity> */}

            {/* Client Services Section */}
            <Your_Book_Service title={AppText.CLIENT_SERVICES} services={services} />

            {/* Assign Colleague Button - Only show if current barber */}
            {isCurrentBarber && (
                <TouchableOpacity
                    style={[styles.Add_Service_Button_style, { backgroundColor: colors.Light_theme_maincolour }]}
                    onPress={() => setIsModalVisible(true)}
                >
                    <ResponsiveText color={colors.white} size={3.3}>
                        {AppText.ASSIGN_COLLEAUGE}
                    </ResponsiveText>
                </TouchableOpacity>
            )}

            {/* Discount Coupon Input - Only show if current barber */}
            {isCurrentBarber ? (
                <Discount_Coupon_Input />
            ) : (
           null
            )}

            {/* Total Amount Card */}
            <View style={[styles.DetailCard, { backgroundColor: backgroundColor, borderWidth: 1, borderColor: getDark_Theme() }]}>
                <View style={styles.TotalAmount}>
                    <ResponsiveText size={4.5} weight={'500'} color={getTextColor()}>
                    {AppText.TOTAL_AMOUNT}
                    </ResponsiveText>
                    <ResponsiveText size={4.5} weight={'500'} color={getTextColor()}>
                    {new Intl.NumberFormat('en-IE', {
                        style: 'currency',
                        currency: 'EUR',
                        }).format(parseFloat(totalAmount))}
                    </ResponsiveText>
                </View>
                </View>


            {/* Additional Notes Section */}
           {/* Additional Notes Section */}
           
           <View style={[styles.DetailCard, { backgroundColor: backgroundColor, borderWidth: 1, borderColor: getDark_Theme() }]}>
                    <ResponsiveText size={4.3} weight={'500'} color={getTextColor()}>
                        Comments
                    </ResponsiveText>

                    {/* Comment List */}
                    <View
                        style={{
                            maxHeight: hp(25), // roughly 3 comment cards high
                            marginTop: hp(2),
                        }}
                        >
                        <ScrollView
                            showsVerticalScrollIndicator={true}
                            nestedScrollEnabled={true}
                            contentContainerStyle={{ paddingBottom: hp(2) }}
                        >
                          {comments.map((item) => {
                                const avatarLetter = item.user_email?.charAt(0)?.toUpperCase() || '?';

                                return (
                                    <View
                                    key={item.id}
                                    style={{
                                        flexDirection: 'row',
                                        backgroundColor: colors.lightGrey,
                                        borderRadius: wp(2),
                                        padding: wp(3),
                                        marginHorizontal: wp(1),
                                        marginBottom: hp(1.5),
                                        shadowColor: '#000',
                                        shadowOffset: { width: 0, height: 1 },
                                        shadowOpacity: 0.05,
                                        shadowRadius: 1,
                                        elevation: 2,
                                        alignItems: 'flex-start',
                                    }}
                                    >
                                    {/* Avatar */}
                                    <View
                                        style={{
                                        height: wp(10),
                                        width: wp(10),
                                        borderRadius: wp(5),
                                        backgroundColor: colors.c_green,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        marginRight: wp(3),
                                        }}
                                    >
                                        <ResponsiveText size={3.5} weight="600" color={colors.white}>
                                        {avatarLetter}
                                        </ResponsiveText>
                                    </View>

                                    {/* Content + Edit Icon */}
                                    <View style={{ flex: 1 }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <ResponsiveText size={3.5} weight="500" color={colors.black} style={{ flex: 1 }}
                                        
                                        maxWidth={wp(50)}
                                        >
                                            {item.comment}
                                        </ResponsiveText>

                                        {/* Edit Icon */}
                                        <TouchableOpacity onPress={() => handleEditComment(item)}>
                                            <Icon
                                            source={globalpath.edit} // use a proper pencil/edit icon here
                                            size={14}
                                            margin={[hp(0.4),wp(1),0,0]}
                                            />
                                        </TouchableOpacity>
                                        </View>

                                        {/* Timestamp */}
                                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: hp(1) }}>
                                        <ResponsiveText size={2.5} weight="300" color={colors.grey}>
                                            {new Date(item.created_at).toLocaleString('en-US', {
                                            day: '2-digit',
                                            month: 'short',
                                            year: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            })}
                                        </ResponsiveText>
                                        </View>
                                    </View>
                                    </View>
                                );
                                })}


                        </ScrollView>
                        </View>
                    {/* Comment Input + Add Button */}
                    <TextInput
                        placeholder="Write a comment..."
                        placeholderTextColor={colors.grey}
                        value={commentText}
                        onChangeText={setCommentText}
                        style={[
                            styles.Input_Notes,
                            {
                            color: colors.black,
                            height: hp(10),
                            marginTop: hp(2),
                            borderColor: isEditing ? colors.Light_theme_maincolour : colors.lightGrey,
                            borderWidth: 1.2,
                            },
                        ]}
                        multiline
                        />

                        <TouchableOpacity
                        style={[styles.Add_Service_Button_style, { backgroundColor: colors.Light_theme_maincolour }]}
                        onPress={handleSubmitComment}
                        >
                        <ResponsiveText color={colors.white}>
                            {isEditing ? 'Update Comment' : 'Add Comment'}
                        </ResponsiveText>
                        </TouchableOpacity>

                    </View>


           {/* <View style={styles.ADNcard}>
                <View style={[styles.TotalAmount, { marginHorizontal: wp(-4) }]}>
                    <ResponsiveText size={4.3} weight={'500'} color={getTextColor()}>
                        {AppText.ADDITIONAL_NOTES}
                    </ResponsiveText>
                    {isCurrentBarber && showSaveIcon && (
                        <TouchableOpacity onPress={handleSaveNotes}>
                            <Icon
                                source={globalpath.save}
                                style={styles.saveIcon}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View> */}

            {/* Notes Input - Read-only if not current barber */}
            {/* <TextInput
                placeholder={isCurrentBarber ? AppText.ENTER_ADDITIONAL_NOTES : "Notes not editable"}
                placeholderTextColor={colors.grey}
                style={[
                    styles.Input_Notes,
                    {
                        color: colors.grey,
                        backgroundColor: isCurrentBarber ? colors.lightGrey : colors.lightGrey4
                    }
                ]}
                multiline={true}
                numberOfLines={4}
                value={notesText}
                onChangeText={isCurrentBarber ? handleNotesChange : null}
                editable={isCurrentBarber}
            /> */}

            {/* Modals */}
            <AssignBarberModal
                visible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                onBarberSelect={handleBarberSelect}
                previouslySelectedBarbers={selectedBarbers}
                salon={{}}
                barberdetails={barberdetails}
            />

            <AddServiceModal
                visible={modalVisible}
                onClose={() => setModalVisible(false)}
                onServiceSelect={(selected) => {
                    console.log('Selected Services:', selected);
                    setModalVisible(false);
                }}
                previouslySelectedServices={[]}
                salon={{}}
            />
        </View>
    )
}

export default Barber_customer__Details

const styles = StyleSheet.create({
    DetailCard: {
        backgroundColor: "red",
        paddingHorizontal: wp(5),
        paddingVertical: hp(2),
        elevation: 5,
        marginHorizontal: wp(5),
        borderRadius: wp(3),
        marginTop: hp(2)
    },
    BarberDetail_Image: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    BarberImage: {
        height: hp(7),
        width: wp(14),
        resizeMode: "contain"
    },
    Barberinfo: {
        alignItems: "center",
        flexDirection: "row",
        marginVertical: hp(0.4)
    },
    Icon: {
        height: hp(4),
        width: wp(7),
        resizeMode: "contain",
        marginRight: wp(3)
    },
    ADNIcon: {
        height: hp(3),
        width: wp(5),
        resizeMode: "contain",
    },
    TotalAmount: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    ADNcard: {
        paddingHorizontal: wp(5),
        paddingVertical: hp(2),
        marginHorizontal: wp(5),
        borderRadius: wp(3),
        marginTop: hp(2)
    },
    saveIcon: {
      height: hp(3),
      width: wp(5),
      resizeMode: 'contain',
      tintColor: colors.Light_theme_maincolour, // or any color you prefer

  },
  Input_Notes: {
      height: hp(12), // Increased height for multiline
      textAlignVertical: 'top', // Align text to top for multiline
      justifyContent: "flex-start", // Changed to flex-start for multiline
      backgroundColor: colors.lightGrey,
      paddingHorizontal: wp(5),
      paddingVertical: hp(2),
      borderWidth: 0.7,
      marginHorizontal: wp(5),
      borderRadius: wp(1.5),
      // marginTop: hp(2),
      fontSize: hp(1.8),
  },
    Add_Service_Button_style: {
        alignSelf: "flex-end",
        paddingHorizontal: wp(2),
        paddingVertical: hp(1),
        backgroundColor: "red",
        marginRight: wp(5),
        borderRadius: wp(1),
        marginTop: hp(2)
    }
})