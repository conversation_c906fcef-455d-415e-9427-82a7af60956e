import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import useAppText from '../../../../Custom/AppText';
import { hp, wp } from '../../../../Custom/Responsiveness';
import useTheme from '../../../../Redux/useTheme';
import ResponsiveText from '../../../../Custom/RnText';
import { colors } from '../../../../Custom/Colors';
import { Update_Appointment } from '../../../../Services/API/Endpoints/barber/appointment_details';
import { update_barber_appointment , update_customer_appointment } from '../../../../Services/API/Endpoints/barber/add_appointment';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ProgressStepper = ({appointment}) => {
  console.log("apintment in progress stepper",appointment)

  const Apptext = useAppText();
  const { getTextColor, backgroundColor } = useTheme();
  const [step, setStep] = useState(-1);
  const [stepLabels, setStepLabels] = useState([]);
  const [isCurrentBarber, setIsCurrentBarber] = useState(false);
  const [userRole, setUserRole] = useState(null);

  useEffect(() => {
    // Check if the logged-in user is a barber and if they are the barber for this appointment
    const checkBarberPermission = async () => {
      try {
        // Get user role
        const userData = await AsyncStorage.getItem('userData');
        if (userData) {
          const { role } = JSON.parse(userData);
          setUserRole(role);

          // If user is a barber, check if they are the barber for this appointment
          if (role === 'barber') {
            const barberData = await AsyncStorage.getItem('barberData');
            if (barberData) {
              const parsed = JSON.parse(barberData);
              const loggedInBarber = Array.isArray(parsed) ? parsed[0] : parsed;

              // Check if the logged-in barber's ID matches the appointment's barber ID
              if (loggedInBarber && appointment && loggedInBarber.id === appointment.barber) {
                setIsCurrentBarber(true);
                console.log('✅ Logged-in barber is the barber for this appointment');
              } else {
                setIsCurrentBarber(false);
                console.log('❌ Logged-in barber is NOT the barber for this appointment');
              }
            }
          } else {
            // If user is admin, they can perform all actions
            setIsCurrentBarber(true);
          }
        }
      } catch (error) {
        console.error('Error checking barber permission:', error);
      }
    };

    checkBarberPermission();

    // Set the labels based on payment method
    if (appointment?.payment_recieved === true) {
      setStepLabels([Apptext.PAYMENT_RECIEVED, Apptext.JOB_START, Apptext.JOB_DONE]);

      // For online payments, first step is always completed
      let initialStep = 0;

      // Check appointment status to set the correct step
      if (appointment.status === 'in_progress') {
        initialStep = 1; // Job started
      } else if (appointment.status === 'completed') {
        initialStep = 2; // Job done
      }

      setStep(initialStep);

    } else {
      setStepLabels([Apptext.JOB_START, Apptext.JOB_DONE, Apptext.PAYMENT_RECIEVED]);

      // Set initial step based on appointment status
      let initialStep = -1;

      if (appointment.status === 'in_progress') {
        initialStep = 0; // Job started
      } else if (appointment.status === 'completed') {
        initialStep = 1; // Job done

        // If job is completed and payment is received, set to final step
        if (appointment.payment_recieved === true) {
          initialStep = 2; // Payment received
        }
      } else if (appointment.status === 'payment_received') {
        initialStep = 2; // Payment received
      }

      setStep(initialStep);
    }
  }, [appointment]);

  const handleNextStep = async () => {
    if (step < 2) {
      // Determine the next step and status
      const nextStep = step + 1;

      let status;
      if (appointment?.payment_way === 'online') {
        // For online payment
        switch (nextStep) {
          case 1:
            status = 'job_start';
            break;
          case 2:
            status = 'job_done';
            break;
        }
      } else {
        // For cash payment
        switch (nextStep) {
          case 0:
            status = 'job_start';
            break;
          case 1:
            status = 'job_done';
            break;
          case 2:
            status = 'payment_received';
            break;
        }
      }

      // Map to API-compatible status
      let apiStatus = status;
      if (status === 'job_start') apiStatus = 'in_progress';
      else if (status === 'job_done') apiStatus = 'completed';

      // Show confirmation alert for both job start and job completion
      if (status === 'job_start') {
        Alert.alert(
          "Confirm Job Start",
          "Are you sure you want to start this job?",
          [
            {
              text: "Cancel",
              style: "cancel"
            },
            {
              text: "Yes, Start Job",
              onPress: () => updateAppointmentStatus(nextStep, apiStatus)
            }
          ]
        );
      } else if (status === 'job_done') {
        Alert.alert(
          "Confirm Job Completion",
          "Are you sure you want to mark this job as done?",
          [
            {
              text: "Cancel",
              style: "cancel"
            },
            {
              text: "Yes, Complete Job",
              onPress: () => updateAppointmentStatus(nextStep, apiStatus)
            }
          ]
        );
      } else if (status === 'payment_received') {
        // For payment received step, show confirmation
        Alert.alert(
          "Confirm Payment",
          "Are you sure you want to mark this payment as received?",
          [
            {
              text: "Cancel",
              style: "cancel"
            },
            {
              text: "Yes, Payment Received",
              onPress: () => updateAppointmentStatus(nextStep, apiStatus)
            }
          ]
        );
      } else {
        // For other steps, proceed without confirmation
        updateAppointmentStatus(nextStep, apiStatus);
      }
    }
  };


  // Function to update the appointment status
  const updateAppointmentStatus = async (nextStep, apiStatus) => {
    try {
      setStep(nextStep); // Update the UI step

      // Check if this is the payment received step
      const isPaymentReceivedStep = apiStatus === 'payment_received';

      if (appointment.barber_appointment === true) {
        // For barber appointments
        const payload = isPaymentReceivedStep
          ? {
              payment_recieved: true
            }
          : {
              status: apiStatus
            };

        console.log("Payload of PATCH API:", payload);
        console.log("The ID is:", appointment.id);
        await update_barber_appointment(appointment.id, payload);
      }
      else {
        // For customer appointments
        const payload = isPaymentReceivedStep
          ? {
              ...appointment,
              payment_recieved: true
            }
          : {
              ...appointment,
              status: apiStatus
            };

        console.log("Payload of PATCH API:", payload);
        console.log("The ID is:", appointment.id);
        await update_customer_appointment(appointment.id, payload);
      }

      console.log('Successfully updated appointment status to:', apiStatus);
    } catch (error) {
      console.error('Failed to update appointment status:', error);
      setStep(step); // Revert step if failed
    }
  };


  return (
    <View>
    <View style={styles.container}>
      {/* Stepper Line with Dots */}
      <View style={styles.stepper}>
        {[0, 1, 2].map((_, index) => (
          <React.Fragment key={index}>
            <View
              style={[
                styles.circle,
                step >= index && styles.activeCircle,
              ]}
            >
               <ResponsiveText
              color={step >= index ? colors.white : getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, , 0]}
            >
           {index + 1}
            </ResponsiveText>
            </View>
            {index < 2 && (
              <View
                style={[
                  styles.line,
                  step >= index + 1 && styles.activeLine,
                ]}
              />
            )}
          </React.Fragment>
        ))}
      </View>

      <View style={styles.labelContainer}>
        {stepLabels.map((label, index) => (
          <Text
            width={wp(22)}
            key={index}
            style={[
              styles.label,
              step >= index && styles.activeLabel,
            ]}
            numberOfLines={2}
          >
            {label}
          </Text>
        ))}
      </View>
    </View>

    {step < 2 ? (
      // Show button if current barber AND (status is not completed OR payment is not received)
      isCurrentBarber && (appointment.status !== "completed" || (appointment.status === "completed" && appointment.payment_recieved === false)) ? (
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.c_green }]}
          onPress={handleNextStep}
        >
          <ResponsiveText color={colors.white} weight={'600'}>
            {/* Show the appropriate button text based on current step */}
            {appointment?.payment_recieved === true ?
              (step === 0 ? Apptext.JOB_START : Apptext.JOB_DONE) :
              (step === -1 ? Apptext.JOB_START : step === 0 ? Apptext.JOB_DONE : Apptext.PAYMENT_RECIEVED)
            }
          </ResponsiveText>
        </TouchableOpacity>
      ) : (
        // If not the current barber or (status is completed AND payment is received), don't show the button
        null
      )
    ) : (
      <View style={styles.button}>
        <ResponsiveText color={colors.grey} size={4.3} weight="600">
          {Apptext.JOB_SUCCESS}
        </ResponsiveText>
      </View>
    )}
    </View>
  );
};

export default ProgressStepper;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: wp(15),
    // backgroundColor:"red",
    // backgroundColor: '#fff',
    marginTop:hp(3),
    marginVertical: hp(1),
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(1),
    width: '100%', // Ensure the container takes full width
  },
  label: {
    // color: '#999',
    color:'#ccc',
    fontSize: hp(1.7),
    textAlign: 'center', // Center the text under the circle
    marginHorizontal: wp(-7),
    // flex: 1, // Allow each label to take full width if needed
  },
  activeLabel: {
    color: colors.c_green,
    fontSize: hp(1.7),
    // fontWeight: 'bold',
  },
  activeLabel2: {
    color: colors.c_green,
    fontSize: hp(1.7),
    // fontWeight: 'bold',
  },
  stepper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // backgroundColor:"red",
    marginBottom: hp(.4),
  },
  circle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderColor: '#ccc',
    borderWidth: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.00,
    elevation: 1,
  },
  activeCircle: {
    backgroundColor: colors.c_green,
    borderWidth: 0,
    borderColor: 'transparent',
    shadowColor: colors.c_green,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.27,
    shadowRadius: 4.65,
    elevation: 6,
  },
  circleText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  line: {
    height: 3,
    flex: 1,
    backgroundColor: '#e0e0e0',
    marginHorizontal: wp(1),
  },
  activeLine: {
    backgroundColor: colors.c_green,
    shadowColor: colors.c_green,
    shadowOffset: {
      width: 0,
      height: 0.5,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.1,
    elevation: 2,
  },
  button: {
    alignSelf: 'flex-end',
    marginTop: hp(2),
    paddingVertical: hp(1),
    paddingHorizontal: wp(5.5),
    marginRight: wp(6),
    borderRadius: wp(1.5),
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  circleText: {
    color: '#fff',
    // fontWeight: 'bold',
    fontSize:hp(2.5)
  },

  inactiveCircleText: {
    color: '#ccc', // Match border color

  },
  successButton: {
    backgroundColor: '#4CAF50', // A slightly different green
    borderWidth: 0,
    borderRadius: wp(3),
  }
});
