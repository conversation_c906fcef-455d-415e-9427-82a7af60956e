import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, Platform } from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const TimeInput = ({ startTime, endTime, onStartTimeChange, onEndTimeChange }) => {
  const AppText = useAppText();
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  const formatTime = (time) => {
    if (!time) return '';
    return new Date(time).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const roundToNearestFifteen = (date) => {
    const minutes = date.getMinutes();
    const roundedMinutes = Math.round(minutes / 15) * 15;
    const newDate = new Date(date);
    newDate.setMinutes(roundedMinutes);
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);
    return newDate;
  };

  const handleStartTimeConfirm = (date) => {
    const roundedDate = roundToNearestFifteen(date);
    onStartTimeChange(roundedDate);
    setShowStartPicker(false);
  };

  const handleEndTimeConfirm = (date) => {
    const roundedDate = roundToNearestFifteen(date);
    onEndTimeChange(roundedDate);
    setShowEndPicker(false);
  };

  const minuteInterval = 15;

  return (
    <View style={styles.container}>
      <ResponsiveText color={getTextColor()} size={4.2} weight="600" margin={[hp(2), 0, hp(1.5), 0]}>
        {AppText.APPOINTMENT_TIME}
      </ResponsiveText>

      <View style={styles.timeContainer}>
        {/* Start Time */}
        <View style={[styles.timeInputContainer,]}>
          <ResponsiveText color={getTextColor()} size={3.5} margin={[0, 0, hp(0.5), 0]}>
            {AppText.START_TIME}
          </ResponsiveText>
          <TouchableOpacity
            style={[styles.timeInput, { borderColor: getDark_Theme() }]}
            onPress={() => setShowStartPicker(true)}
          >
            <Icon
              source={globalpath.watch}
              size={wp(5)}
              tintColor={colors.darkGrey}
            />
            <ResponsiveText color={startTime ? getTextColor() : colors.grey} margin={[0, 0, 0, wp(2)]} textAlign={'center'}>
              {startTime ? formatTime(startTime) : AppText.SELECT_START_TIME}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        {/* End Time */}
        {/* <View style={[styles.timeInputContainer, ]}>
          <ResponsiveText color={getTextColor()} size={3.5} margin={[0, 0, hp(0.5), 0]}>
            {AppText.END_TIME}
          </ResponsiveText>
          <TouchableOpacity
            style={[styles.timeInput, { borderColor: getDark_Theme() }]}
            onPress={() => setShowEndPicker(true)}
          >
                 <Icon
              source={globalpath.watch}
              size={wp(5)}
              tintColor={colors.darkGrey}
            />
            <ResponsiveText color={endTime ? getTextColor() : colors.grey} margin={[0, 0, 0, wp(2)]}>
              {endTime ? formatTime(endTime) : AppText.SELECT_END_TIME}
            </ResponsiveText>
       
          </TouchableOpacity>
        </View> */}
      </View>

      {/* Time Pickers */}
      <DateTimePickerModal
        isVisible={showStartPicker}
        mode="time"
        onConfirm={handleStartTimeConfirm}
        onCancel={() => setShowStartPicker(false)}
        date={startTime ? new Date(startTime) : new Date()}
        is24Hour={false}
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        locale="en_US"
        minuteInterval={minuteInterval}
      />

      {/* <DateTimePickerModal
        isVisible={showEndPicker}
        mode="time"
        onConfirm={handleEndTimeConfirm}
        onCancel={() => setShowEndPicker(false)}
        date={endTime ? new Date(endTime) : new Date()}
        is24Hour={false}
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        locale="en_US"
      /> */}
    </View>
  );
};

export default TimeInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeInputContainer: {
    flex: 1,
    marginHorizontal: wp(1),
  },
  timeInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    height: hp(6),
  },
}); 