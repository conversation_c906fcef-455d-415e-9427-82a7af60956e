import React, { useState , useCallback , useEffect , useMemo } from 'react';
import { StyleSheet, View, TouchableOpacity, TextInput, ScrollView, FlatList  , ActivityIndicator} from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ServiceItem from './ServiceItem';
import { get_all_service } from '../../../Services/API/Endpoints/barber/add_appointment';
import { useNavigation , useFocusEffect } from '@react-navigation/native';



const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const AddServiceContent = ({ onClose, onSave, selectedServices  ,onUpdateDurations}) => {
  const AppText = useAppText();
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [allservices, setAllservices] = useState([]);
  const [selectedItems, setSelectedItems] = useState(selectedServices || []);



      useFocusEffect(
          useCallback(() => {
            fetch_all_services();
          }, []),
        );



         const fetch_all_services = async () => {
                try {
                  setLoading(true);
                  setRefreshing(true);

                  const response = await get_all_service();
                  console.log('🟢 All services fetched:', response);
                  if (Array.isArray(response)) {
                    const sortedServices = response.sort((a, b) => b.id - a.id);
                    setAllservices(sortedServices);

                  }

                } catch (error) {
                  console.error('❌ Failed to fetch services:', error);
                } finally {
                  setLoading(false);
                  setRefreshing(false);
                }
              };


              const servicesWithColors = useMemo(() => {
                return allservices.map(service => ({
                  ...service,
                  color: service.color && service.color !== "" && service.color !== null ? service.color : generateRandomColor()
                }));
              }, [allservices]);

  const filteredServices = useMemo(() => {
    if (!searchText) return servicesWithColors;
    return servicesWithColors.filter(service =>
      service.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [searchText, servicesWithColors]);

  const handleSearch = (text) => {

    setSearchText(text);
  };

  const handleSave = () => {
    console.log("Selected Services:", selectedItems); // Log to console
    onSave(selectedItems);
    onClose();
  };


  // const toggleServiceSelection = (service) => {
  //   setSelectedItems(prev => {
  //     const isSelected = prev.some(item => item.id === service.id);
  //     if (isSelected) {
  //       // If already selected, deselect it
  //       return [];
  //     } else {
  //       // If not selected, select only this service (replacing any previously selected)
  //       return [service];
  //     }
  //   });
  // };

const toggleServiceSelection = (service) => {
  setSelectedItems(prev => {
    const isSelected = prev.some(item => item.id === service.id);
    if (isSelected) {
      // Remove the service
      return prev.filter(item => item.id !== service.id);
    } else {
      // Add the service
      return [...prev, service];
    }
  });
};


  const renderServiceItem = ({ item, index }) => (
    <ServiceItem
    service={item}
    isLast={index === filteredServices.length - 1}
    isSelected={selectedItems.some(selected => selected.id === item.id)}
    onPress={() => toggleServiceSelection(item)}
  />
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
      >
        {AppText.NO_RESULTS_FOUND}
      </ResponsiveText>
    </View>
  );

  return (
    <View style={styles.mainContainer}>
      {/* Header with Back Button and Title */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose}>
          <Icon
            source={globalpath.goback}
            size={wp(4.5)}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>
        <ResponsiveText
          color={getTextColor()}
          size={4.5}
          weight="600"
        >
          {AppText.ADD_SERVICE}
        </ResponsiveText>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        {/* Search Input */}
        <View style={[styles.searchContainer, { borderColor: getDark_Theme() }]}>
          <Icon
            source={globalpath.search}
            size={wp(5)}
            tintColor={getTextColor()}
            margin={[0, wp(2), 0, 0]}
          />
          <TextInput
            style={[styles.searchInput, { color: getTextColor() }]}
            placeholder={AppText.SEARCH_SERVICES}
            placeholderTextColor={colors.grey}
            value={searchText}
            onChangeText={handleSearch}
          />
        </View>

        {/* Services List */}
        {loading ? (
              <View style={styles.emptyContainer}>
                <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
              </View>
            ) : (
              <FlatList
                data={filteredServices}
                renderItem={renderServiceItem}
                keyExtractor={item => item.id.toString()}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContent}
                ListEmptyComponent={renderEmptyComponent}
              />
            )}
      </View>

      {/* Save Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
        >
          <ResponsiveText
            color={colors.white}
            size={4.5}
            weight="600"
          >
            {AppText.SAVE}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddServiceContent;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
    marginTop: hp(1.5),
  },
  placeholder: {
    width: wp(6),
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    height: hp(5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  searchInput: {
    flex: 1,
    fontSize: wp(3.8),
    backgroundColor: 'transparent',
  },
  listContent: {
    paddingBottom: hp(2),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
  },
  bottomContainer: {
    paddingHorizontal: wp(4),
    paddingBottom: hp(3),
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
  },
});