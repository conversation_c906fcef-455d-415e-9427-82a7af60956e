import React, { useState, useCallback, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, RefreshControl, Modal } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import AddAppointmentModal from './AddAppointmentModal';
import BarberHeader from './BarberHeader';
import CalendarView from './CalendarView';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { get_barber_deatils, list_barber_appointments , get_barber_breaks , get_shops_barbers} from '../../../Services/API/Endpoints/barber/dashboard';
import Loader from '../../../Custom/loader';
import { useRoute } from '@react-navigation/native';
import ResponsiveText from '../../../Custom/RnText';
import ManageBreaks from './ManageBreaks';
import AddAppointmentContent from './AddAppointmentContent';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomAlert from '../../../Components/CustomAlert';

const Barber_DailyView = ({salonData, userRole, AllShopsData, selectedDate: propSelectedDate}) => {
  console.log("salon id -- userrole ", salonData?.id , userRole)
  console.log("AllShopsData in daily view", AllShopsData)

  const navigation = useNavigation();
  const [currentBarberPairIndex, setCurrentBarberPairIndex] = useState(0);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [shopid, setShopID] = useState(null);
  const [barbers, setBarbers] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [selectedDate, setSelectedDate] = useState(formatToYYYYMMDD(propSelectedDate || new Date()));
  const [breaks, setBreaks] = useState([]);
  const [selectedBarber, setSelectedBarber] = useState(null);
  const [showManageBreaks, setShowManageBreaks] = useState(false);

  // Log when showManageBreaks changes
  useEffect(() => {
    console.log("showManageBreaks changed to:", showManageBreaks);
  }, [showManageBreaks]);

  // Update selected date when prop changes
  useEffect(() => {
    if (propSelectedDate) {
      const formattedDate = formatToYYYYMMDD(propSelectedDate);
      console.log("Selected date changed to:", formattedDate);
      setSelectedDate(formattedDate);

      // Fetch appointments for the new date
      if (shopid) {
        fetch_Appointments(formattedDate, shopid);
      } else if (userRole === 'admin' && salonData?.id) {
        fetch_Appointments(formattedDate, salonData.id);
      }
    } else {
      // If date is cleared, reset to current date
      const currentDate = formatToYYYYMMDD(new Date());
      setSelectedDate(currentDate);

      // Fetch appointments for current date
      if (shopid) {
        fetch_Appointments(currentDate, shopid);
      } else if (userRole === 'admin' && salonData?.id) {
        fetch_Appointments(currentDate, salonData.id);
      }
    }
  }, [propSelectedDate, shopid, userRole, salonData?.id]);
  const [showAddAppointment, setShowAddAppointment] = useState(false);
  const [showCustomAlert, setShowCustomAlert] = useState(false);

  // useFocusEffect(
  //   useCallback(() => {
  //     fetchbarberdetails();
  //   }, []),
  // );
  // const fetchbarberdetails = async () => {
  //   try {
  //     setLoading(true);
  //     setRefreshing(true);

  //     const response = await get_barber_deatils();
  //     console.log('🟢Barber details fetched:', response);

  //     setShopID(response[0].shop);

  //     const id = response[0].shop;

  //     const current_date = formatToYYYYMMDD(new Date());
  //     console.log('📅 Current Date:', current_date);
  //     await get_shops(id);
  //     await fetchBarberBreaks(id);
  //     await fetch_Appointments(current_date, id);

  //   } catch (error) {
  //     console.error('❌ Failed to fetch services:', error);
  //   } finally {
  //     setLoading(false);
  //     setRefreshing(false);
  //   }
  // };


  // const onRefresh = useCallback(() => {
  //   setRefreshing(true);
  //   fetchbarberdetails().then(() => setRefreshing(false));
  // }, []);

    // Consolidated and improved useFocusEffect
    useFocusEffect(
      useCallback(() => {
        // Only proceed if we have the required data
        if (!userRole) return;
        if (userRole === 'admin' && !salonData?.id) {
          console.log('Waiting for salonData.id...');
          return;
        }

        fetchbarberdetails();
      }, [userRole, salonData?.id])
    );

    // Fully memoized fetch function with proper dependencies
    const fetchbarberdetails = useCallback(async () => {
      console.log("Fetching with role:", userRole, "salonID:", salonData?.id);

      try {
        setLoading(true);
        setRefreshing(true);

        let targetId;
        if (userRole === 'admin') {
          if (!salonData?.id) {
            console.error('Admin user but no salonData.id available');
            return;
          }
          targetId = salonData.id;
        } else {
          const response = await get_barber_deatils();
          console.log("response momna",response)
          if (!response[0]?.shop) {
            console.error('No shop ID found in barber details');
            return;
          }
          targetId = response[0].shop;
        }

        setShopID(targetId);

        // Use selected date if available, otherwise use current date
        const dateToUse = selectedDate || formatToYYYYMMDD(new Date());
        console.log('📅 Date for fetching appointments:', dateToUse);

        // Execute all fetches in parallel for better performance
        await Promise.all([
          get_shops(targetId),
          fetchBarberBreaks(targetId),
          fetch_Appointments(dateToUse, targetId)
        ]);

      } catch (error) {
        console.error('❌ Failed to fetch services:', error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    }, [userRole, salonData?.id]);

    // Improved refresh handler
    const onRefresh = useCallback(() => {
      console.log("Refresh initiated, current role:", userRole);
      setRefreshing(true);
      fetchbarberdetails()
        .catch(error => console.error('Refresh failed:', error))
        .finally(() => setRefreshing(false));
    }, [fetchbarberdetails, userRole]);


  const handleAppointmentAdded = useCallback(async () => {
    console.log("Appointment added, refreshing data...");
    setLoading(true);
    try {
      // Use selected date if available, otherwise use current date
      const dateToUse = selectedDate || formatToYYYYMMDD(new Date());

      if (userRole === 'admin' && salonData?.id) {
        await Promise.all([
          get_shops(salonData.id),
          fetchBarberBreaks(salonData.id),
          fetch_Appointments(dateToUse, salonData.id)
        ]);
      } else if (shopid) {
        await Promise.all([
          get_shops(shopid),
          fetchBarberBreaks(shopid),
          fetch_Appointments(dateToUse, shopid)
        ]);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setLoading(false);
    }
  }, [userRole, salonData?.id, shopid, selectedDate]);


  const get_shops = async (id) => {
    try {
      const response = await get_shops_barbers(id);
      console.log('🟢Barber details fetched:', response);

      // Process the response to extract unique barbers
      const uniqueBarbers = [];
      const barberMap = new Map();

      // If user is a barber, get the logged-in barber details from AsyncStorage
      let loggedInBarber = null;
      if (userRole === 'barber') {
        try {
          const barberData = await AsyncStorage.getItem('barberData');
          if (barberData) {
            const parsed = JSON.parse(barberData);
            loggedInBarber = Array.isArray(parsed) ? parsed[0] : parsed;
            console.log('✅ Logged-in barber retrieved:', loggedInBarber);
          }
        } catch (error) {
          console.log('❌ Error fetching logged-in barber details:', error);
        }
      }

      // If user is a barber, put the logged-in barber at index 0
      if (userRole === 'barber' && loggedInBarber) {
        // First add the logged-in barber
        uniqueBarbers.push({
          id: loggedInBarber.id,
          name: loggedInBarber.full_name,
          full_name: loggedInBarber.full_name,
          shop: loggedInBarber.shop,
          isLoggedInBarber: true, // Flag to identify the logged-in barber
          ...loggedInBarber
        });
        barberMap.set(loggedInBarber.id, true);
      }

      // Then add the rest of the barbers
      response.forEach(barber => {
        if (!barberMap.has(barber.id)) {
          barberMap.set(barber.id, true);
          uniqueBarbers.push({
            id: barber.id,
            name: barber.full_name,
            full_name: barber.full_name,
            shop: barber.shop,
            isLoggedInBarber: false,
            ...barber
          });
        }
      });

      setBarbers(uniqueBarbers);
    } catch (error) {
      console.error('❌ Failed to fetch breaks:', error);
    }
  };

// Update your fetchBarberBreaks function to set the breaks state
const fetchBarberBreaks = async (id) => {
  console.log('Fetching breaks for barber ID:', id);
  try {
    const res = await get_barber_breaks(id);
    console.log('🟠 Breaks fetched:', res);
    setBreaks(res); // Store breaks in state
  } catch (error) {
    console.error('❌ Failed to fetch breaks:', error);
  }
};

  function formatToYYYYMMDD(dateInput) {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return null;

    // Get the date in local timezone
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  const fetch_Appointments = async (currentDate, id) => {
    console.log("currentDate , id", currentDate, id)
    const start_date = currentDate;
    const end_date = currentDate;

    try {
      setLoading(true);
      setRefreshing(true);

      const response = await list_barber_appointments(start_date, end_date, id);
      console.log('🟢 Details of barbers fetched:--------------', response);


      setAppointments(response);
      // setShopID(response[0].shop);

    } catch (error) {
      console.error('❌ Failed to fetch Details of barbers fetched::', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  const handlePrevious = () => {
    if (barbers.length <= 1) return; // If there is only 1 barber, don't allow previous

    // If we're going back to index 0 and user is a barber, we'll show only the logged-in barber
    if (currentBarberPairIndex === 1 && userRole === 'barber') {
      setCurrentBarberPairIndex(0);
      return;
    }

    setCurrentBarberPairIndex(prev => (prev - 1 < 0 ? 0 : prev - 1));
  };

  const handleNext = () => {
    if (barbers.length <= 1) return; // If there is only 1 barber, don't allow next

    // Special case: If we're at index 0 as a barber user (showing only logged-in barber)
    // We want to move to showing the next barber
    if (userRole === 'barber' && currentBarberPairIndex === 0) {
      setCurrentBarberPairIndex(1);
      return;
    }

    // Calculate the maximum index based on the user role
    let maxIndex;
    if (userRole === 'admin') {
      // For admin, we show 2 barbers at a time, so max index is (total barbers / 2) rounded up - 1
      maxIndex = Math.ceil(barbers.length / 2) - 1;
    } else {
      // For barber, we show 1 barber at a time, so max index is (total barbers - 1)
      maxIndex = barbers.length - 1;
    }

    setCurrentBarberPairIndex(prev => {
      return prev + 1 > maxIndex ? maxIndex : prev + 1;
    });
  };

    const getCurrentBarberPair = () => {
      if (barbers.length === 0) return [];

      // If user is a barber and we're at index 0, only show the logged-in barber
      if (userRole === 'barber' && currentBarberPairIndex === 0) {
        // Find the logged-in barber (should be at index 0)
        const loggedInBarber = barbers.find(barber => barber.isLoggedInBarber);
        if (loggedInBarber) {
          return [loggedInBarber.name];
        }
      }

      // For admin, show two barbers at a time
      if (userRole === 'admin') {
        const startIndex = currentBarberPairIndex * 2;
        const endIndex = Math.min(startIndex + 2, barbers.length);
        return barbers.slice(startIndex, endIndex).map(barber => barber.name);
      }

      // For barber, show only one barber at a time
      const index = currentBarberPairIndex;
      if (index < barbers.length) {
        return [barbers[index].name];
      }

      return [];
    };


  const handleAddPress = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSelectedBarber(null); // Reset selected barber when modal is closed
  };

  const handleBarberPress = (barberName) => {
    console.log("Barber pressed:", barberName);
    console.log("All barbers:", JSON.stringify(barbers));

    // Find the barber object by name
    const barberObj = barbers.find(b => b.name === barberName);
    console.log("Found barber object:", barberObj);

    // If user is a barber, check if they're trying to manage breaks for another barber
    if (userRole === 'barber' && barberObj && !barberObj.isLoggedInBarber) {
      setShowCustomAlert(true);
      return;
    }

    if (barberObj) {
      console.log("Setting selected barber and showing appointment modal");
      // Add openManageBreaks flag to the barber object
      const barberWithFlag = {
        ...barberObj,
        openManageBreaks: true
      };
      setSelectedBarber(barberWithFlag);
      setIsModalVisible(true);
    } else {
      console.log("Barber object not found for name:", barberName);
      // Try to find by full_name instead of name
      const barberByFullName = barbers.find(b => b.full_name === barberName);
      if (barberByFullName) {
        // If user is a barber, check if they're trying to manage breaks for another barber
        if (userRole === 'barber' && !barberByFullName.isLoggedInBarber) {
          setShowCustomAlert(true);
          return;
        }

        console.log("Found barber by full_name:", barberByFullName);
        // Add openManageBreaks flag to the barber object
        const barberWithFlag = {
          ...barberByFullName,
          openManageBreaks: true
        };
        setSelectedBarber(barberWithFlag);
        setIsModalVisible(true);
      } else {
        // If we still can't find the barber, try a more flexible approach
        console.log("Trying flexible matching for barber name:", barberName);
        for (const barber of barbers) {
          console.log(`Checking barber: ${barber.name} / ${barber.full_name}`);
          if (barber.name?.includes(barberName) || barber.full_name?.includes(barberName)) {
            // If user is a barber, check if they're trying to manage breaks for another barber
            if (userRole === 'barber' && !barber.isLoggedInBarber) {
              setShowCustomAlert(true);
              return;
            }

            console.log("Found partial match:", barber);
            // Add openManageBreaks flag to the barber object
            const barberWithFlag = {
              ...barber,
              openManageBreaks: true
            };
            setSelectedBarber(barberWithFlag);
            setIsModalVisible(true);
            break;
          }
        }
      }
    }
  };

  const handleCloseManageBreaks = () => {
    setShowManageBreaks(false);
    setSelectedBarber(null);
  };

  const handleCloseAddAppointment = () => {
    setShowAddAppointment(false);
    setSelectedBarber(null);
  };


  return (
    <View style={styles.mainContainer}>
      {/* {
        loading ? (
         undefined
        ) : (
          <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.Light_theme_maincolour]}
              tintColor={colors.Light_theme_maincolour}
            />
          }
        >
          <BarberHeader
            currentBarbers={getCurrentBarberPair()}
            onPrevious={handlePrevious}
            onNext={handleNext}
          />
          <CalendarView
            currentBarbers={getCurrentBarberPair()}
            appointments={appointments}
            breaks={breaks}
          />


          </ScrollView>
        )
      }
  */}

  {
  loading ? (
    undefined
  ) : (
    barbers.length === 0 ? (
      <View style={{ alignItems: 'center', marginTop: hp(5) }}>
        <ResponsiveText size={4} weight="600" color={colors.grey}>
          No Data Available
        </ResponsiveText>
      </View>
    ) : (
    <View>
        <BarberHeader
          currentBarbers={getCurrentBarberPair()}
          onPrevious={handlePrevious}
          onNext={handleNext}
          onBarberPress={handleBarberPress}
          userRole={userRole}
          barberObjects={barbers}
          hasMoreBarbers={userRole === 'admin'
            ? currentBarberPairIndex < Math.ceil(barbers.length / 2) - 1
            : currentBarberPairIndex < barbers.length - 1}
          hasPreviousBarbers={currentBarberPairIndex > 0}
        />
          <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.Light_theme_maincolour]}
            tintColor={colors.Light_theme_maincolour}
          />
        }
      >

        {
          appointments.length === 0 && breaks.length === 0 ? (
            <View style={{ alignItems: 'center', marginTop: hp(5) }}>
              <ResponsiveText size={4} weight="600" color={colors.grey}>
                No Data Available
              </ResponsiveText>
            </View>
          ) : (
            <CalendarView
              currentBarbers={getCurrentBarberPair()}
              appointments={appointments}
              breaks={breaks}
              selectedDate={propSelectedDate || selectedDate}
            />
          )
        }

      </ScrollView>
      </View>
    )
  )
}

      <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddPress}
        activeOpacity={0.9}
      >
        <Icon
          source={globalpath.add}
          size={wp(10)}
          tintColor={colors.white}
        />
      </TouchableOpacity>
      {loading ? <Loader/> : undefined}

      {showManageBreaks && (
        <Modal
          visible={showManageBreaks}
          animationType="slide"
          transparent={true}
          onRequestClose={handleCloseManageBreaks}
          statusBarTranslucent={true}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <ManageBreaks
                onBack={handleCloseManageBreaks}
                salonData={salonData}
                onClose={handleCloseManageBreaks}
                onAppointmentAdded={handleAppointmentAdded}
                userRole={userRole}
                selectedBarber={selectedBarber}
              />
            </View>
          </View>
        </Modal>
      )}

      {showAddAppointment && (
        <Modal
          visible={showAddAppointment}
          animationType="slide"
          transparent={true}
          onRequestClose={handleCloseAddAppointment}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <AddAppointmentContent
                onClose={handleCloseAddAppointment}
                selectedDate={selectedDate}
                setSelectedDate={setSelectedDate}
                onAppointmentAdded={handleAppointmentAdded}
                salonData={salonData}
                userRole={userRole}
                selectedBarber={selectedBarber}
              />
            </View>
          </View>
        </Modal>
      )}

      <AddAppointmentModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        onAppointmentAdded={handleAppointmentAdded}
        salonData={salonData}
        userRole={userRole}
        selectedBarber={selectedBarber}
      />

      <CustomAlert
        visible={showCustomAlert}
        title="Permission Denied"
        message="You can only manage breaks for your own schedule."
        onClose={() => setShowCustomAlert(false)}
        type="error"
        buttonText="I Understand"
      />
    </View>

  );
};

export default Barber_DailyView;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  addButton: {
    position: 'absolute',
    bottom: hp(2),
    right: wp(4),
    width: wp(11),
    height: wp(11),
    borderRadius: wp(6),
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    backgroundColor:colors.Light_theme_maincolour
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  modalContent: {
    width: wp(90),
    maxHeight: hp(80),
    backgroundColor: colors.white,
    borderRadius: wp(3),
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  testButton: {
    position: 'absolute',
    right: wp(4),
    width: wp(25),
    height: wp(11),
    borderRadius: wp(6),
    backgroundColor: colors.Light_theme_maincolour,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 100,
  },
});