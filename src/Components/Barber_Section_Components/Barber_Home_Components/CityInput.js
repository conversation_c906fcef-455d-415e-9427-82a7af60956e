import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const CityInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.CITY}
      placeholder={AppText.ENTER_CITY}
      value={value}
      onChangeText={onChangeText}
    />
  );
};

export default CityInput; 