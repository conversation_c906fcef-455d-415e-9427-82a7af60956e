import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Modal, TouchableOpacity, Platform } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';

const DatePickerModal = ({ visible, onClose, onSave, initialDate, restrictToPresentAndFuture = false }) => {
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [selectedDate, setSelectedDate] = useState(initialDate || new Date().toISOString().split('T')[0]);
  const [minDate, setMinDate] = useState(null);

  // Helper function to get today's date in YYYY-MM-DD format
  const getTodayString = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Set minimum date to today if restrictToPresentAndFuture is true
  useEffect(() => {
    if (restrictToPresentAndFuture) {
      const todayString = getTodayString();
      setMinDate(todayString);

      // If initialDate is in the past, update selectedDate to today
      if (initialDate) {
        const initialDateObj = new Date(initialDate);
        const todayObj = new Date(todayString);

        if (initialDateObj < todayObj) {
          setSelectedDate(todayString);
        }
      }
    } else {
      setMinDate(null);
    }
  }, [restrictToPresentAndFuture, initialDate]);

  const handleSave = () => {
    onSave(selectedDate);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor, borderColor: getDark_Theme() }]}>
          <ResponsiveText
            color={getTextColor()}
            size={5}
            weight="bold"
            margin={[0, 0, hp(1), 0]}
          >
            {AppText.SELECT_DATE}
          </ResponsiveText>

          {restrictToPresentAndFuture && (
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3}
              weight="500"
              margin={[0, 0, hp(2), 0]}
              style={styles.infoText}
            >
              ✨ You can only select today or future dates ✨
            </ResponsiveText>
          )}

          <Calendar
            onDayPress={(day) => {
              // Extra safety check to ensure we don't select dates before today
              if (restrictToPresentAndFuture) {
                const selectedDay = new Date(day.dateString);
                const today = new Date(getTodayString());

                // Only set the date if it's today or in the future
                if (selectedDay >= today) {
                  setSelectedDate(day.dateString);
                }
              } else {
                setSelectedDate(day.dateString);
              }
            }}
            markedDates={{
              [selectedDate]: { selected: true, selectedColor: colors.Light_theme_maincolour }
            }}
            minDate={minDate}
            theme={{
              backgroundColor: backgroundColor,
              calendarBackground: backgroundColor,
              textSectionTitleColor: getTextColor(),
              selectedDayBackgroundColor: colors.Light_theme_maincolour,
              selectedDayTextColor: colors.white,
              todayTextColor: colors.Light_theme_maincolour,
              dayTextColor: getTextColor(),
              textDisabledColor: colors.grey,
              dotColor: colors.Light_theme_maincolour,
              selectedDotColor: colors.white,
              arrowColor: colors.Light_theme_maincolour,
              monthTextColor: getTextColor(),
              indicatorColor: colors.Light_theme_maincolour,
            }}
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: getDark_Theme() }]}
              onPress={onClose}
            >
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.CANCEL}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
            >
              <ResponsiveText color={colors.white} size={4}>
                {AppText.SAVE}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DatePickerModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    padding: wp(5),
    borderRadius: wp(3),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    flex: 1,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  infoText: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
});