import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import DatePickerModal from './DatePickerModal';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';

const DateOfBirthInput = ({ value, onChange }) => {
  const AppText = useAppText();
  const { getTextColor, getDark_Theme, backgroundColor } = useTheme();
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);

  const formatDate = (date) => {
    if (!date) return '';
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      const day = dateObj.getDate().toString().padStart(2, '0');
      const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
      const year = dateObj.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleSave = (date) => {
    if (date) {
      onChange(date);
    }
    setDatePickerVisible(false);
  };

  return (
    <View style={styles.container}>
      <ResponsiveText
        color={getTextColor()}
        size={4}
        weight="500"
        margin={[0, 0, hp(1), 0]}
      >
        {AppText.DATE_OF_BIRTH}
      </ResponsiveText>
      <TouchableOpacity
        style={[styles.input, { borderColor: getDark_Theme(), backgroundColor }]}
        onPress={() => setDatePickerVisible(true)}
      >
        <ResponsiveText
          color={value ? getTextColor() : colors.grey}
          size={3.8}
        >
          {value ? formatDate(value) : AppText.DD_MM_YYYY}
        </ResponsiveText>
        <Icon
          source={globalpath.calendar}
          size={wp(5)}
          tintColor={colors.darkGrey}
        />
      </TouchableOpacity>

      <DatePickerModal
        visible={isDatePickerVisible}
        onClose={() => setDatePickerVisible(false)}
        onSave={handleSave}
        initialDate={value}
      />
    </View>
  );
};

export default DateOfBirthInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  input: {
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    justifyContent: "space-between",
    flexDirection: 'row',
    alignItems: 'center',
  },
}); 