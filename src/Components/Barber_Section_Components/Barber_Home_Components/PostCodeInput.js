import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const PostCodeInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.POST_CODE}
      placeholder={AppText.ENTER_POST_CODE}
      value={value}
      onChangeText={onChangeText}
      keyboardType="numeric"
    />
  );
};

export default PostCodeInput; 