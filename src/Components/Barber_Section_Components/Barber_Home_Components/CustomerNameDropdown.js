import React, { useState , useCallback , useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { get_all_customers } from '../../../Services/API/Endpoints/barber/add_appointment';
import { useNavigation , useFocusEffect } from '@react-navigation/native';



const CustomerNameDropdown = ({ value, onChange, onShowPreviousBook, selectedCustomerName , allcustomers,setAllcustomers ,searchText,setSearchText,handleAddNew}) => {

  const AppText = useAppText();
  const { getDark_Theme, getTextColor, backgroundColor } = useTheme();
  const [showDropdown, setShowDropdown] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  const [matchFound, setMatchFound] = useState(false);
  

    useEffect(() => {
      if (selectedCustomerName) {
        setSearchText(selectedCustomerName);
      }
    }, [selectedCustomerName]);
    
    useFocusEffect(
        useCallback(() => {
          fetch_allCustomers();
        }, []),
      );



       const fetch_allCustomers = async () => {
              try {
                setLoading(true);
                setRefreshing(true);
          
                const response = await get_all_customers();
                console.log('🟢 All Customers fetched:', response);

                if (Array.isArray(response)) {
                  const sortedServices = response.sort((a, b) => b.id - a.id);
                  setAllcustomers(sortedServices);
                
                }
          
              } catch (error) {
                console.error('❌ Failed to fetch services:', error);
              } finally {
                setLoading(false);
                setRefreshing(false);
              }
            };

  const filteredCustomers = allcustomers.filter(customer =>
    customer.full_name.toLowerCase().includes(searchText.toLowerCase())
  );

  useEffect(() => {
    const exactMatch = allcustomers.some(customer => 
      customer.full_name.toLowerCase() === searchText.toLowerCase()
    );
    setMatchFound(exactMatch);
    if (!exactMatch) {
      onChange(null);
    }
  }, [searchText]);

  const selectedCustomer = allcustomers.find(customer => customer.id === value);

  const handleClear = () => {
    onChange(null);
    setSearchText('');
  };

  const getRecordsText = (customer) => {
    if (!customer) return '';
    const recordCount = customer.records.length;
    if (recordCount === 0) return AppText.NO_RECORD_FOUND;
    return `${recordCount} ${recordCount === 1 ? AppText.RECORD : AppText.RECORDS} ${AppText.FOUND}`;
  };

  const renderDropdownContent = () => {
    // if (value && selectedCustomer) {
    //   // Show only record information for selected customer
    //   return (
    //     <ScrollView style={styles.scrollView}>
    //       <View style={styles.recordInfoContainer}>
    //         <ResponsiveText 
    //           color={selectedCustomer.records.length === 0 ? colors.red : "green"} 
    //         //   weight="500"
    //         >
    //           {getRecordsText(selectedCustomer)}
    //         </ResponsiveText>
    //         {selectedCustomer.records.length > 0 && (
    //           <View style={styles.recordsList}>
    //             {selectedCustomer.records.map(record => (
    //               <View key={record.id} style={styles.recordItem}>
    //                 <ResponsiveText color={getTextColor()} size={3.5}>
    //                   {record.date} - {record.service}
    //                 </ResponsiveText>
    //               </View>
    //             ))}
    //           </View>
    //         )}
    //       </View>
    //     </ScrollView>
    //   );
    // } else {
      // Show customer names list
      return (
        <ScrollView style={styles.scrollView}>
          {filteredCustomers.length > 0 ? (
            filteredCustomers.map(customer => (
              <TouchableOpacity
                key={customer.id}
                style={[styles.dropdownItem, { borderBottomColor: getDark_Theme() }]}
                onPress={() => {
                  onChange(customer.id);
                  setSearchText(customer.full_name);
                  setShowDropdown(false);
                }}
              >
                <ResponsiveText color={getTextColor()}>{customer.full_name}</ResponsiveText>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.noRecordContainer}>
              <ResponsiveText color={getTextColor()} size={3.8}>
                {AppText.NO_RECORD_FOUND}
              </ResponsiveText>
            </View>
          )}
        </ScrollView>
      );
    // }
  };

  return (
    <View style={styles.container}>
      <View style={styles.add_icon}>
      <ResponsiveText color={getTextColor()} size={4} weight="500" margin={[hp(2), 0, hp(1.5), 0]}>
        {AppText.CUSTOMER_NAME}
      </ResponsiveText>

           <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={handleAddNew}
          >
            <View>
              <Icon
                source={globalpath.add}
                size={wp(5)}
                tintColor={colors.white}
              />
              {/* <ResponsiveText
                color={colors.white}
                size={4}
                weight="500"
                style={styles.addButtonText}
              >
                {AppText.ADD_NEW}
              </ResponsiveText> */}
            </View>
          </TouchableOpacity> 
      </View>


      

      <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
        <View style={styles.input}>
          <TextInput
            style={[styles.textInput, { color: getTextColor() }]}
            value={searchText}
            onChangeText={(text) => {
              setSearchText(text);
              setShowDropdown(true);
            }}
            placeholder={AppText.ENTER_CUSTOMER_NAME}
            placeholderTextColor={colors.grey}
          />
          {/* <Icon
            source={showDropdown ? globalpath.up : globalpath.down}
            size={wp(4)}
            tintColor={getTextColor()}
            margin={[0,wp(3),0,0]}
          /> */}
        </View>
        <View style={styles.iconContainer}>
          {matchFound && (
            <TouchableOpacity
              onPress={() => {
                const matchedCustomer = filteredCustomers[0];
                onChange(matchedCustomer.id);
                setSearchText(matchedCustomer.full_name);
                onShowPreviousBook && onShowPreviousBook();
              }}
              style={styles.iconButton}
            >
              <Icon source={globalpath.info} size={wp(5.6)} tintColor={"green"} />
            </TouchableOpacity>
          )}
          {searchText !== '' && (
            <TouchableOpacity onPress={handleClear} style={styles.iconButton}>
              <Icon source={globalpath.cross} size={wp(4.6)} tintColor={getTextColor()} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {showDropdown && (
        <View style={[styles.dropdownContainer, { backgroundColor }]}>
          {renderDropdownContent()}
        </View>
      )}
    </View>
  );
};

export default CustomerNameDropdown;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    height: hp(6),
  },
  input: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textInput: {
    flex: 1,
    fontSize: wp(3.8),
    padding: 0,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: wp(1),
    marginLeft: wp(2),
  },
  dropdownContainer: {
    marginTop: hp(0.5),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.grey,
    maxHeight: hp(25), // Increased height
  },
  scrollView: {
    maxHeight: hp(24), // Slightly less than container to account for border
  },
  dropdownItem: {
    padding: wp(4),
    borderBottomWidth: 1,
  },
  recordInfoContainer: {
    padding: wp(4),
  },
  recordsList: {
    marginTop: hp(1),
  },
  recordItem: {
    paddingVertical: hp(0.5),
  },
  noRecordContainer: {
    padding: wp(4),
    alignItems: 'center',
  },
  add_icon: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addButton: {
    position: 'absolute',
    // bottom: hp(2),
    right: wp(1),
    width: wp(7),
    height: wp(7),
    borderRadius: wp(6),
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
}); 