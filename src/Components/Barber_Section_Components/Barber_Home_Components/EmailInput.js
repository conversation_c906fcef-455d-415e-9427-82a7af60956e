import React from 'react';
import CustomTextInput from '../../CustomTextInput';
import useAppText from '../../../Custom/AppText';

const EmailInput = ({ value, onChangeText }) => {
  const AppText = useAppText();
  
  return (
    <CustomTextInput
      label={AppText.EMAIL}
      placeholder={AppText.ENTER_EMAIL}
      value={value}
      onChangeText={onChangeText}
      keyboardType="email-address"
          autoCapitalize="none"
  
    />
  );
};

export default EmailInput; 