import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, Alert, ActivityIndicator } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import CustomerNameInput from './CustomerNameInput';
import LastNameInput from './LastNameInput';
import EmailInput from './EmailInput';
import PhoneInput from 'react-native-phone-number-input';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { create_cutomer_signUP } from '../../../Services/API/Endpoints/Auth/Auth';
import Modal from 'react-native-modal';
import DatePicker from 'react-native-modern-datepicker';

const AddNewContent = ({ onBack }) => {
  const AppText = useAppText();
  const { getTextColor, getDark_Theme, backgroundColor, themeColor } = useTheme();
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [formData, setFormData] = useState({
    customerName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
  });
  const [countryCode, setCountryCode] = useState('PK');
  const [phoneError, setPhoneError] = useState('');

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePhoneChange = (number) => {
    handleInputChange('phone', number);

    try {
      const phoneNumberObj = parsePhoneNumberFromString(number, countryCode);

      if (phoneNumberObj) {
        if (!phoneNumberObj.isValid()) {
          setPhoneError(AppText.INVALID_PHONE_NUMBER);
        } else {
          setPhoneError('');
        }
      }
    } catch (e) {
      setPhoneError('Invalid phone number format');
    }
  };

  const handleCountryChange = (country) => {
    setCountryCode(country.cca2);
    setPhoneError('');
  };

  const handleDateChange = (selectedDate) => {
    // Convert date from YYYY-MM-DD to YYYY/MM/DD format
    const formattedDate = selectedDate.replace(/-/g, '/');
    handleInputChange('dateOfBirth', formattedDate);
    setShowDatePicker(false);
  };

  const validateForm = () => {
    if (!formData.customerName.trim()) {
      Alert.alert('Error', 'Please enter customer name');
      return false;
    }
    if (!formData.lastName.trim()) {
      Alert.alert('Error', 'Please enter last name');
      return false;
    }
    if (!formData.email.trim()) {
      Alert.alert('Error', 'Please enter email');
      return false;
    }
    if (!formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return false;
    }
    if (!formData.phone.trim()) {
      Alert.alert('Error', 'Please enter phone number');
      return false;
    }
    if (phoneError) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return false;
    }
    if (!formData.dateOfBirth) {
      Alert.alert('Error', 'Please select date of birth');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const payload = {
        email: formData.email,
        password: "SecurePassword123", // Default password
        role: "customer",
        full_name: `${formData.customerName} ${formData.lastName}`,
        phone_number: formData.phone,
        address_city: "",
        address_postal_code: "",
        address_country: "",
        address_state: "",
        address_street: "",
        dob: formData.dateOfBirth
      };

      console.log('Sending payload:', payload);
      const response = await create_cutomer_signUP(payload);
      console.log('API Response:', response);

      Alert.alert(
        'Success',
        'Customer created successfully!',
        [{ text: 'OK', onPress: () => {
          // Pass back the created customer data
          onBack({
            id: response.id,
            full_name: payload.full_name,
            email: payload.email,
            phone_number: payload.phone_number
          });
        }}]
      );
    } catch (error) {
      console.error('Error creating customer:', error);
      Alert.alert(
        'Error',
        error?.response?.data?.dob?.[0] || error?.response?.data?.message || 'Failed to create customer. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
      keyboardVerticalOffset={Platform.OS === 'ios' ? hp(28) : 0}
    >
      {/* Header with Back Button and Client Info */}
      <View style={[styles.header, { borderBottomColor: getDark_Theme() }]}>
        <TouchableOpacity onPress={() => onBack(null)}>
          <Icon
            source={globalpath.goback}
            size={wp(4.5)}
            tintColor={getTextColor()}
          />
        </TouchableOpacity>
        <View style={styles.titleContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={4.5}
            weight="600"
            style={styles.clientInfoText}
          >
            {AppText.CLIENT_INFO}
          </ResponsiveText>
        </View>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <CustomerNameInput
          value={formData.customerName}
          onChangeText={(value) => handleInputChange('customerName', value)}
        />
        <LastNameInput
          value={formData.lastName}
          onChangeText={(value) => handleInputChange('lastName', value)}
        />
        <EmailInput
          value={formData.email}
          onChangeText={(value) => handleInputChange('email', value)}
        />
          <ResponsiveText color={getTextColor()} size={4} weight="600" margin={[hp(0),0,hp(1.5),0]}>{AppText.PHONE_NUMBER}</ResponsiveText>
        <View style={[styles.phoneinputContainer, { borderColor: getDark_Theme() }]}>
          <PhoneInput
            defaultCode="PK"
            layout="first"
            onChangeText={handlePhoneChange}
            onChangeCountry={handleCountryChange}
            value={formData.phone}
            placeholder={AppText.PHONE_PLACEHOLDER}
            placeholderTextColor={getTextColor()}
            containerStyle={styles.phoneInput}
            textContainerStyle={styles.phoneTextContainer}
            textInputStyle={[styles.phoneTextInput, {color: getTextColor()}]}
            codeTextStyle={{color: getTextColor()}}
          />
        </View>
        {phoneError ? <ResponsiveText color={colors.red} margin={[hp(-1.8),0,hp(1.3),0]}>{phoneError}</ResponsiveText> : null}
        <ResponsiveText color={getTextColor()} size={4} weight="600" margin={[hp(0),0,hp(1.5),0]}>{AppText.DATE_OF_BIRTH}</ResponsiveText>
        <TouchableOpacity
          style={[styles.inputContainer, { borderColor: getDark_Theme() }]}
          onPress={() => setShowDatePicker(true)}
        >
          <ResponsiveText
            color={formData.dateOfBirth ? getTextColor() : colors.grey}
            size={4}
          >
            {formData.dateOfBirth || AppText.DOB_PLACEHOLDER}
          </ResponsiveText>
          <Icon
            source={globalpath.calendar}
            size={wp(5)}
          />
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleSave} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <ResponsiveText
              color={colors.white}
              size={4.5}
              weight="600"
            >
              {AppText.SAVE}
            </ResponsiveText>
          )}
        </TouchableOpacity>
      </ScrollView>

      <Modal
        isVisible={showDatePicker}
        onBackdropPress={() => setShowDatePicker(false)}
        onBackButtonPress={() => setShowDatePicker(false)}
        style={styles.modalStyle}
      >
        <View style={[styles.datePickerContainer, { backgroundColor: backgroundColor }]}>
          <DatePicker
            mode="calendar"
            onDateChange={handleDateChange}
            options={{
              textHeaderColor: themeColor,
              mainColor: colors.Light_theme_maincolour,
              textDefaultColor: getTextColor(),
              selectedTextColor: colors.white,
              backgroundColor: backgroundColor,
            }}
          />
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

export default AddNewContent;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
    marginTop: hp(1.5),
    borderBottomWidth: 1,
    paddingBottom: hp(1),
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  clientInfoText: {
    textAlign: 'center',
  },
  placeholder: {
    width: wp(6),
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: wp(1),
    paddingBottom: hp(4),
  },
  button: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
  },
  phoneinputContainer: {
    flexDirection: 'row',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    borderWidth: 1,
    height: Platform.OS === 'android' ? hp(7) : hp(6),
    overflow: 'hidden',
  },
  phoneInput: {
    flex: 1,
    height: '100%',
    backgroundColor: 'transparent',
  },
  phoneTextContainer: {
    backgroundColor: 'transparent',
    paddingVertical: 0,
  },
  phoneTextInput: {
    height: '100%',
    fontSize: wp(4),
    padding: 0,
  },
  inputContainer: {
    height: hp(6),
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    paddingHorizontal: wp(4),
    borderWidth: 1,
    justifyContent: 'space-between',
  },
  modalStyle: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  datePickerContainer: {
    padding: wp(4),
    borderTopLeftRadius: wp(4),
    borderTopRightRadius: wp(4),
  },
});