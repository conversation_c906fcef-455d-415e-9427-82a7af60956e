import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StatusBar, StyleSheet, Modal } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import MonthYearPickerModal from './MonthYearPickerModal';

const Weekly_Calendar_Header = ({
  onPress,
  showBellIcon = true,
  leftIconType = 'bars', // 'bars' or 'back'
  onMonthSelect, // Callback for when a month is selected
  selectedMonth: propSelectedMonth, // Month passed from parent
}) => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { getTextColor, getBellBackground, getDark_Theme, backgroundColor, isDarkTheme } = useTheme();

  // State for date picker
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(propSelectedMonth || new Date());

  // Update local state when prop changes
  useEffect(() => {
    if (propSelectedMonth) {
      setSelectedMonth(propSelectedMonth);
    }
  }, [propSelectedMonth]);

  const handleLeftIconPress = () => {
    if (leftIconType === 'back') {
      navigation.goBack();
    } else {
      navigation.openDrawer();
    }
  };

  const getLeftIconSource = () => {
    return leftIconType === 'back' ? globalpath.goback : globalpath.bars;
  };



  // Handle date selection from calendar
  const handleDateSelect = (date) => {
    const selectedDate = new Date(date);
    // Set day to 1 to focus on month and year only
    selectedDate.setDate(1);
    setSelectedMonth(selectedDate);
    if (onMonthSelect) {
      onMonthSelect(selectedDate);
    }
    setShowDatePicker(false);
  };

  // Clear selected month and reset to current month
  const clearSelectedMonth = () => {
    const currentDate = new Date();
    currentDate.setDate(1); // Set to first day of current month
    setSelectedMonth(currentDate);
    if (onMonthSelect) {
      onMonthSelect(currentDate);
    }
    setShowDatePicker(false); // Close the date picker if it's open
  };

  // Open date picker
  const handleOpenDatePicker = () => {
    setShowDatePicker(true);
  };

  // Format month for display
  const formatMonth = (date) => {
    if (!date) {
      return getCurrentMonth();
    }
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Get current month in the required format
  const getCurrentMonth = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Check if the selected month is the current month
  const isCurrentMonth = () => {
    if (!selectedMonth) return true;

    const today = new Date();
    return (
      selectedMonth.getMonth() === today.getMonth() &&
      selectedMonth.getFullYear() === today.getFullYear()
    );
  };





  return (
    <View>
      <StatusBar backgroundColor={backgroundColor} barStyle={isDarkTheme() ? "light-content" : "dark-content"} />

      <View style={[styles.headerContainer]}>
        <View style={styles.leftIconContainer}>
          <TouchableOpacity onPress={handleLeftIconPress}>
            <Icon
              source={getLeftIconSource()}
              size={wp(5.5)}
              margin={[0, 0, 0, wp(5)]}
              tintColor={getTextColor()}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.titleContainer}>
          <TouchableOpacity
            style={styles.calendarButton}
            onPress={handleOpenDatePicker}
          >
            <Icon
              source={globalpath.calendar}
              size={wp(5)}
              tintColor={getTextColor()}
            />
            <ResponsiveText
              size={4.5}
              weight={'600'}
              color={getTextColor()}
              style={styles.calendarText}
            >
              {formatMonth(selectedMonth)}
            </ResponsiveText>

            {/* Only show cross button when not in current month */}
            {!isCurrentMonth() && (
              <TouchableOpacity
                onPress={clearSelectedMonth}
                style={styles.crossButton}
              >
                <Icon
                  source={globalpath.cross}
                  size={wp(4)}
                  tintColor={getTextColor()}
                />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.rightIconContainer}>
          {showBellIcon ? (
            <TouchableOpacity
              style={[styles.iconContainer, { backgroundColor: getBellBackground() }]}
              onPress={onPress}
            >
              <Icon source={globalpath.bell} size={wp(6.5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          ) : (
            <View style={styles.placeholder} />
          )}
        </View>
      </View>

      <MonthYearPickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onSave={handleDateSelect}
        initialDate={selectedMonth || new Date()}
      />

      <View style={{borderBottomWidth:1, borderColor: getDark_Theme(), marginVertical: wp(2.5)}}/>
    </View>
  );
};

export default Weekly_Calendar_Header;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp(1),
    justifyContent: 'space-between',
  },
  leftIconContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: wp(1),
    paddingHorizontal: wp(2),
    borderRadius: wp(2),
  },
  calendarText: {
    marginLeft: wp(2),
  },
  crossButton: {
    marginLeft: wp(2),
    padding: wp(1),
  },
  rightIconContainer: {
    flex: 1,
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    borderRadius: wp(10),
    padding: wp(1.8),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(3),
  },
  placeholder: {
    width: wp(10),
  }
});
