import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StatusBar, StyleSheet, Modal } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import DatePickerModal from './Barber_Home_Components/DatePickerModal';

const Calendar_Header = ({
  onPress,
  showBellIcon = true,
  leftIconType = 'bars', // 'bars' or 'back'
  onDateSelect, // Callback for when a date is selected
  selectedDate: propSelectedDate, // Date passed from parent
}) => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { getTextColor, getBellBackground, getDark_Theme, backgroundColor, isDarkTheme } = useTheme();

  // State for date picker
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(propSelectedDate || null);

  // Update local state when prop changes
  useEffect(() => {
    if (propSelectedDate) {
      setSelectedDate(propSelectedDate);
    }
  }, [propSelectedDate]);

  const handleLeftIconPress = () => {
    if (leftIconType === 'back') {
      navigation.goBack();
    } else {
      navigation.openDrawer();
    }
  };

  const getLeftIconSource = () => {
    return leftIconType === 'back' ? globalpath.goback : globalpath.bars;
  };

  // Open date picker
  const openDatePicker = () => {
    setShowDatePicker(true);
  };

  // Handle date selection from calendar
  const handleDateSelect = (date) => {
    const selectedDateObj = new Date(date);
    setSelectedDate(selectedDateObj);
    if (onDateSelect) {
      onDateSelect(selectedDateObj);
    }
  };

  // Clear selected date
  const clearSelectedDate = () => {
    setSelectedDate(null);
    if (onDateSelect) {
      onDateSelect(null);
    }
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) {
      return getCurrentDate();
    }
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Get current date in the required format
  const getCurrentDate = () => {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Format date to YYYY-MM-DD for the calendar component
  const formatToYYYYMMDD = (dateInput) => {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return null;

    // Get the date in local timezone
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  return (
    <View>
      <StatusBar backgroundColor={backgroundColor} barStyle={isDarkTheme() ? "light-content" : "dark-content"} />

      <View style={[styles.headerContainer]}>
        <View style={styles.leftIconContainer}>
          <TouchableOpacity onPress={handleLeftIconPress}>
            <Icon
              source={getLeftIconSource()}
              size={wp(5.5)}
              margin={[0, 0, 0, wp(5)]}
              tintColor={getTextColor()}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.titleContainer}>
          <TouchableOpacity
            style={styles.calendarButton}
            onPress={openDatePicker}
          >
            <Icon
              source={globalpath.calendar}
              size={wp(5)}
              tintColor={getTextColor()}
            />
            <ResponsiveText
              size={4.5}
              weight={'600'}
              color={getTextColor()}
              style={styles.calendarText}
            >
              {selectedDate ? formatDate(selectedDate) : getCurrentDate()}
            </ResponsiveText>

            {selectedDate && (
              <TouchableOpacity
                onPress={clearSelectedDate}
                style={styles.crossButton}
              >
                <Icon
                  source={globalpath.cross}
                  size={wp(4)}
                  tintColor={getTextColor()}
                />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.rightIconContainer}>
          {showBellIcon ? (
            <TouchableOpacity
              style={[styles.iconContainer, { backgroundColor: getBellBackground() }]}
              onPress={onPress}
            >
              <Icon source={globalpath.bell} size={wp(6.5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          ) : (
            <View style={styles.placeholder} />
          )}
        </View>
      </View>

      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onSave={handleDateSelect}
        initialDate={selectedDate ? formatToYYYYMMDD(selectedDate) : formatToYYYYMMDD(new Date())}
      />

      <View style={{borderBottomWidth:1, borderColor: getDark_Theme(), marginVertical: wp(2.5)}}/>
    </View>
  );
};

export default Calendar_Header;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp(1),
    justifyContent: 'space-between',
  },
  leftIconContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: wp(1),
    paddingHorizontal: wp(2),
    borderRadius: wp(2),
  },
  calendarText: {
    marginLeft: wp(2),
  },
  crossButton: {
    marginLeft: wp(2),
    padding: wp(1),
  },
  rightIconContainer: {
    flex: 1,
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    borderRadius: wp(10),
    padding: wp(1.8),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(3),
  },
  placeholder: {
    width: wp(10),
  }
});