import React, { useEffect, useState , useCallback } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import { hp, wp } from '../../../Custom/Responsiveness';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useFocusEffect } from '@react-navigation/native';



const BarberInfoProfile = () => {
  const { getTextColor,  } = useTheme();


  const [barber, setBarber] = useState(null);
  // console.log(customer.full_name)
  
   useFocusEffect(
        useCallback(() => {
          fetchBarberDetails();
        }, []),
      );

    const fetchBarberDetails = async () => {
        try {
          const data = await AsyncStorage.getItem('barberData');
          if (data) {
            const parsed = JSON.parse(data);
            // If it's an array, get the first customer
            const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
            setBarber(barberInfo);
            console.log('✅ BarberData object retrieved in the BarberInfoProfile', barberInfo);
          } else {
            console.log('ℹ️ No barber data found in AsyncStorage');
          }
        } catch (error) {
          console.log('❌ Error fetching barber details:', error);
        }
      };

  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <Image 
          source={globalpath.profile} 
          style={styles.profileImage}
          resizeMode="contain"
        />
      </View>
      <View style={styles.rightSection}>
        <ResponsiveText 
          color={getTextColor()} 
          size={5}
          weight={'bold'}
        >
          {barber?.full_name}
        </ResponsiveText>
        <ResponsiveText 
          color={colors.grey} 
          size={4}
          margin={[hp(1), 0, 0, 0]}
        >
          {barber?.email}
        </ResponsiveText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(3),
  },
  leftSection: {
    marginRight: wp(3),
  },
  profileImage: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
  },
  rightSection: {
    flex: 1,
  },
});

export default BarberInfoProfile; 