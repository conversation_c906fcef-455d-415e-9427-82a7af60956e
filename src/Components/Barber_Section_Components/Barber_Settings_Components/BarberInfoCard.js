import React, { useEffect, useState , useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useFocusEffect } from '@react-navigation/native';



const InfoItem = ({ icon, label, value }) => {
  const { getTextColor, getSecondaryTextColor } = useTheme();

  return (
    <View style={styles.infoItem}>
      <View style={styles.iconContainer}>
        <Icon source={icon} size={wp(6)} style={{tintColor: getTextColor()}} />
      </View>
      <View style={styles.textContainer}>
        <ResponsiveText 
          color={getTextColor()} 
          size={4.5}
        >
          {label}
        </ResponsiveText>
        <ResponsiveText 
          color={"#6B6E82"} 
          size={4}
          margin={[hp(0.5), 0, 0, 0]}
        >
          {value}
        </ResponsiveText>
      </View>
    </View>
  );
};

const BarberInfoCard = () => {
  const AppText = useAppText();
  const { getsky_Theme } = useTheme();

  const [barber, setBarber] = useState(null);
  // console.log(customer.full_name)


  useFocusEffect(
          useCallback(() => {
            fetchBarberDetails();
          }, []),
        );

    const fetchBarberDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('barberData');
        if (data) {
          const parsed = JSON.parse(data);
          // If it's an array, get the first customer
          const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setBarber(barberInfo);
          console.log('✅ BarberData object retrieved in the BarberInfoProfile', barberInfo);
        } else {
          console.log('ℹ️ No barber data found in AsyncStorage');
        }
      } catch (error) {
        console.log('❌ Error fetching barber details:', error);
      }
    };
  


  return (
    <View style={[styles.container, { backgroundColor: getsky_Theme() }]}>
      <InfoItem
        icon={globalpath.user}
        label={AppText.FULL_NAME}
        value={barber?.full_name}
      />
      <InfoItem
        icon={globalpath.email}
        label={AppText.EMAIL}
        value={barber?.email}
      />
      <InfoItem
        icon={globalpath.phone}
        label={AppText.PHONE_NUMBER}
        value={barber?.phone_number}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: wp(4),
    borderRadius: wp(4),
    padding: wp(4),
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: hp(3),
  },
  iconContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    marginRight: wp(3),
  },
  textContainer: {
    flex: 1,
  },
});

export default BarberInfoCard; 