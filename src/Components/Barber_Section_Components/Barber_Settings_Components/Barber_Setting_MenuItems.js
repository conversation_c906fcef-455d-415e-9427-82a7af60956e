import React from 'react';
import { View, StyleSheet, TouchableOpacity, Switch } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedTheme, setThemeColor, setBackgroundColor, setLanguage } from '../../../Redux/Slices/themeSlice';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';

const Barber_Setting_MenuItems = ({ icon, title, onPress, isThemeSwitch, isLanguageSwitch }) => {
  const AppText = useAppText();
  const { getTextColor, getSecondaryTextColor, getDark_Theme } = useTheme();
  const dispatch = useDispatch();
  const selectedTheme = useSelector((state) => state.theme.selectedTheme);
  const currentLanguage = useSelector((state) => state.theme.language);

  const handleThemeSwitchChange = () => {
    const newTheme = selectedTheme === 'light' ? 'dark' : 'light';
    dispatch(setSelectedTheme(newTheme));
    dispatch(setThemeColor(newTheme === 'dark' ? colors.Light_theme_maincolour : colors.black));
    dispatch(setBackgroundColor(newTheme === 'dark' ? colors.black : colors.white));
  };

  const handleLanguageSwitchChange = () => {
    const newLanguage = currentLanguage === 'en' ? 'it' : 'en';
    dispatch(setLanguage(newLanguage));
  };

  const handleSwitchChange = () => {
    if (isThemeSwitch) {
      handleThemeSwitchChange();
    } else if (isLanguageSwitch) {
      handleLanguageSwitchChange();
    }
  };

  const isToggled = isThemeSwitch 
    ? selectedTheme === 'dark'
    : currentLanguage === 'it';

  const showSwitch = isThemeSwitch || isLanguageSwitch;

  const getDisplayTitle = () => {
    if (isThemeSwitch) {
      return isToggled ? AppText.DARK_MODE : AppText.LIGHT_MODE;
    } else if (isLanguageSwitch) {
      return isToggled ? AppText.ITALIAN : AppText.ENGLISH;
    }
    return title;
  };

  return (
    <TouchableOpacity 
      style={[styles.container, { borderBottomColor: getDark_Theme() }]} 
      onPress={onPress}
      disabled={showSwitch}
    >
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Icon source={icon} size={wp(6)} tintColor={getTextColor()} />
        </View>
        <View style={styles.middleSection}>
          <ResponsiveText color={getTextColor()} size={4} weight={'600'}>
            {getDisplayTitle()}
          </ResponsiveText>
        </View>
        <View style={styles.rightSection}>
          {showSwitch ? (
            <Switch
              trackColor={{ false: '#767577', true: colors.Light_theme_maincolour }}
              thumbColor={isToggled ? '#fff' : '#f4f3f4'}
              ios_backgroundColor="#3e3e3e"
              onValueChange={handleSwitchChange}
              value={isToggled}
            />
          ) : (
            <Icon source={globalpath.left} size={wp(5)} tintColor={getTextColor()} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    paddingVertical: hp(2),
    marginHorizontal: wp(1.5),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: wp(6),
  },
  leftSection: {
    marginRight: wp(3),
  },
  middleSection: {
    flex: 1,
  },
  rightSection: {
    marginLeft: wp(2),
  },
});

export default Barber_Setting_MenuItems; 