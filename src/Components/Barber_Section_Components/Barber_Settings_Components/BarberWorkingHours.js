import React, { useEffect, useState , useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useFocusEffect } from '@react-navigation/native';


// Format time from 24-hour format to AM/PM format
const formatTimeToAMPM = (timeString) => {
  if (!timeString || timeString === '00:00:00') return 'Closed';

  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours, 10);
  const minute = parseInt(minutes, 10);

  const period = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;

  return `${formattedHour}:${minute.toString().padStart(2, '0')} ${period}`;
};

const WeekDayCard = ({ day, dayName, hasWorkingHours, startTime, endTime }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  // Get abbreviated day name
  const getShortDayName = (dayName) => {
    if (!dayName) return '';
    return dayName.substring(0, 3);
  };

  // Get day of month (just for display purposes)
  const getDayOfMonth = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diff = day - dayOfWeek;
    const date = new Date(today);
    date.setDate(today.getDate() + diff);
    return date.getDate();
  };

  return (
    <View style={styles.cardContainer}>
      <View
        style={[
          styles.card,
          {
            borderColor: getDark_Theme(),
            backgroundColor: hasWorkingHours ? colors.Light_theme_maincolour : 'transparent',
          },
        ]}
      >
        {hasWorkingHours && (
          <Icon
            source={globalpath.tick}
            size={wp(4)}
            style={styles.tickIcon}
          />
        )}
        <ResponsiveText
          color={hasWorkingHours ? colors.white : getTextColor()}
          size={4.5}
          weight="400"
        >
          {getShortDayName(dayName)}
        </ResponsiveText>
        <ResponsiveText
          color={hasWorkingHours ? colors.white : getTextColor()}
          size={3.5}
          weight="500"
          margin={[hp(0.5), 0, 0, 0]}
        >
          {getDayOfMonth()}
        </ResponsiveText>
      </View>

      {hasWorkingHours && (
        <View style={styles.timeContainer}>
          <ResponsiveText
            // color={"#6B6E82"}
            color={colors.Light_theme_maincolour}

            size={2.8}
            textAlign="center"

          >
            {formatTimeToAMPM(startTime)} - {formatTimeToAMPM(endTime)}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

const BarberWorkingHours = () => {
  const AppText = useAppText();
  const { getsky_Theme } = useTheme();
  const [barber, setBarber] = useState(null);


   useFocusEffect(
          useCallback(() => {
            fetchBarberDetails();
          }, []),
        );
  




 
    const fetchBarberDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('barberData');
        if (data) {
          const parsed = JSON.parse(data);
          const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setBarber(barberInfo);
          console.log('✅ BarberData object retrieved in BarberWorkingHours', barberInfo);
        } else {
          console.log('ℹ️ No barber data found in AsyncStorage');
        }
      } catch (error) {
        console.log('❌ Error fetching barber details:', error);
      }
    };


  // Sort working hours by day_of_week
  const sortedWorkingHours = barber?.working_hours
    ? [...barber.working_hours].sort((a, b) => a.day_of_week - b.day_of_week)
    : [];

  // Get day name based on day_of_week index
  const getDayName = (dayIndex) => {
    const days = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
    return AppText[days[dayIndex]] || days[dayIndex];
  };

  return (
    <View style={[styles.container, { backgroundColor: getsky_Theme() }]}>
      <ResponsiveText
        color={colors.Light_theme_maincolour}
        size={5}
        weight={'bold'}
        margin={[0, 0, hp(2), 0]}
      >
        {AppText.WORKING_HOURS}
      </ResponsiveText>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.weekDaysContainer}
      >
        {Array.from({ length: 7 }, (_, i) => {
          const workingHour = sortedWorkingHours.find(wh => wh.day_of_week === i);
          const hasWorkingHours = workingHour &&
            workingHour.start_time &&
            workingHour.end_time &&
            (workingHour.start_time !== '00:00:00' || workingHour.end_time !== '00:00:00');

          return (
            <WeekDayCard
              key={i}
              day={i}
              dayName={getDayName(i)}
              hasWorkingHours={hasWorkingHours}
              startTime={workingHour?.start_time}
              endTime={workingHour?.end_time}
            />
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: wp(4),
    borderRadius: wp(4),
    padding: wp(4),
    marginTop: hp(2),
    marginBottom: hp(2),
  },
  weekDaysContainer: {
    flexDirection: 'row',
    paddingVertical: hp(1),
  },
  cardContainer: {
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  card: {
    height: hp(11),
    width: wp(25),
    borderWidth: 1,
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  tickIcon: {
    position: 'absolute',
    top: hp(0.5),
    right: wp(1),
    tintColor: colors.white,
  },
  timeContainer: {
    marginTop: hp(1),
    width: wp(25),
  },
});

export default BarberWorkingHours;
