import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Add_Barber_Time_Picker_Modal from '../../Barber/Add_Barber_Time_Picker_Modal';
import { update_barber_working_hours } from '../../../Services/API/Endpoints/Admin/Barber';

// Format time from 24-hour format to AM/PM format
const formatTimeToAMPM = (timeString) => {
  if (!timeString || timeString === '00:00:00') return 'Closed';

  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours, 10);
  const minute = parseInt(minutes, 10);

  const period = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;

  return `${formattedHour}:${minute.toString().padStart(2, '0')} ${period}`;
};

const WeekDayCard = ({ day, dayName, hasWorkingHours, startTime, endTime, onPress }) => {
  const { getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();

  // Get abbreviated day name
  const getShortDayName = (dayName) => {
    if (!dayName) return '';
    return dayName.substring(0, 3);
  };

  // Get day of month (just for display purposes)
  const getDayOfMonth = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diff = day - dayOfWeek;
    const date = new Date(today);
    date.setDate(today.getDate() + diff);
    return date.getDate();
  };

  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity
        style={[
          styles.card,
          {
            borderColor: getDark_Theme(),
            backgroundColor: hasWorkingHours ? colors.Light_theme_maincolour : 'transparent',
          },
        ]}
        onPress={onPress}
      >
        {hasWorkingHours && (
          <Icon
            source={globalpath.tick}
            size={wp(4)}
            style={styles.tickIcon}
          />
        )}
        <ResponsiveText
          color={hasWorkingHours ? colors.white : getTextColor()}
          size={4.5}
          weight="500"
        >
          {getShortDayName(dayName)}
        </ResponsiveText>
        <ResponsiveText
          color={hasWorkingHours ? colors.white : getTextColor()}
          size={3.5}
          weight="500"
          margin={[hp(0.5), 0, 0, 0]}
        >
          {getDayOfMonth()}
        </ResponsiveText>

        {/* <View style={styles.editIconContainer}>
          <Icon source={globalpath.edit} size={wp(3.5)} style={{tintColor: hasWorkingHours ? colors.white : colors.Light_theme_maincolour}} />
        </View> */}
      </TouchableOpacity>

      {hasWorkingHours && (
        <View style={styles.timeContainer}>
          <ResponsiveText
            // color={"#6B6E82"}
            color={colors.Light_theme_maincolour}

            size={2.8}
            textAlign="center"
          >
            {formatTimeToAMPM(startTime)} - {formatTimeToAMPM(endTime)}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

const EditableBarberWorkingHours = ({ workingHours, onWorkingHoursChange }) => {
  const AppText = useAppText();
  const { getsky_Theme } = useTheme();
  const [localWorkingHours, setLocalWorkingHours] = useState([]);
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [selectedDayIndex, setSelectedDayIndex] = useState(null);

  useEffect(() => {
    if (workingHours && workingHours.length > 0) {
      // Sort working hours by day_of_week
      const sortedHours = [...workingHours].sort((a, b) => a.day_of_week - b.day_of_week);
      setLocalWorkingHours(sortedHours);
    } else {
      // Initialize with default working hours if none provided
      const defaultWorkingHours = Array.from({ length: 7 }, (_, i) => ({
        day_of_week: i,
        start_time: '00:00:00',
        end_time: '00:00:00'
      }));
      setLocalWorkingHours(defaultWorkingHours);
    }
  }, [workingHours]);

  const handleDayPress = (index) => {
    setSelectedDayIndex(index);
    setShowTimeModal(true);
  };

  // Convert Date object to 24-hour format string (HH:MM:SS)
  const formatTo24Hour = (date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}:00`;
  };

  const handleTimeSelect = async (startTime, endTime) => {
    if (selectedDayIndex === null) return;
  
    const formattedStartTime = formatTo24Hour(startTime);
    const formattedEndTime = formatTo24Hour(endTime);
  
    try {
      const updatedHours = [...localWorkingHours];
  
      // Get the object to update
      const updatedHour = {
        ...updatedHours[selectedDayIndex],
        start_time: formattedStartTime,
        end_time: formattedEndTime
      };
  
      console.log("🕒 THE UPDATED OBJECT IS --->", updatedHour);
  
      // API Call
      const response = await update_barber_working_hours(updatedHour.id, updatedHour);
      console.log('✅ Working hours updated successfully:', response);
  
      // Replace in the array
      updatedHours[selectedDayIndex] = updatedHour;
  
      setLocalWorkingHours(updatedHours);
      onWorkingHoursChange(updatedHours);
      setShowTimeModal(false);
    } catch (error) {
      console.error('❌ Failed to update working hours:', error?.response?.data || error);
      let errorMessage = 'Failed to update working hours.';
      if (error?.response?.data) {
        const data = error.response.data;
        errorMessage = Object.keys(data)
          .map(key => `${key}: ${Array.isArray(data[key]) ? data[key].join(', ') : data[key]}`)
          .join('\n');
      }
      Alert.alert('Error', errorMessage);
    }
  };
  
  
  // Convert time string to Date object for the time picker
  const timeStringToDate = (timeString) => {
    if (!timeString || timeString === '00:00:00') {
      return new Date();
    }

    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours, 10));
    date.setMinutes(parseInt(minutes, 10));
    date.setSeconds(0);
    return date;
  };

  // Get day name based on day_of_week index
  const getDayName = (dayIndex) => {
    const days = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
    return AppText[days[dayIndex]] || days[dayIndex];
  };

  return (
    <View style={[styles.container, { backgroundColor: getsky_Theme() }]}>
      <ResponsiveText
        color={colors.Light_theme_maincolour}
        size={4}
        weight={'600'}
        margin={[0, 0, hp(2), 0]}
      >
        {AppText.WORKING_HOURS}
      </ResponsiveText>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.weekDaysContainer}
      >
        {Array.from({ length: 7 }, (_, i) => {
          const workingHour = localWorkingHours.find(wh => wh.day_of_week === i);
          const hasWorkingHours = workingHour &&
            workingHour.start_time &&
            workingHour.end_time &&
            (workingHour.start_time !== '00:00:00' || workingHour.end_time !== '00:00:00');

          return (
            <WeekDayCard
              key={i}
              day={i}
              dayName={getDayName(i)}
              hasWorkingHours={hasWorkingHours}
              startTime={workingHour?.start_time}
              endTime={workingHour?.end_time}
              onPress={() => handleDayPress(i)}
            />
          );
        })}
      </ScrollView>

      {showTimeModal && selectedDayIndex !== null && (
        <Add_Barber_Time_Picker_Modal
          visible={showTimeModal}
          onClose={() => setShowTimeModal(false)}
          onSave={handleTimeSelect}
          initialStartTime={timeStringToDate(localWorkingHours.find(wh => wh.day_of_week === selectedDayIndex)?.start_time)}
          initialEndTime={timeStringToDate(localWorkingHours.find(wh => wh.day_of_week === selectedDayIndex)?.end_time)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // marginHorizontal: wp(4),
    borderRadius: wp(2),
    padding: wp(4),
    marginTop: hp(2),
    marginBottom: hp(2),
  },
  weekDaysContainer: {
    flexDirection: 'row',
    paddingVertical: hp(1),
  },
  cardContainer: {
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  card: {
    height: hp(11),
    width: wp(25),
    borderWidth: 1,
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  tickIcon: {
    position: 'absolute',
    top: hp(0.5),
    right: wp(1),
    tintColor: colors.white,
  },
  timeContainer: {
    marginTop: hp(1),
    width: wp(25),
  },
  editIconContainer: {
    position: 'absolute',
    bottom: hp(0.5),
    right: wp(1),
  },
});

export default EditableBarberWorkingHours;
