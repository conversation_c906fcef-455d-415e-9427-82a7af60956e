import React, { useEffect, useState , useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity ,Image} from 'react-native';
// import ResponsiveText from '../../Custom/RnText';
// import Icon from '../../Custom/Icon';
// import { globalpath } from '../../Custom/globalpath';
// import { wp, hp } from '../../Custom/Responsiveness';
// import useTheme from '../../Redux/useTheme';
// import { colors } from '../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { hp, wp } from '../../../Custom/Responsiveness';
import { globalpath } from '../../../Custom/globalpath';
import { colors } from '../../../Custom/Colors';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import useTheme from '../../../Redux/useTheme';
// import ResponsiveText from '../../../Custom/RnText';
// import { globalpath } from '../../../Custom/globalpath';
// import { colors } from '../../../Custom/Colors';
// import { hp, wp } from '../../../Custom/Responsiveness';
// import Icon from '../../../Custom/Icon';
import { useNavigation, useFocusEffect } from '@react-navigation/native';


const Barber_ProfileSection = () => {
  const navigation = useNavigation();
  const { getTextColor, getSecondaryTextColor, getDark_Theme, } = useTheme();
   const handlePress = () => {
    navigation.navigate('Barber_Profile_Info');
    console.log(' Barber profile section pressed');
   }

   const [barber, setBarber] = useState(null);
   // console.log(customer.full_name)


   useFocusEffect(
    useCallback(() => {
     fetchBarberDetails();
    }, []),
  );

   
  
     const fetchBarberDetails = async () => {
       try {
         const data = await AsyncStorage.getItem('barberData');
         if (data) {
           const parsed = JSON.parse(data);
           // If it's an array, get the first customer
           const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
           setBarber(barberInfo);
           console.log('✅ BarberData object retrieved in the BarberInfoProfile', barberInfo);
         } else {
           console.log('ℹ️ No barber data found in AsyncStorage');
         }
       } catch (error) {
         console.log('❌ Error fetching barber details:', error);
       }
     };






  return (
    <TouchableOpacity style={[styles.container, { borderColor: getDark_Theme() }]} 
    onPress={handlePress}
    >
      <View style={styles.rowContainer}>
        <View style={styles.leftSection}>
          <Image source={globalpath.profile} style={{width:wp(15),height:wp(15)}} resizeMode={'contain'}/>
        </View>
        <View style={styles.middleSection}>
          <View style={styles.nameContainer}>
            <ResponsiveText color={getTextColor()} size={4.5} weight="bold">
              {barber?.full_name}
            </ResponsiveText>
            <View>
              <Icon source={globalpath.profile_eddit} size={wp(4)} margin={[0,0,0,wp(3)]}  />
            </View>
          </View>
          <ResponsiveText color={colors.grey} size={3.5}>
            {barber?.email}
          </ResponsiveText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    // borderWidth: 1,
    // borderRadius: wp(3),
    marginHorizontal: wp(2),
    marginTop: hp(2),
    padding: wp(4),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    marginRight: wp(3),
    
  },
  middleSection: {
    flex: 1,
    // backgroundColor:'pink'
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
});

export default Barber_ProfileSection; 