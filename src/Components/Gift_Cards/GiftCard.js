import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { useNavigation } from '@react-navigation/native';
import { globalpath } from '../../Custom/globalpath';


const GiftCard = ({ item, onPress }) => {
  console.log("THE GIFT CARD IS ITEM IS ", item)
  const AppText = useAppText();
  const { getDark_Theme, getTextColor } = useTheme();
  const navigation = useNavigation();

  return (
    <View 
      style={[styles.card, { borderColor: getDark_Theme() }]} 
      onPress={onPress}
    >
      <Image 
       source={item.giftcard_image
                             ? { uri: item.giftcard_image } // Remote image (from API)
                             : globalpath.logo }
        style={styles.cardImage}
      />
      <View style={styles.contentContainer}>
        <ResponsiveText 
          color={getTextColor()} 
          size={4.5} 
          weight="600"
          style={styles.title}
          numberOfLines={1}
          ellipsizeMode="tail"
          margin={[hp(1),0,0,wp(0)]}
        >
          {item.product_name}
        </ResponsiveText>

        <ResponsiveText 
          color={getTextColor()} 
          size={3.5} 
          weight="400"
          style={styles.title}
          numberOfLines={1}
          ellipsizeMode="tail"
          margin={[hp(1),0,0,wp(0)]}
        >
          {item.product_type_name}
        </ResponsiveText>

        
        
        <View style={styles.detailsRow}>
          
          <ResponsiveText 
            color={getTextColor()} 
            size={3.5}
            style={styles.description}
            numberOfLines={2}
            ellipsizeMode="tail"
            maxWidth={wp(60)}
          margin={[hp(1),0,0,wp(0)]}

          >
            {item.description}
          </ResponsiveText>
          <ResponsiveText 
            color={getTextColor()} 
            size={3.5}
            weight="600"
          >
            ${item.price}
          </ResponsiveText>
        </View>

        <TouchableOpacity 
          style={[styles.viewDetailsButton, { backgroundColor: colors.Light_theme_maincolour }]}
          onPress={onPress}
        >
          <ResponsiveText color={colors.white} size={3.5}>
            {AppText.VIEW_GIFT_CARD_DETAILS}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: wp(2),
    marginHorizontal: wp(4),
    marginVertical: hp(1),
    overflow: 'hidden',
    paddingHorizontal: wp(4),
    paddingVertical: hp(0.5),
  },
  cardImage: {
    width: '100%',
    height: hp(15),
    resizeMode: 'cover',
    borderRadius: wp(2),
    marginTop: hp(2),
  },
  contentContainer: {
    padding: wp(3),
  },
  title: {
    marginBottom: hp(1),
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  description: {
    flex: 1,
    // marginRight: wp(2),
  },
  viewDetailsButton: {
    paddingVertical: hp(1.5),
    paddingHorizontal: wp(4),
    borderRadius: wp(1.5),
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
});

export default GiftCard; 