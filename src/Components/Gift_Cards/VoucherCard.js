import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import useTheme from '../../Redux/useTheme';

const VoucherCard = ({ 
  title, 
  getTextColor, 
  getDark_Theme,
  onEdit,
  onDelete
}) => {
  const { getBellBackground } = useTheme();
  return (
    <View style={styles.wrapper}>
      <View style={[styles.cardContainer, {
        borderColor: getDark_Theme(),
        borderWidth: 1.5
      }]}>
        <View style={[styles.colorStrip, {backgroundColor: colors.Light_theme_maincolour}]} />
        <View style={styles.cardContent}>
          <ResponsiveText color={getTextColor()} size={4.5} weight={'600'} numberOfLines={2} maxWidth={wp(55)}>
            {title}
          </ResponsiveText>
          <View style={styles.actionsContainer}>
            <TouchableOpacity 
              style={[styles.actionButton, {backgroundColor: colors.Light_theme_maincolour}]}
              onPress={onEdit}
            >
              <Icon 
                source={globalpath.edit} 
                tintColor={colors.white}  
                size={wp(3.7)} 
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton,{backgroundColor: getBellBackground()}]}
              onPress={onDelete}
            >
              <Icon 
                source={globalpath.delete_icon} 
                tintColor={colors.red}  
                size={wp(4)} 
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

export default VoucherCard;

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: hp(2),
    marginHorizontal: wp(4),
  },
  cardContainer: {
    borderRadius: wp(3),
    overflow: 'hidden',
    flexDirection: 'row',
    borderTopLeftRadius: wp(3),
    borderBottomLeftRadius: wp(3),
  },
  colorStrip: {
    width: wp(4),
    borderTopLeftRadius: wp(2.5),
    borderBottomLeftRadius: wp(2.5),
  },
  cardContent: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(1.5),
    marginHorizontal: wp(4),
    borderRadius: wp(2),
    alignItems: "center",
    justifyContent: "space-between",
    flexDirection: "row",
    flex: 1
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: wp(2),
  },
  actionButton: {
    padding: wp(1.6),
    borderRadius: wp(8),
  },
}); 