import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import { hp, wp } from '../../Custom/Responsiveness';

const GiftCardDescription = ({ description }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { getTextColor } = useTheme();
  const AppText = useAppText();

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <ResponsiveText
          color={getTextColor()}
          size={4}
          weight="bold"
        >
          {AppText.GIFT_CARD_DETAILS}
        </ResponsiveText>
        <Icon
          source={isExpanded ? globalpath.down : globalpath.up}
          size={wp(4)}
          tintColor={getTextColor()}
        />
      </TouchableOpacity>
      {isExpanded && (
        <View style={styles.descriptionContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={3.5}
            weight="400"
          >
            {description}
          </ResponsiveText>
        </View>
      )}
    </View>
  );
};

export default GiftCardDescription;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
    marginHorizontal: wp(5),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  descriptionContainer: {
    marginTop: hp(1),
  },
}); 