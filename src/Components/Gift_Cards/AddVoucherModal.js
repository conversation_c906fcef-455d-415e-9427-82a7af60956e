import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, KeyboardAvoidingView, ActivityIndicator, Alert } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import { colors } from '../../Custom/Colors';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import CustomTextInput from '../CustomTextInput';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { add_gift_type } from '../../Services/API/Endpoints/Admin/Giftcard';

const AddVoucherModal = ({ visible, onClose, onSave, initialValue = '', editingVoucher }) => {
  const [voucherName, setVoucherName] = useState('');
  const [loading, setLoading] = useState(false);

  const AppText = useAppText();
  const { getDark_Theme, backgroundColor, getTextColor } = useTheme();
  
  useEffect(() => {
    if (visible) {
      setVoucherName(initialValue);
    }
  }, [visible, initialValue]);

  const handleSave = async () => {
    if (voucherName.trim()) {
      setLoading(true);
      const payload = { name: voucherName.trim() };
  
      try {
        if (editingVoucher) {
          // This call will be made from VouchersTab.js instead
          onSave(voucherName); 
        } else {
          const response = await add_gift_type(payload);
          console.log('🟢 Product name added:', response);
          Alert.alert('Success', 'Product name added successfully!');
          onSave(voucherName, response); // pass response to parent
        }
        setVoucherName('');
        onClose();
      } catch (error) {
        console.error('❌ Error saving gift product name:', error);
        Alert.alert('Error', 'Failed to save product name.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex:1}}
      > 
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent,{backgroundColor:backgroundColor}]}>
            <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />

            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Icon source={globalpath.cross} size={wp(5)} tintColor={getTextColor()} />
              </TouchableOpacity>
            </View>

            <CustomTextInput
              label={AppText.VOUCHER_NAME}
              placeholder={AppText.ENTER_VOUCHER_NAME}
              value={voucherName}
              onChangeText={setVoucherName}
            />

            <TouchableOpacity
              style={[
                styles.saveButton,
                {
                  backgroundColor: voucherName.trim() ? colors.Light_theme_maincolour : colors.grey,
                  opacity: voucherName.trim() ? 1 : 0.5,
                },
              ]}
              disabled={!voucherName.trim() || loading}
              onPress={handleSave}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                  {AppText.SAVE}
                </ResponsiveText>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default AddVoucherModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(5),
    paddingBottom: hp(4),
  },
  modalHeader: {
    flexDirection: 'row',
    marginBottom: hp(3),
    alignItems:"flex-end",
    justifyContent:"flex-end",
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
  },
  closeButton: {
    padding: wp(2),
    alignItems:"flex-end",
    justifyContent:"flex-end",
  },
  saveButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(2),
    marginBottom:hp(2)
  },
}); 