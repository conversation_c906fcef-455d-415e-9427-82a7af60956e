import React, { useState, useRef } from 'react';
import { StyleSheet, View, ScrollView, Dimensions, Image } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import { globalpath } from '../../Custom/globalpath';

const { width } = Dimensions.get('window');

const CardImageCarousel = ({ image }) => {
  console.log("image is ", image)
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef(null);

  // Create an array with the same image repeated 3 times
  const images = [image, image, image];

  const handleScroll = (event) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const offset = event.nativeEvent.contentOffset.x;
    const activeSlide = Math.floor(offset / slideSize);
    setActiveIndex(activeSlide);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
         <View style={styles.imageContainer}>
            <Image
             source={image
                                  ? { uri: image } // Remote image (from API)
                                  : globalpath.logo }
              style={styles.image}
              resizeMode="contain"
            />
          </View>
        {/* {images.map((img, index) => (
          <View key={index} style={styles.imageContainer}>
            <Image
             source={img
                                  ? { uri: img } // Remote image (from API)
                                  : globalpath.logo }
              style={styles.image}
              resizeMode="contain"
            />
          </View>
        ))} */}
      </ScrollView>
      {/* <View style={styles.paginationContainer}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              {
                backgroundColor: index === activeIndex ? colors.Light_theme_maincolour : colors.grey,
                width: index === activeIndex ? wp(6) : wp(2),
                borderRadius: index === activeIndex ? wp(2) : wp(1),
              },
            ]}
          />
        ))}
      </View> */}
    </View>
  );
};

export default CardImageCarousel;

const styles = StyleSheet.create({
  container: {
    height: hp(40),
    backgroundColor: colors.lightGrey1,
    borderBottomRightRadius: wp(4),
    borderBottomLeftRadius: wp(4),
    marginBottom: hp(1),
    bottom: hp(1.5),
  },
  imageContainer: {
    width: width,
    alignItems: "center",
    justifyContent: "center",
    bottom: hp()
  },
  image: {
    width: '80%',
    height: '80%',
  },
  paginationContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: hp(2.5),
    alignSelf: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    height: wp(2),
    marginHorizontal: wp(1.5),
  },
}); 