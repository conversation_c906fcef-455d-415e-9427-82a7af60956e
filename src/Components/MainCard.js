import React from 'react';
import { StyleSheet, View } from 'react-native';
import { colors } from '../Custom/Colors';
import { hp, wp } from '../Custom/Responsiveness';
import useTheme from '../Redux/useTheme';

const MainCard = ({ children, style }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor ,getDark_Theme} = useTheme();
  
  return (
    <View style={[styles.mainCard, { 
      backgroundColor: backgroundColor,
      borderColor: getDark_Theme(),
      shadowColor: getTextColor()
    }, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  mainCard: {
    margin: wp(3),

    borderRadius: wp(3),
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    // These ensure the card wraps its content properly
    alignSelf: 'stretch', // or 'flex-start' depending on your needs
    flexShrink: 1,       // allows shrinking if needed
    flexGrow: 0,    
    // paddingHorizontal:wp(3)   // prevents growing beyond content size
  },
});

export default MainCard;