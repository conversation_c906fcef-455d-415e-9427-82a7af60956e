import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const ServiceCard = ({ item, isSelected, onPress, onSeeDetails }) => {
  const { getDark_Theme, getTextColor, getborderTextColor } = useTheme();
  const AppText = useAppText();

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          borderColor: isSelected ? colors.Light_theme_maincolour : getDark_Theme(),
        },
      ]}
      onPress={onPress}
    >
      {isSelected && (
        <Icon
          source={globalpath.tick}
          size={wp(4)}
          style={styles.tickIcon}
        />
      )}
      <Image source={item.image} style={styles.image} />
      <View style={styles.contentContainer}>
        <ResponsiveText
          color={getTextColor()}
          size={4.3}
          weight="bold"
          numberOfLines={1}
        >
          {item.title}
        </ResponsiveText>
        <ResponsiveText
          color={getborderTextColor()}
          size={3.7}
          weight="400"
          margin={[hp(0.5), 0, hp(0.5), 0]}
          numberOfLines={1}
        >
          {item.Description}
        </ResponsiveText>
        <View style={styles.bottomRow}>
          <View style={[styles.timeContainer, { backgroundColor: colors.lightGrey6 }]}>
            <Icon source={globalpath.watch} size={wp(3)} tintColor={colors.c_green} />
            <ResponsiveText
              color={colors.c_green}
              size={3}
              weight="500"
              margin={[0, 0, 0, wp(1)]}
            >
              {item.time}
            </ResponsiveText>
          </View>
          <TouchableOpacity 
            style={[styles.timeContainer]}
            onPress={() => onSeeDetails && onSeeDetails(item)}
          >
            <Icon source={globalpath.eye2} size={wp(4)} tintColor={getborderTextColor()} margin={[0, 0, wp(0.5), wp(0)]} />
            <ResponsiveText
              color={getborderTextColor()}
              size={3}
              weight="500"
              margin={[0, 0, 0, wp(1)]}
            >
              {AppText.SEE_DETAILS}
            </ResponsiveText>
          </TouchableOpacity>
          <View style={styles.priceContainer}>
            <ResponsiveText
              color={colors.white}
              size={4}
              weight="bold"
            >
              ${item.amount}
            </ResponsiveText>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ServiceCard;

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
    position: 'relative',
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
  },
  contentContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(0.5),
    borderRadius: wp(1)
  },
  tickIcon: {
    position: 'absolute',
    top: hp(1),
    right: wp(2),
  },
  priceContainer: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.2),
    borderRadius: wp(1)
  }
}); 