import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Alert } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const BarberCard = ({ item, isSelected, onPress, from,salon  , service}) => {
  console.log('Coming from -- > ', service);
  const { getDark_Theme, getTextColor, getborderTextColor } = useTheme();
  const AppText = useAppText();

  const handleBarberPress = () => {
    // ✅ Check if barber is from another salon
    const isFromAnotherSalon =
      (from === 'Salon' || from === 'Add') &&
      item.shop != null &&
      item.shop !== salon?.id;
  
    // ✅ Check if barber is already assigned to this current service
    const isBarberAlreadyInThisService =
      Array.isArray(service?.assignedBarbers) &&
      service.assignedBarbers.some(
        barber => barber.id === item.id && barber.service_to_barber_id
      );
  
    // 🛑 Block if from another salon
    if (isFromAnotherSalon) {
      Alert.alert(
        'Barber Blocked',
        'This barber is already linked to another salon.',
        [{ text: 'OK' }]
      );
      return;
    }
  
    // 🛑 Block if already assigned to this service
    if (isBarberAlreadyInThisService) {
      Alert.alert(
        'Barber Already Assigned',
        'This barber is already linked to this service.',
        [{ text: 'OK' }]
      );
      return;
    }
  
    // ✅ Otherwise allow selection
    onPress();
  };
  
  
  
  

  

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          borderColor: isSelected ? colors.Light_theme_maincolour : getDark_Theme(),
        },
      ]}
      onPress={handleBarberPress}  // Trigger the custom function
    >
      {isSelected && (
        <Icon
          source={globalpath.tick}
          size={wp(4)}
          style={styles.tickIcon}
        />
      )}
      <Image
        source={
          item.profile_image
            ? { uri: item.profile_image } // Remote image (from API)
            : globalpath.logo // Local fallback image
        }
        style={styles.image}
      />
      <View style={styles.contentContainer}>
        <ResponsiveText
          color={getTextColor()}
          size={4.3}
          weight="bold"
          numberOfLines={1}
        >
          {item.full_name}
        </ResponsiveText>
        
        <ResponsiveText
          color={getborderTextColor()}
          size={3.7}
          weight="400"
          margin={[hp(0.5), 0, hp(0.5), 0]}
          numberOfLines={1}
        >
          {item.email}
        </ResponsiveText>

        {(from === 'Salon' || from === 'Add') && item.shop != null && item.shop !== salon?.id ? (
  <View style={styles.nameContainer}>
    <Icon source={globalpath.award} size={wp(4)} tintColor={colors.c_green} />
    <ResponsiveText
      color={colors.red}
      size={3}
      weight="500"
      margin={[0, 0, 0, wp(1)]}
    >
      This barber is already linked.
    </ResponsiveText>
  </View>
) : (
  <View style={styles.nameContainer}>
    <Icon source={globalpath.award} size={wp(4)} tintColor={colors.c_green} />
    <ResponsiveText
      color={colors.c_green}
      size={3}
      weight="500"
      margin={[0, 0, 0, wp(1)]}
    >
      {item.phone_number}
    </ResponsiveText>
  </View>
)}

        
      </View>
    </TouchableOpacity>
  );
};

export default BarberCard;

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    marginBottom: hp(2),
    position: 'relative',
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
  },
  contentContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  tickIcon: {
    position: 'absolute',
    top: hp(1),
    right: wp(2),
  },
});
