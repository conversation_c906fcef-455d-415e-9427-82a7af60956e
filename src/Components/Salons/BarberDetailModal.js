import React from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Image, Alert, Platform } from 'react-native';
import Modal from 'react-native-modal';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const BarberDetailModal = ({ visible, onClose, barbers, onDeleteBarber }) => {
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const AppText = useAppText();

  if (!barbers || barbers.length === 0) return null;

  const handleDeleteBarber = (barber) => {
    console.log('🔄 Attempting to delete barber:', barber);
    Alert.alert(
      AppText.DELETE_BARBER,
      AppText.DELETE_BARBER_CONFIRMATION,
      [
        {
          text: AppText.CANCEL,
          style: 'cancel',
          onPress: () => console.log('❌ Delete cancelled')
        },
        {
          text: AppText.DELETE,
          style: 'destructive',
          onPress: () => {
            console.log('✅ Deleting barber:', barber);
            
            onDeleteBarber(barber);
          }
        },
      ]
    );
  };

  const formatDescription = (description) => {
    if (!description) return '';
    
    try {
      const lines = description.split('\n');
      const formattedLines = lines.map((line, index) => {
        if (!line) return '';
        if (line.trim().startsWith('•')) {
          return line;
        } else if (line.trim().startsWith('-')) {
          return '  ' + line;
        } else if (line.trim()) {
          return '• ' + line;
        }
        return line;
      });
      return formattedLines.join('\n');
    } catch (error) {
      console.error('Error formatting description:', error);
      return description || '';
    }
  };

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={[styles.container, { backgroundColor }]}>
        <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />
        
        <View style={styles.headerContainer}>
          <View style={styles.titleContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={5}
              weight="bold"
            >
              {AppText.SELECTED_BARBERS}
            </ResponsiveText>
            <View style={[styles.barberCountContainer, { backgroundColor: colors.Light_theme_maincolour }]}>
              <ResponsiveText
                color={colors.white}
                size={4}
                weight="bold"
              >
                {barbers.length}
              </ResponsiveText>
            </View>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon source={globalpath.cross} size={wp(6)} tintColor={getTextColor()} />
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.scrollContainer} 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {barbers.map((barber, index) => (
            <View 
              key={barber.id} 
              style={[
                styles.barberCard,
                { 
                  backgroundColor: backgroundColor,
                  ...Platform.select({
                    ios: {
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 4,
                    },
                    android: {
                      elevation: 3,
                    },
                  }),
                }
              ]}
            >
              <View style={styles.barberHeader}>
                <View style={styles.barberImageContainer}>
                  <Image
                    source={
                      barber.profile_image
                        ? { uri: barber.profile_image }
                        : globalpath.logo
                    }
                    style={styles.image}
                  />
                  {/* <View style={styles.onlineIndicator} /> */}
                </View>
                
                <View style={styles.barberInfo}>
                  <View style={styles.nameContainer}>
                    <ResponsiveText
                      color={getTextColor()}
                      size={4.5}
                      weight="bold"
                    >
                      {barber.full_name || 'N/A'}
                    </ResponsiveText>
                    <TouchableOpacity 
                      style={styles.deleteButton}
                      onPress={() => handleDeleteBarber(barber)}
                    >
                      <Icon source={globalpath.delete_icon} size={wp(4)} tintColor={colors.red} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.contactContainer}>
                    <View style={styles.contactInfo}>
                      <Icon source={globalpath.email} size={wp(4)} tintColor={colors.Light_theme_maincolour} />
                      <ResponsiveText
                        color={getTextColor()}
                        size={3.5}
                        weight="400"
                        margin={[0, 0, 0, wp(2)]}
                      >
                        {barber.email || 'N/A'}
                      </ResponsiveText>
                    </View>

                    <View style={styles.contactInfo}>
                      <Icon source={globalpath.phone} size={wp(4)} tintColor={colors.Light_theme_maincolour} />
                      <ResponsiveText
                        color={getTextColor()}
                        size={3.5}
                        weight="400"
                        margin={[0, 0, 0, wp(2)]}
                      >
                        {barber.phone_number || 'N/A'}
                      </ResponsiveText>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.descriptionContainer}>
                <ResponsiveText
                  color={getTextColor()}
                  size={4.2}
                  weight="bold"
                  margin={[hp(1), 0, hp(1), 0]}
                >
                  {AppText.BARBER_DESCRIPTION}
                </ResponsiveText>
                <ResponsiveText
                  color={getTextColor()}
                  size={3.5}
                  weight="400"
                  lineHeight={5}
                  style={styles.bulletPoints}
                >
                  {formatDescription(barber.description)}
                </ResponsiveText>
              </View>

              {index < barbers.length - 1 && (
                <View style={[styles.separator, { backgroundColor: colors.lightGrey }]} />
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </Modal>
  );
};

export default BarberDetailModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    maxHeight: '90%',
    height: hp(80),
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
    marginBottom: hp(1),
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  barberCountContainer: {
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    borderRadius: wp(2),
    marginLeft: wp(2),
  },
  closeButton: {
    padding: wp(2),
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: hp(2),
  },
  barberCard: {
    borderRadius: wp(3),
    padding: wp(4),
    marginBottom: hp(2),
  },
  barberHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  barberImageContainer: {
    position: 'relative',
    marginRight: wp(3),
  },
  image: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    backgroundColor: colors.c_green,
    borderWidth: 2,
    borderColor: colors.white,
  },
  barberInfo: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  contactContainer: {
    marginTop: hp(1),
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  deleteButton: {
    padding: wp(2),
  },
  descriptionContainer: {
    marginTop: hp(2),
    paddingTop: hp(2),
    borderTopWidth: 1,
    borderTopColor: colors.lightGrey,
  },
  bulletPoints: {
    textAlign: 'left',
  },
  separator: {
    height: 1,
    marginTop: hp(2),
  },
});