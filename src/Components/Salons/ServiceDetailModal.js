import React from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Image } from 'react-native';
import Modal from 'react-native-modal';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const ServiceDetailModal = ({ visible, onClose, service }) => {
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const AppText = useAppText();

  if (!service) return null;

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={[styles.container, { backgroundColor }]}>
        <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Icon source={globalpath.cross} size={wp(6)} tintColor={getTextColor()} />
        </TouchableOpacity>

        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.contentContainer}>
            <Image
              source={
                service.service_image
                  ? { uri: service.service_image }
                  : globalpath.logo
              }
              style={styles.image}
            />
            
            <View style={styles.detailsContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={5}
                weight="bold"
                margin={[hp(2), 0, hp(1), 0]}
              >
                {service.name}
              </ResponsiveText>

              <View style={styles.timePriceContainer}>
                <View style={[styles.timeContainer, { backgroundColor: colors.lightGrey6 }]}>
                  <Icon source={globalpath.watch} size={wp(3)} tintColor={colors.c_green} />
                  <ResponsiveText
                    color={colors.c_green}
                    size={3}
                    weight="500"
                    margin={[0, 0, 0, wp(1)]}
                  >
                    {service.duration} min
                  </ResponsiveText>
                </View>
                <View style={styles.priceContainer}>
                  <ResponsiveText
                    color={colors.white}
                    size={4}
                    weight="bold"
                  >
                    ${service.price}
                  </ResponsiveText>
                </View>
              </View>

              <View style={styles.descriptionContainer}>
                <ResponsiveText
                  color={getTextColor()}
                  size={4.2}
                  weight="bold"
                  margin={[hp(0), 0, hp(1), 0]}
                >
                  {AppText.SERVICE_DESCRIPTION}
                </ResponsiveText>
                <ResponsiveText
                  color={getTextColor()}
                  size={3.5}
                  weight="400"
                  lineHeight={5}
                >
                  {service.description}
                </ResponsiveText>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default ServiceDetailModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    maxHeight: '90%',
    height: hp(80),
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
    marginBottom: hp(1),
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: hp(1),
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  image: {
    width: '100%',
    height: hp(25),
    borderRadius: wp(3),
    marginBottom: hp(2),
    resizeMode: 'contain',
  },
  detailsContainer: {
    flex: 1,
  },
  timePriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: hp(1),
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    borderRadius: wp(1),
  },
  priceContainer: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    borderRadius: wp(1),
  },
  descriptionContainer: {
    marginTop: hp(2),
    // backgroundColor: 'red',
    padding: wp(4),
  },
});