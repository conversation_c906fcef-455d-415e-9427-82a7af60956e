import React, { useState, useEffect , useCallback } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView , FlatList , RefreshControl, Alert } from 'react-native';
import Modal from 'react-native-modal';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ServiceCard from './ServiceCard';
import { serviceMockData } from '../../Mocks/service_mock';
import { get_all_service } from '../../Services/API/Endpoints/Admin/Services';
import {useFocusEffect} from '@react-navigation/core';
import ServiceDetailModal from './ServiceDetailModal';
import ServiceTab_Service_Card from '../Services_Components/ServiceTab_Service_Card';
import { shop_to_service } from '../../Services/API/Endpoints/Admin/Salons';

const AddServiceModal = ({ visible, onClose, onServiceSelect, previouslySelectedServices = [], salon , from }) => {
  console.log("AddServiceModal - Salon Data:", from);

  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const AppText = useAppText();
  const [selectedServices, setSelectedServices] = useState([]);
  const [Services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [showServiceDetail, setShowServiceDetail] = useState(false);
  const [selectedServiceForDetail, setSelectedServiceForDetail] = useState(null);


  const onRefresh = useCallback(() => {
    fetchServices();
    handleSearch('');
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchServices();
    }, []),
  );

  useEffect(() => {
    setFilteredServices(Services);
    // Reset selected services when modal opens
    setSelectedServices([]);
  }, [Services, visible]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setRefreshing(true);

      const response = await get_all_service();
      console.log('🟢 Services fetched:', response);

      if (Array.isArray(response)) {
        const sortedServices = response.sort((a, b) => b.id - a.id);
        setServices(sortedServices);
        setFilteredServices(sortedServices);
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleServiceSelect = (service) => {
    // Check if service is already selected in previouslySelectedServices
    const isPreviouslySelected = previouslySelectedServices.some(s => s.id === service.id);
    if (isPreviouslySelected) {
      Alert.alert(
        AppText.SERVICE_ALREADY_SELECTED,
        AppText.SERVICE_ALREADY_SELECTED_MESSAGE,
        [{ text: AppText.OK, style: 'default' }]
      );
      return; // Don't allow selection of previously selected services
    }

    setSelectedServices(prev => {
      const isSelected = prev.some(s => s.id === service.id);
      if (isSelected) {
        return prev.filter(s => s.id !== service.id);
      } else {
        return [...prev, service];
      }
    });
  };

  const handleServicePress = (service) => {
    setSelectedServiceForDetail(service);
    setShowServiceDetail(true);
  };



const handleAddServices = () => {
  if (from === 'Salon') {
    if (selectedServices.length === 0) {
      Alert.alert('No Services Selected', 'Please select at least one service.');
      return;
    }
  
    Alert.alert(
      'Add Service',
      'Are you sure you want to add this service to this salon?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => console.log('🚫 Add service cancelled.'),
        },
        {
          text: 'Yes',
          onPress: async () => {
            const serviceIds = selectedServices.map(service => service.id);
            const payload = {
              shop: salon.id,
              service: serviceIds,
            };
  
            console.log('📦 Final Payload to Send:', payload);
  
            try {
              // ✅ Hit API
              const response = await shop_to_service(payload);
              console.log('✅ shop_to_service response:', response);
  
              if (response && Array.isArray(response.created) && response.created.length > 0) {
                console.log('🎉 Service(s) added successfully:', response);
              
                onServiceSelect(selectedServices);
                setSelectedServices([]);
                onClose();
                Alert.alert('🎉 Service(s) added successfully')
              } else {
                throw new Error('No services were created. Please try again.');
              }
            } catch (error) {
              console.error(
                '❌ Error in shop_to_service:',
                error?.response?.data || error.message || error
              );
  
              const errorMessage =
                error?.response?.data?.errors?.join('\n') ||
                error?.message ||
                'Something went wrong while adding the service(s).';
  
              Alert.alert('Error', errorMessage);
            }
          },
        },
      ],
      { cancelable: true }
    );
  }
  else{
    onServiceSelect(selectedServices);
    setSelectedServices([]);
    onClose();

  }
  
};

  

  const handleClose = () => {
    setSelectedServices([]);
    onClose();
  };

  const renderServiceItem = ({item}) => {
    const isPreviouslySelected = previouslySelectedServices.some(s => s.id === item.id);
    
    return (
      <ServiceTab_Service_Card
        key={item.id}
        item={{
          id: item.id,
          service_image: item.service_image? item.service_image : undefined,
          title: item.name,
          description: item.description,
          duration: `${item.duration} min`,
          amount: item.price,
        }}
        onPress={() => handleServiceSelect(item)}
        onSeeDetails={() => handleServicePress(item)}
        isSelected={selectedServices.some(s => s.id === item.id)}
        isDisabled={isPreviouslySelected}
        showCheckmark={isPreviouslySelected}
      />
    );
  };

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={handleClose}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={[styles.container, { backgroundColor }]}>
        <View style={[styles.dragBar, { backgroundColor: getDark_Theme() }]} />
        <TouchableOpacity onPress={handleClose} style={{alignItems:"flex-end",justifyContent:"flex-end"}}>
          <Icon source={globalpath.cross} size={wp(6)} tintColor={getTextColor()} />
        </TouchableOpacity>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={5}
              weight="600"
            >
              {AppText.SELECT_MULTIPLE_SERVICES}
            </ResponsiveText>
          </View>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={item => item.id.toString()}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.Light_theme_maincolour]}
                tintColor={colors.Light_theme_maincolour}
              />
            }
            ListEmptyComponent={() => (
              <View style={styles.noResults}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {AppText.NO_RESULTS_FOUND}
                </ResponsiveText>
              </View>
            )}
          />
        </ScrollView>

        <TouchableOpacity
          style={[
            styles.addButton,
            { backgroundColor: selectedServices.length > 0 ? colors.Light_theme_maincolour : getDark_Theme() }
          ]}
          onPress={handleAddServices}
          disabled={selectedServices.length === 0}
        >
          <ResponsiveText
            color={colors.white}
            size={4}
            weight="bold"
          >
            {AppText.ADD_SERVICE} ({selectedServices.length})
          </ResponsiveText>
        </TouchableOpacity>

        <ServiceDetailModal
          visible={showServiceDetail}
          onClose={() => setShowServiceDetail(false)}
          service={selectedServiceForDetail}
        />
      </View>
    </Modal>
  );
};

export default AddServiceModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
    padding: wp(4),
    maxHeight: '80%',
  },
  dragBar: {
    width: wp(35),
    height: hp(0.5),
    borderRadius: hp(0.25),
    alignSelf: 'center',
    marginBottom: hp(1),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(1.5)
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: "space-between",
  },
  content: {
    marginBottom: hp(2),
  },
  addButton: {
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginBottom: wp(6)
  },
}); 