import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';

const WeekDayCard = ({
  day,
  isSelected,
  onPress,
  width = wp(25),
}) => {
  const { getDark_Theme, getTextColor } = useTheme();


  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          borderColor: getDark_Theme(),
          backgroundColor: isSelected ? colors.Light_theme_maincolour : 'transparent',
          width,
        },
      ]}
      onPress={onPress}
    >
      {isSelected && (
        <Icon
          source={globalpath.tick}
          size={wp(4)}
          style={styles.tickIcon}
        />
      )}
      <ResponsiveText
        color={isSelected ? colors.white : getTextColor()}
        size={4}
        weight="600"
      >
        {day}
      </ResponsiveText>
    </TouchableOpacity>
  );
};

export default WeekDayCard;

const styles = StyleSheet.create({
  card: {
    height: hp(11),
    borderWidth: 1,
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: wp(1),
    position: 'relative',
  },
  tickIcon: {
    position: 'absolute',
    top: hp(0.5),
    right: wp(1),
  },
});