import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Modal,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';

const TimePickerModal = ({
  visible,
  onClose,
  onSave,
  initialStartTime,
  initialEndTime,
}) => {

  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [startTime, setStartTime] = useState(initialStartTime || new Date());
  const [endTime, setEndTime] = useState(initialEndTime || new Date());
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);


  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const handleSave = () => {
    // Validate that start time is not equal to end time
    if (startTime.getTime() === endTime.getTime()) {
      Alert.alert('Validation Error', 'Start time cannot be equal to end time.');
      return;
    }

    // Validate that end time is later than start time
    if (startTime.getTime() >= endTime.getTime()) {
      Alert.alert('Validation Error', 'End time must be later than start time.');
      return;
    }

    // If validations pass, save the times
    onSave(startTime, endTime);
    onClose();
  };

  // Round time to nearest 15 minutes
  const roundToNearest15Minutes = (date) => {
    const minutes = date.getMinutes();
    const remainder = minutes % 15;
    const roundedMinutes = remainder < 8 ? minutes - remainder : minutes + (15 - remainder);

    const newDate = new Date(date);
    newDate.setMinutes(roundedMinutes);
    newDate.setSeconds(0);

    return newDate;
  };

  const handleStartTimeConfirm = (date) => {
    // Round to nearest 15 minutes
    const roundedDate = roundToNearest15Minutes(date);
    setStartTime(roundedDate);
    setShowStartPicker(false);
  };

  const handleEndTimeConfirm = (date) => {
    // Round to nearest 15 minutes
    const roundedDate = roundToNearest15Minutes(date);
    setEndTime(roundedDate);
    setShowEndPicker(false);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor , borderColor: getDark_Theme() }]}>
          <ResponsiveText
            color={getTextColor()}
            size={5}
            weight="bold"
            margin={[0, 0, hp(2), 0]}
          >
            {AppText.SELECT_TIME}
          </ResponsiveText>

          <View style={styles.timeContainer}>
            <View style={styles.timeSection}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="500"
                margin={[0, 0, hp(1), 0]}
              >
                {AppText.START_TIME}
              </ResponsiveText>
              <TouchableOpacity
                style={[styles.timeButton, { borderColor: getDark_Theme() }]}
                onPress={() => setShowStartPicker(true)}
              >
                <ResponsiveText color={getTextColor()} size={4}>
                  {formatTime(startTime)}
                </ResponsiveText>
              </TouchableOpacity>
            </View>

            <View style={styles.timeSection}>
              <ResponsiveText
              color={getTextColor()}
                size={4}
                weight="500"
                margin={[0, 0, hp(1), 0]}
              >
                {AppText.END_TIME}
              </ResponsiveText>
              <TouchableOpacity
                style={[styles.timeButton, { borderColor: getDark_Theme() }]}
                onPress={() => setShowEndPicker(true)}
              >
                <ResponsiveText color={getTextColor()} size={4}>
                  {formatTime(endTime)}
                </ResponsiveText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, {backgroundColor: getDark_Theme()}]}
              onPress={onClose}
            >
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.CANCEL}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
            >
              <ResponsiveText color={colors.white} size={4}>
                {AppText.SAVE}
              </ResponsiveText>
            </TouchableOpacity>
          </View>

          <DateTimePickerModal
            isVisible={showStartPicker}
            mode="time"
            onConfirm={handleStartTimeConfirm}
            onCancel={() => setShowStartPicker(false)}
            date={startTime}
            is24Hour={false}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            locale="en_US"
          />

          <DateTimePickerModal
            isVisible={showEndPicker}
            mode="time"
            onConfirm={handleEndTimeConfirm}
            onCancel={() => setShowEndPicker(false)}
            date={endTime}
            is24Hour={false}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            locale="en_US"
          />
        </View>
      </View>
    </Modal>
  );
};

export default TimePickerModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    padding: wp(5),
    borderRadius: wp(3),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(3),
  },
  timeSection: {
    flex: 1,
    marginHorizontal: wp(2),
  },
  timeButton: {
    borderWidth: 1,
    borderRadius: wp(2),
    padding: wp(3),
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    flex: 1,
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  saveButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});