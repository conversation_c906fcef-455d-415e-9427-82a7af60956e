import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, Alert } from 'react-native';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
import { colors } from '../../Custom/Colors';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ServiceDetailModal from './ServiceDetailModal';
import BarberDetailModal from './BarberDetailModal';
import { deleteServiceToBarberByQuery , deleteServiceToBarberByQuery_filter } from '../../Services/API/Endpoints/Admin/Salons';
import { update_barber } from '../../Services/API/Endpoints/Admin/Barber';

const SelectedServiceCard = ({ service, onAssignBarber, onDelete,  salon ,  from  }) => {
  // console.log("SelectedServiceCard - Salon Data:", salon);

  const { getDark_Theme, getTextColor, getborderTextColor } = useTheme();
  const AppText = useAppText();
  const [showServiceDetail, setShowServiceDetail] = useState(false);
  const [showBarberDetail, setShowBarberDetail] = useState(false);

      const handleDeleteBarber = (barber) => {
        console.log('🔄 Deleting barber from service:', {
          serviceId: service.id,
          barberId: barber.id,
         
        });

        if(from === 'Add'){
           // ✅ Update local service data
        const updatedBarbers = service.assignedBarbers.filter(b => b.id !== barber.id);
        service.assignedBarbers = updatedBarbers;
        console.log('✅ Updated service barbers:', updatedBarbers);

        // ✅ Close modal
        setShowBarberDetail(false);

        

        }
        else{
           // ✅ Conditionally delete using the correct method
        if (barber.service_to_barber_id) {
          deleteServiceToBarber(barber.id, service.id, barber.service_to_barber_id);
          const updatedBarbers = service.assignedBarbers.filter(b => b.id !== barber.id);
          service.assignedBarbers = updatedBarbers;
          console.log('✅ Updated service barbers:', updatedBarbers);
  
          // ✅ Close modal
          setShowBarberDetail(false);
        } else {
          
          deleteServiceToBarberByQuery_func(barber.id, service.id)
          const updatedBarbers = service.assignedBarbers.filter(b => b.id !== barber.id);
          service.assignedBarbers = updatedBarbers;
          console.log('✅ Updated service barbers:', updatedBarbers);
  
          // ✅ Close modal
          setShowBarberDetail(false);
            
        }

        }
       

       
      };


  const deleteServiceToBarber = async (barberID, serviceId , id) => {
    try {
      console.warn(`Deleting barber id ${barberID} from service ${serviceId} and the id is ${id}`);
      await deleteServiceToBarberByQuery(id); // actual API call
      await updateBarber(barberID);
      console.log(`✅ Deleted barber ${id}`);
      Alert.alert('🎉 Barber deleted successfully')

    } catch (error) {
      console.error(`❌ Failed to delete service ${serviceId}:`, error);
    }
  };


  const deleteServiceToBarberByQuery_func = async (barberID, serviceId) => {
    try {
    
      await deleteServiceToBarberByQuery_filter(barberID , serviceId); // actual API call
      await updateBarber(barberID);
      console.log(`✅ Deleted barber `);
      Alert.alert('🎉 Barber deleted successfully')
    } catch (error) {
      console.error(`❌ Failed to delete service ${serviceId}:`, error);
    }
  };



  const updateBarber = async (barberId) => {
    try {
      const payload = {
        shop: null,
      
      };

      console.log('🚀 REMOVE SHOP ID FROM BARBER ID:', payload);

      // Call the API to update barber details for the shop
      const response = await update_barber(barberId, payload); // Assuming update_barber is the API function
      console.log('✅ Barber DE-linked to shop successfully:', response);
    } catch (error) {
      console.error(
        '❌ Error in linkshop_to_barber:',
        error?.response?.data || error,
      );
      // Optional: Alert or handle individual barber error
      Alert.alert(
        'Error',
        `Failed to link Barber ID ${barberId} to shop: ${error.message}`,
      );
    }
  };

  // console.log("Selected Service:", service);
  // console.log("Assigned Barbers:", service.assignedBarbers);

  return (
    <>
      <View style={[styles.card, { borderColor: getDark_Theme() }]}>
        <Image
          source={
            service.service_image
              ? { uri: service.service_image }
              : globalpath.logo
          }
          style={styles.image}
        />
        <View style={styles.contentContainer}>
          <View style={styles.titleRow}>
            <ResponsiveText
              color={getTextColor()}
              size={4.2}
              weight="bold"
              numberOfLines={1}
              maxWidth={wp(30)}
            >
              {service.name} 
            </ResponsiveText>
            <TouchableOpacity
              style={[styles.assignButton, { backgroundColor: colors.Light_theme_maincolour }]}
              onPress={onAssignBarber}
            >
              <ResponsiveText
                color={colors.white}
                size={3.5}
                weight="500"
              >
                {AppText.ASSIGN_BARBER} ({service.assignedBarbers?.length || 0})
              </ResponsiveText>
            </TouchableOpacity>
          </View>
          <View style={styles.detailsRow}>
            <TouchableOpacity 
              style={[styles.detailsButton, { marginTop: hp(1.5) }]}
              onPress={() => setShowServiceDetail(true)}
            >
              <Icon source={globalpath.eye2} size={wp(4)} tintColor={getborderTextColor()} margin={[0,0,wp(0.5),wp(0)]}/>
              <ResponsiveText
                color={getborderTextColor()}
                size={3}
                weight="500"
                margin={[0, 0, 0, wp(1)]}
              >
                {AppText.SEE_DETAILS}
              </ResponsiveText>
            </TouchableOpacity>
            {service.assignedBarbers && service.assignedBarbers.length > 0 && (
              <TouchableOpacity 
                style={[styles.detailsButton, { marginTop: hp(1.5) }]}
                onPress={() => setShowBarberDetail(true)}
              >
                <Icon source={globalpath.award} size={wp(4)} tintColor={getborderTextColor()} margin={[0,0,wp(0.5),wp(0)]}/>
                <ResponsiveText
                  color={getborderTextColor()}
                  size={3}
                  weight="500"
                  margin={[0, 0, 0, wp(1)]}
                >
                  {AppText.VIEW_BARBER} ({service.assignedBarbers.length})
                </ResponsiveText>
              </TouchableOpacity>
            )}
            <TouchableOpacity 
              style={[styles.deleteButton, { marginTop: hp(1.5) }]}
              onPress={onDelete}
            >
              <Icon source={globalpath.delete_icon} size={wp(4)} tintColor={colors.red} margin={[0,0,wp(0.5),wp(0)]}/>
              <ResponsiveText
                color={colors.red}
                size={3}
                weight="500"
                margin={[0, 0, 0, wp(1)]}
              >
                {AppText.DELETE}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ServiceDetailModal
        visible={showServiceDetail}
        onClose={() => setShowServiceDetail(false)}
        service={service}
      />

      <BarberDetailModal
        visible={showBarberDetail}
        onClose={() => setShowBarberDetail(false)}
        barbers={service.assignedBarbers}
        onDeleteBarber={handleDeleteBarber}
      />
    </>
  );
};

export default SelectedServiceCard;

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
    marginBottom: hp(1),
  },
  image: {
    width: wp(14),
    height: wp(14),
    borderRadius: wp(2),
  },
  contentContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assignButton: {
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(0.3),
    borderRadius: wp(1),
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: hp(1.5),
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}); 