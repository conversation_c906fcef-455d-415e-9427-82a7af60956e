// src/Components/CustomHeader.js
import React from 'react';
import { View, TouchableOpacity, StatusBar, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';
import ResponsiveText from '../Custom/RnText';
import { wp, hp } from '../Custom/Responsiveness';
import { colors } from '../Custom/Colors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useTheme from '../Redux/useTheme';
const CustomHeader = ({ 
  title, 
  onPress, 
  showBellIcon = true,
  leftIconType = 'bars' // 'bars' or 'back'
}) => {
  const navigation = useNavigation();
  const { getTextColor, getLightGrayBackground, getBellBackground, getborderTextColor, getDarK_mode_LightGrayBackground ,getDark_Theme,backgroundColor,isDarkTheme} = useTheme();

  const handleLeftIconPress = () => {
    if (leftIconType === 'back') {
      navigation.goBack();
    } else {
      navigation.openDrawer();
    }
  };

  const getLeftIconSource = () => {
    return leftIconType === 'back' ? globalpath.goback : globalpath.bars;
  };

  return (
    <View>
      {/* White Status Bar */}
      <StatusBar backgroundColor="white" barStyle="dark-content" />
      <StatusBar backgroundColor={backgroundColor} barStyle={isDarkTheme() ? "light-content" : "dark-content"} />

      {/* Header Content */}
      <View style={[styles.headerContainer]}>
        {/* Left Icon Container */}
        <View style={styles.leftIconContainer}>
          <TouchableOpacity onPress={handleLeftIconPress}>
            <Icon 
              source={getLeftIconSource()} 
              size={ leftIconType === 'back' ? wp(4.5) : wp(5.5)} 
              margin={[0, 0, 0, wp(5)]} 
              tintColor={getTextColor()} 
            />
          </TouchableOpacity>
        </View>

        {/* Title Container */}
        <View style={styles.titleContainer}>
          <ResponsiveText size={5.1} weight={'600'} color={getTextColor()} style={styles.title}>
            {title}
          </ResponsiveText>
        </View>

        {/* Right Icon Container */}
        <View style={styles.rightIconContainer}>
          {showBellIcon ? (
            <TouchableOpacity
              style={[styles.bellIconContainer, { backgroundColor: getBellBackground() }]}
              onPress={onPress}
            >
              <Icon source={globalpath.bell} size={wp(6.5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          ) : (
            <View style={styles.placeholder} />
          )}
        </View>
      </View>
      <View style={{borderBottomWidth:1, borderColor: getDark_Theme(), marginVertical: wp(2.5)}}/>
    </View>
  );
};

export default CustomHeader;

// Styles
const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp(1),
    justifyContent: 'space-between',
   
  },
  leftIconContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 2, // Takes more space to ensure title is centered
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    textAlign: 'center',
  },
  rightIconContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  bellIconContainer: {
    borderRadius: wp(10),
    padding: wp(1.8),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(3),
  },
  placeholder: {
    width: wp(10), // Same width as the bell icon container
  },
});