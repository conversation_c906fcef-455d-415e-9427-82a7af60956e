import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';
const RecentWorkCard = ({ appointment }) => {
  console.log("appointment: in recent work card", appointment);
    const { getTextColor,backgroundColor,getDark_Theme } = useTheme();
       const AppText = useAppText();
  // Format date to display in a readable format
  const formatDate = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format time to display in a readable format
  const formatTime = (timeString) => {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;

    return `${formattedHour}:${minutes} ${ampm}`;
  };

  // Get service names from the appointment
  const getServiceNames = () => {
    if (!appointment.service_name || appointment.service_name.length === 0) {
      return 'No service';
    }

    return appointment.service_name.map(service => service.name).join(', ');
  };

  return (
    <View style={[styles.container,{borderColor:getDark_Theme()}]}>
      <View style={styles.header}>
        <View style={styles.customerInfo}>
          <Icon
            source={globalpath.calendar_user}
            style={styles.icon}
            tintColor={appointment.customer_appointment ? colors.Light_theme_maincolour : colors.grey}
          />
          <ResponsiveText size={4} weight="bold" color={getTextColor()}>
            {appointment.customer_name}
          </ResponsiveText>
        </View>

        <View style={styles.statusContainer}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: appointment.payment_recieved ? colors.c_green : colors.grey }
          ]} />
          <ResponsiveText size={3.2} color={colors.grey}>
            {appointment.payment_recieved ? 'Paid' : 'Pending'}
          </ResponsiveText>
        </View>
      </View>

      <View style={styles.divider} />

      <View style={styles.content}>
        <View style={styles.infoRow}>
          <Icon
            source={globalpath.calendar}
            style={styles.infoIcon}
            tintColor={colors.grey}
          />
          <ResponsiveText size={3.5} color={colors.grey}>
            {formatDate(appointment.date)}
          </ResponsiveText>
        </View>

        <View style={styles.infoRow}>
          <Icon
            source={globalpath.watch}
            style={styles.infoIcon}
            tintColor={colors.grey}
          />
          <ResponsiveText size={3.5} color={colors.grey}>
            {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
          </ResponsiveText>
        </View>

        <View style={styles.serviceContainer}>
          <ResponsiveText size={3.8} weight="bold" margin={[hp(1), 0, hp(0.5), 0]} color={getTextColor()}>
            {AppText.SERVICES_PROVIDED}
          </ResponsiveText>

          {appointment.service_name && appointment.service_name.map((service, index) => (
            <View key={index} style={styles.serviceRow}>
              <View style={[styles.serviceColorSquare, { backgroundColor: service.color || colors.yellow }]} />
              <ResponsiveText size={3.5} color={colors.grey}>
                {service.name} ({service.modif_duration})
              </ResponsiveText>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // backgroundColor: colors.white,
    borderRadius: wp(3),
    marginBottom: hp(2),
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    borderWidth:1
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: wp(5),
    height: wp(5),
    marginRight: wp(2),
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginRight: wp(1),
  },
  divider: {
    height: 1,
    backgroundColor: colors.lightGrey1,
    marginHorizontal: wp(4),
  },
  content: {
    padding: wp(4),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  infoIcon: {
    width: wp(4),
    height: wp(4),
    marginRight: wp(2),
  },
  serviceContainer: {
    marginTop: hp(1),
  },
  serviceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
  serviceColorSquare: {
    width: wp(3),
    height: wp(3),
    marginRight: wp(2),
  },
});

export default RecentWorkCard;
