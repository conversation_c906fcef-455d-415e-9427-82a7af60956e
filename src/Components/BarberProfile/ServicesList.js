import React from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import LoadingIndicator from '../Common/LoadingIndicator';
import ServiceCard from './ServiceCard';
import useAppText from '../../Custom/AppText';
import App from '../../../App';
const ServicesList = ({ services, isLoading = false }) => {
    const AppText = useAppText();
  if (isLoading) {
    return <LoadingIndicator style={styles.loadingContainer} />;
  }

  if (!services || services.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <ResponsiveText size={4} color={colors.grey}>
          No services available
        </ResponsiveText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ResponsiveText 
        size={5} 
        color={colors.Light_theme_maincolour} 
        weight="bold"
        margin={[0, 0, hp(1.5), wp(5)]}
      >
       {AppText.SERVICES}
      </ResponsiveText>
      
      <FlatList
        data={services}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => <ServiceCard service={item} />}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: hp(2),
  },
  listContent: {
    paddingHorizontal: wp(5),
    paddingBottom: hp(2),
  },
  loadingContainer: {
    height: hp(20),
  },
  emptyContainer: {
    height: hp(20),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(2),
  },
});

export default ServicesList;
