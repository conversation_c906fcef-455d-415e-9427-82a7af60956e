import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
const TabSelector = ({ tabs, selectedTab, onTabChange }) => {
  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => onTabChange(tab.key)}
            style={[
              styles.tab,
              selectedTab === tab.key && styles.selectedTab
            ]}
            activeOpacity={0.7}
          >
            <ResponsiveText
              size={3.5}
              weight="700"
              color={selectedTab === tab.key ? colors.white : colors.grey}
            >
              {tab.label}
            </ResponsiveText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: wp(5),
    marginTop: hp(2),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.lightGrey1,
    borderRadius: wp(3),
    padding: wp(1),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: hp(1.5),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: wp(2),
  },
  selectedTab: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});

export default TabSelector;
