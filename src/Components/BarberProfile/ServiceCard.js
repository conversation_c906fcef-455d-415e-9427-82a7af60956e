import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';

const ServiceCard = ({ service }) => {
    const { backgroundColor ,getDark_Theme,getTextColor} = useTheme();
    const AppText = useAppText();
  // Default color if service.color is null/undefined/empty
  const serviceColor = service.color || colors.Light_theme_maincolour;
  
  // Format duration to display in hours and minutes
  const formatDuration = (minutes) => {
    if (!minutes) return '0 min';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`;
    }
    return `${mins} min`;
  };

  return (
    <View style={[styles.container,{backgroundColor:backgroundColor, borderColor:getDark_Theme()}]}>
      {/* Service color indicator */}
      <View style={[styles.colorIndicator, { backgroundColor: serviceColor }]} />
      
      <View style={styles.contentContainer}>
        <View style={styles.leftContent}>
          {/* Service image */}
          <View style={styles.imageContainer}>
            {service.service_image ? (
              <Image 
                source={{ uri: service.service_image }} 
                style={styles.image}
                resizeMode="cover"
              />
            ) : (
              <Icon 
                source={globalpath.detailbarber} 
                style={styles.image}
                resizeMode="cover"
              />
            )}
          </View>
          
          {/* Service details */}
          <View style={styles.detailsContainer}>
            <ResponsiveText 
              size={4.5} 
              weight="bold" 
              numberOfLines={1}
              color={getTextColor()}
            >
              {service.name}
            </ResponsiveText>
            
            <View style={styles.categoryRow}>
              <ResponsiveText 
                size={3.2} 
                color={colors.grey}
                numberOfLines={1}
              >
                {service.category_name} • {service.sub_category_name}
              </ResponsiveText>
            </View>
            
            <ResponsiveText 
              size={3.5} 
              numberOfLines={2} 
              color={colors.grey}
              margin={[hp(0.5), 0, 0, 0]}
              style={styles.description}
            >
              {service.description?.substring(0, 80)}
              {service.description?.length > 80 ? '...' : ''}
            </ResponsiveText>
          </View>
        </View>
        
        {/* Price and duration */}
        <View style={styles.rightContent}>
          <ResponsiveText 
            size={4.5} 
            weight="bold" 
            color={colors.Light_theme_maincolour}
          >
            ${service.price}
          </ResponsiveText>
          
          <View style={styles.durationContainer}>
            <Icon 
              source={globalpath.clock} 
              style={styles.clockIcon} 
              tintColor={colors.grey}
            />
            <ResponsiveText size={3.2} color={colors.grey}>
              {formatDuration(service.duration)}
            </ResponsiveText>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // backgroundColor: colors.white,
    borderRadius: wp(3),
    marginBottom: hp(2),
    overflow: 'hidden',
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    borderWidth:1
  },
  colorIndicator: {
    width: wp(2),
    height: '100%',
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    padding: wp(3),
  },
  leftContent: {
    flex: 1,
    flexDirection: 'row',
  },
  imageContainer: {
    width: wp(15),
    height: wp(15),
    borderRadius: wp(2),
    overflow: 'hidden',
    backgroundColor: colors.lightGrey1,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  detailsContainer: {
    flex: 1,
    marginLeft: wp(3),
    justifyContent: 'center',
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(0.3),
  },
  description: {
    opacity: 0.8,
  },
  rightContent: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingLeft: wp(2),
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  clockIcon: {
    width: wp(3.5),
    height: wp(3.5),
    marginRight: wp(1),
  },
});

export default ServiceCard;
