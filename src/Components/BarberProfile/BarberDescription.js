import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import ResponsiveText from '../../Custom/RnText';
import useTheme from '../../Redux/useTheme';
import useAppText from '../../Custom/AppText';

/**
 * Component to display barber description
 * @param {Object} props - Component props
 * @param {string} props.description - Barber's description
 */
const BarberDescription = ({ description }) => {
  const { getTextColor,backgroundColor,getDark_Theme } = useTheme();
  const [expanded, setExpanded] = useState(false);
    const AppText = useAppText();

  // If description is empty, don't render anything
  if (!description) return null;

  return (
    <View style={[styles.container]}>
      <View style={[styles.card,{backgroundColor:backgroundColor , borderColor:getDark_Theme()}]}>
        <ResponsiveText 
          size={5} 
          color={colors.Light_theme_maincolour} 
          weight="bold"
          margin={[0, 0, hp(1), 0]}
        >
          {AppText.ABOUT_ME}
        </ResponsiveText>
        
        <ResponsiveText 
          numberOfLines={expanded ? undefined : 3} 
          color={getTextColor()} 
          lineHeight={6}
          size={3.8}
        >
          {description}
        </ResponsiveText>
        
        {description.length > 120 && (
          <TouchableOpacity 
            style={styles.readMoreButton} 
            onPress={() => setExpanded(!expanded)}
          >
            <ResponsiveText 
              size={3.5} 
              color={colors.Light_theme_maincolour}
              weight="bold"
            >
              {expanded ? AppText.READ_LESS : AppText.READ_MORE}
            </ResponsiveText>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: wp(5),
    marginTop: hp(2),
  },
  card: {
    // backgroundColor: colors.white,
    borderRadius: wp(3),
    padding: wp(4),
    marginBottom: hp(2),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    borderWidth:1
  },
  readMoreButton: {
    alignSelf: 'flex-end',
    marginTop: hp(1),
    paddingVertical: hp(0.5),
  },
});

export default BarberDescription;

