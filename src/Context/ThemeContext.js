import React, { createContext, useState, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../Custom/Colors';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [themeColor, setThemeColor] = useState(colors.Light_theme_maincolour); // Default color

  const updateThemeColor = (color) => {
    setThemeColor(color);
    AsyncStorage.setItem('themeColor', color);
  };

  const loadThemeColor = async () => {
    try {
      const savedColor = await AsyncStorage.getItem('themeColor');
      if (savedColor) {
        setThemeColor(savedColor);
      }
    } catch (error) {
      console.error('Error loading theme color:', error);
    }
  };

  return (
    <ThemeContext.Provider value={{ themeColor, updateThemeColor, loadThemeColor }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}; 