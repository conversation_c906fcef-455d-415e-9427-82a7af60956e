// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import My_Orders from '../../Screens/CustomerScreens/TrackOrders/My_Orders';
import Customer_Order from '../../Screens/CustomerScreens/TrackOrders/Customer_Order';
import Customer_History from '../../Screens/CustomerScreens/TrackOrders/Customer_History';
import Customer_Order_Details from '../../Screens/CustomerScreens/TrackOrders/Customer_Order_Details';
const Stack = createStackNavigator();

const Track_Orders_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='My_Orders'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >
      <Stack.Screen 
        name="My_Orders" 
        component={My_Orders}
        options={({ route }) => ({
          params: route.params
        })}
      />
      <Stack.Screen name="Order" component={Customer_Order} />
      <Stack.Screen name="History" component={Customer_History} />
      <Stack.Screen name="Order_Details" component={Customer_Order_Details} />
    </Stack.Navigator>
  );
};

    export default Track_Orders_Stack;