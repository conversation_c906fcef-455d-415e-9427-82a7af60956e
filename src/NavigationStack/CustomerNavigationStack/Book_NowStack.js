// src/Navigations/CustomerStack.js
import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import Book_now from '../../Screens/CustomerScreens/Book_Now/Book_now';
import Details_Book_Now from '../../Screens/CustomerScreens/Book_Now/Details_Book_Now';
import Book_Now_Buy from '../../Screens/CustomerScreens/Book_Now/Book_Now_Buy';
import Proceed_book_now from '../../Screens/CustomerScreens/Book_Now/Proceed_book_now';
import Invoice from '../../Screens/CustomerScreens/Book_Now/Invoice';
import Pay_Now from '../../Screens/CustomerScreens/Book_Now/Pay_Now';
import Pay_Now_Checkout from '../../Screens/CustomerScreens/Book_Now/Pay_Now_Checkout';

const Stack = createStackNavigator();

const Book_NowStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="Book_now"
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}>
      <Stack.Screen name="Book_now" component={Book_now} />
      <Stack.Screen name="Details_Book_Now" component={Details_Book_Now} />
      <Stack.Screen name="Book_Now_Buy" component={Book_Now_Buy} />
      <Stack.Screen name="Proceed_book_now" component={Proceed_book_now} />
      <Stack.Screen name="Invoice" component={Invoice} />
      <Stack.Screen name="Pay_Now" component={Pay_Now} />

      <Stack.Screen name="Pay_Now_Checkout" component={Pay_Now_Checkout} />
    </Stack.Navigator>
  );
};

export default Book_NowStack;
