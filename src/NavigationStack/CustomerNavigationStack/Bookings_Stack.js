// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Bookings from '../../Screens/CustomerScreens/My_Bookings/Bookings';
import BookingDetails from '../../Components/Bookings/BookingDetails';

const Stack = createStackNavigator();

const Bookings_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Bookings'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Bookings" component={Bookings} />
      <Stack.Screen name='BookingDetails' component={BookingDetails}/>
    </Stack.Navigator>
  );
};

    export default Bookings_Stack;