// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Contact_Us from '../../Screens/CustomerScreens/Contact_us/Contact_Us';


const Stack = createStackNavigator();

const Contact_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Contact'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Contact" component={Contact_Us} />
    </Stack.Navigator>
  );
};

            export default Contact_Stack;