// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Personal_Area from '../../Screens/CustomerScreens/Personal_Area/Personal_Area';
import Track_Orders_Stack from './Track_Orders_Stack';
import Personal_Profile_Info from '../../Screens/CustomerScreens/Personal_Area/Personal_Profile_Info';
import Edit_Personal_Profile_Info from '../../Screens/CustomerScreens/Personal_Area/Edit_Personal_Profile_Info';
import DeliveryAddress from '../../Screens/CustomerScreens/Personal_Area/DeliveryAddress';
import Add_New_Addres from '../../Screens/CustomerScreens/Personal_Area/Add_New_Addres';

const Stack = createStackNavigator();

const Personal_Area_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Personal_Area'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

    <Stack.Screen name="Personal_Area" component={Personal_Area} />
    <Stack.Screen name='Track_Orders_Stack' component={Track_Orders_Stack}/>
    <Stack.Screen name='Personal_Profile_Info' component={Personal_Profile_Info}/>
    <Stack.Screen name='Edit_Personal_Profile_Info' component={Edit_Personal_Profile_Info}/>
    <Stack.Screen name='DeliveryAddress' component={DeliveryAddress}/>
    <Stack.Screen name='Add_New_Addres' component={Add_New_Addres}/>

    </Stack.Navigator>
  );
};

export default Personal_Area_Stack;