// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Gift_Card from '../../Screens/CustomerScreens/Gift_Cards/Gift_Card';
import View_Customer_Gift_Card from '../../Screens/CustomerScreens/Gift_Cards/View_Customer_Gift_Card';
const Stack = createStackNavigator();

const Gift_Card_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Gift_Card'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Gift_Card" component={Gift_Card} />
      <Stack.Screen name="View_Customer_Gift_Card" component={View_Customer_Gift_Card} />
    </Stack.Navigator>
  );
};

    export default Gift_Card_Stack;