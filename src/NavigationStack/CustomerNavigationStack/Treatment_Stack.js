// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Treatment from '../../Screens/CustomerScreens/Treatments/Treatment';
import Treatment_Purchased from '../../Screens/CustomerScreens/Treatments/Treatment_Purchased';
const Stack = createStackNavigator();

const Book_NowStack = () => {
  return (
    <Stack.Navigator initialRouteName='Treatment'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Treatment" component={Treatment} />
      <Stack.Screen name="Treatment_Purchased" component={Treatment_Purchased} />
    </Stack.Navigator>
  );
};

export default Book_NowStack;