// src/Navigations/CustomerStack.js
import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useNavigation } from '@react-navigation/native';
import Find_Products from '../../Screens/CustomerScreens/Products/Find_Products';
import Find_Products_Details from '../../Screens/CustomerScreens/Products/Find_Products_Details';
import Find_Products_filter from '../../Screens/CustomerScreens/Products/Find_Products_filter';
import Add_To_Cart_Customer from '../../Screens/CustomerScreens/Products/Add_To_Cart_Customer';
import Main_Add_Cart from '../../Screens/CustomerScreens/Products/Main_Add_Cart';
import Order_Success from '../../Screens/CustomerScreens/Products/Order_Success';
import Track_Orders_Stack from './Track_Orders_Stack';
import Add_New_Addres from '../../Screens/CustomerScreens/Personal_Area/Add_New_Addres';
const Stack = createStackNavigator();

const Customer_Product_Stack = () => {
  // const navigation = useNavigation();

  // useEffect(() => {
  //   const unsubscribe = navigation.addListener('focus', () => {
  //     // Reset to Products screen when the stack is focused from drawer
  //     navigation.reset({
  //       index: 0,
  //       routes: [{ name: 'Products' }],
  //     });
  //   });

  //   return unsubscribe;
  // }, [navigation]);

  return (
    <Stack.Navigator initialRouteName='Products'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Products" component={Find_Products} />
      <Stack.Screen name="Products_Details" component={Find_Products_Details} />
      <Stack.Screen name="Products_Filter" component={Find_Products_filter} />
      <Stack.Screen name="Add_To_Cart_Customer" component={Add_To_Cart_Customer} />
      <Stack.Screen name="Main_Add_Cart" component={Main_Add_Cart} />
      <Stack.Screen name="OrderSuccess" component={Order_Success} />
      <Stack.Screen name="Track_Orders_Stack" component={Track_Orders_Stack} />
      <Stack.Screen name='Add_New_Addres' component={Add_New_Addres}/>



    </Stack.Navigator>
  );
};

export default Customer_Product_Stack;