// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Customer_Settings from '../../Screens/CustomerScreens/Settings/Customer_Settings';
import Personal_Profile_Info from '../../Screens/CustomerScreens/Personal_Area/Personal_Profile_Info';
import Edit_Personal_Profile_Info from '../../Screens/CustomerScreens/Personal_Area/Edit_Personal_Profile_Info';
const Stack = createStackNavigator();

const Settings_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Settings'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

      <Stack.Screen name="Settings" component={Customer_Settings} />
      <Stack.Screen name="Personal_Profile_Info" component={Personal_Profile_Info} />
      <Stack.Screen name="Edit_Personal_Profile_Info" component={Edit_Personal_Profile_Info} />
    </Stack.Navigator>
  );
};

export default Settings_Stack;