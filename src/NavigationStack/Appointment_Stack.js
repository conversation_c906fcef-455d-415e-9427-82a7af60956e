// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Admin_Appointment from '../Screens/Admin_Appointment/Admin_Appointment';
import Barber_Home_Stack from './Barber_Navigation_Stack/Barber_Home_Stack';


const Stack = createStackNavigator();

const Appointment_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='Admin_Appointment'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >
      <Stack.Screen name="Admin_Appointment" component={Admin_Appointment} />
      <Stack.Screen name="Barber_Home_Stack" component={Barber_Home_Stack} />


    

    </Stack.Navigator>
  );
};

export default Appointment_Stack;