// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Barber_Settings from '../../Screens/Barber_Screens/Barber_Settings/Barber_Settings';
import Barber_Profile_Info from '../../Screens/Barber_Screens/Barber_Settings/Barber_Profile_info';
import Barber_Edit_Profile_info from '../../Screens/Barber_Screens/Barber_Settings/Barber_Edit_Profile_info';
import Edit_Brber_Schedule from '../../Screens/Barber_Screens/Barber_Settings/View_Ratings_Brber_Details';
import View_Ratings_Brber_Details from '../../Screens/Barber_Screens/Barber_Settings/View_Ratings_Brber_Details';
import Manage_Barber_Schedule from '../../Screens/Barber_Screens/Barber_Settings/Manage_Barber_Schedule';
const Stack = createStackNavigator();

const Barber_Settings_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='BarberSettings'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

    <Stack.Screen name="BarberSettings" component={Barber_Settings}/>
    <Stack.Screen name='Barber_Profile_Info' component={Barber_Profile_Info}/>
    <Stack.Screen name='Barber_Edit_Profile_info' component={Barber_Edit_Profile_info}/>
    <Stack.Screen name='Manage_Barber_Schedule' component={Manage_Barber_Schedule}/>
    <Stack.Screen name='View_Ratings_Brber_Details' component={View_Ratings_Brber_Details}/>


    </Stack.Navigator>
  );
};

export default Barber_Settings_Stack;