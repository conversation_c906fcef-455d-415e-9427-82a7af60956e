// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import BarberHome from '../../Screens/Barber_Screens/Barber_Home/BarberHome';
import Barber_Pendings_Jobs from '../../Screens/Barber_Screens/Barber_Home/Barber_Pendings_Jobs';
const Stack = createStackNavigator();

// const Barber_Home_Stack = () => {
//   return (
//     <Stack.Navigator initialRouteName='BarberHome'
//       screenOptions={{
//         headerShown: false, // Hide the header for the stack navigator
//       }}
//     >

//     <Stack.Screen name="BarberHome" component={BarberHome}/>
//     <Stack.Screen name='Barber_Pendings_Jobs' component={Barber_Pendings_Jobs}/>


//     </Stack.Navigator>
//   );
// };

const Barber_Home_Stack = () => {
  return (
    <Stack.Navigator initialRouteName='BarberHome'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >
      <Stack.Screen name="BarberHome" component={BarberHome} />
      <Stack.Screen name='Barber_Pendings_Jobs' component={Barber_Pendings_Jobs} />
    </Stack.Navigator>
  );
};

export default Barber_Home_Stack;


