// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import BarberHome from '../../Screens/Barber_Screens/Barber_Home/BarberHome';
import Contact_Us from '../../Screens/CustomerScreens/Contact_us/Contact_Us';
import Barber_Contact_Screen from '../../Screens/Barber_Screens/Contact_Us/Barber_Contact_Screen';
const Stack = createStackNavigator();

const Barber_Contact_Us = () => {
  return (
    <Stack.Navigator initialRouteName='Barber_Contact_Screen'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >

    <Stack.Screen name="Barber_Contact_Screen" component={Barber_Contact_Screen}/>


    </Stack.Navigator>
  );
};

export default Barber_Contact_Us;