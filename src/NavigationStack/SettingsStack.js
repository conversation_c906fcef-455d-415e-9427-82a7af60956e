// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Settings from '../Screens/Settings';
import Personal_Info from '../Screens/Settings/Personal_Info';
import Edit_Profilr from '../Screens/Settings/Edit_Profilr';
import Orders from '../Screens/Settings/Orders';
import Track_now from '../Screens/Settings/Orders/Track_now';
import Order_Details from '../Screens/Settings/Orders/Order_Details';
import SalonRatings from '../Screens/Settings/SalonsRatings/SalonRatings';
import EditProfileForm from '../Components/Settings_Components/EditProfileForm';
const Stack = createStackNavigator();

const SettingsStack = () => {
  return (
    <Stack.Navigator initialRouteName='Settings'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >
      <Stack.Screen name="Settings" component={Settings} />
      <Stack.Screen name="Personal_Info" component={Personal_Info} />
      <Stack.Screen name="Edit_Profilr" component={Edit_Profilr} />
      <Stack.Screen name="Orders" component={Orders} />
      <Stack.Screen name="Track_now" component={Track_now} />
      <Stack.Screen name="Order_Details" component={Order_Details} />
      <Stack.Screen name="SalonRatings" component={SalonRatings} />
      <Stack.Screen name='EditProfileForm' component={EditProfileForm}/>

    </Stack.Navigator>
  );
};

export default SettingsStack;