// src/Navigations/CustomerStack.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Home from '../Screens/Home';

const Stack = createStackNavigator();

const HomeStack = () => {
  return (
    <Stack.Navigator initialRouteName='Home'
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}
    >
      <Stack.Screen name="Home" component={Home} />

    

    </Stack.Navigator>
  );
};

export default HomeStack;