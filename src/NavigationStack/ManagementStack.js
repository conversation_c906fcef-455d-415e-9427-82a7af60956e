// src/Navigations/CustomerStack.js
import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import Management from '../Screens/Management';
import Salons from '../Screens/Management/Salons';
import Barber from '../Screens/Management/Barber';
import Products from '../Screens/Management/Products';
import GiftCards from '../Screens/Management/GiftCards';
import Add_Salons from '../Screens/Management/Salons/Add_Salons';
import Add_Salons_Details from '../Screens/Management/Salons/Add_Salons_Details';
import Service from '../Screens/Management/Service';
import Services_tab from '../Screens/Management/Services/Services_tab';
import Category_Tab from '../Screens/Management/Services/Category_Tab';
import Add_Service from '../Screens/Management/Services/Add_Service';
import Add_Services_Details from '../Screens/Management/Services/Add_Services_Details';
import AddNewBarber from '../Components/Barber/AddNewBarber';
import Add_Product from '../Screens/Management/Products/Add_Product';
import Edit_Product from '../Screens/Management/Products/Edit_Product';
import Products_Details from '../Screens/Management/Products/Products_Details';
import ImageUploadcard from '../Components/Barber/ImageUploadcard';
import BarberDetails from '../Components/Barber/BarberDetails';
import View_Product_Details from '../Screens/Management/Products/View_Product_Details';
import Product_Filters from '../Screens/Management/Products/Product_Filters';
import Add_Gift_Card from '../Screens/Management/Gift_Card/Add_Gift_Card';
import View_Gift_Card from '../Screens/Management/Gift_Card/View_Gift_Card';
import View_Salons_Details from '../Screens/Management/Salons/View_Salons_Details';
import Barber_Details from '../Components/Barber/Barber_Details';
import Edit_Gift_Card from '../Screens/Management/Gift_Card/Edit_Gift_Card';
import View_Ratings_Brber_Details from '../Screens/Barber_Screens/Barber_Settings/View_Ratings_Brber_Details';
const Stack = createStackNavigator();

const ManagementStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="Management"
      screenOptions={{
        headerShown: false, // Hide the header for the stack navigator
      }}>
      <Stack.Screen name="Management" component={Management} />
      <Stack.Screen name="Salons" component={Salons} />
      <Stack.Screen name="Service" component={Service} />
      <Stack.Screen name="Barber" component={Barber} />
      <Stack.Screen name="Products" component={Products} />
      <Stack.Screen name="GiftCards" component={GiftCards} />
      <Stack.Screen name="Add Salons" component={Add_Salons} />
      <Stack.Screen name="Add Salons Details" component={Add_Salons_Details} />
      <Stack.Screen name="Services" component={Services_tab} />
      <Stack.Screen name="Category" component={Category_Tab} />
      <Stack.Screen name="Add Service" component={Add_Service} />
      <Stack.Screen
        name="Add Service Details"
        component={Add_Services_Details}
      />
      <Stack.Screen name="AddnewBarber" component={AddNewBarber} />
      <Stack.Screen name="Add_Product" component={Add_Product} />
      <Stack.Screen name="Edit_Product" component={Edit_Product} />
      <Stack.Screen name="Products_Details" component={Products_Details} />
      <Stack.Screen
        name="View_Product_Details"
        component={View_Product_Details}
      />
      <Stack.Screen name="Product Filters" component={Product_Filters} />
      <Stack.Screen name="Add_Gift_Card" component={Add_Gift_Card} />
      <Stack.Screen name="View_Gift_Card" component={View_Gift_Card} />
      <Stack.Screen name="ImageUploadcard" component={ImageUploadcard} />
      <Stack.Screen name="BarberDetails" component={BarberDetails} />
      <Stack.Screen
        name="View_Salons_Details"
        component={View_Salons_Details}
      />
      <Stack.Screen name="Barber_Details" component={Barber_Details} />
      <Stack.Screen name="Edit_Gift_Card" component={Edit_Gift_Card} />
    <Stack.Screen name='View_Ratings_Brber_Details' component={View_Ratings_Brber_Details}/>
      
    </Stack.Navigator>
  );
};

export default ManagementStack;
