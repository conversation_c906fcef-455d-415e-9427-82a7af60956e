import React from 'react';
import { Text } from 'react-native';
import Fonts from './Fonts';
import { wp } from './Responsiveness';
import { handleMargin, handlePadding } from './theme';
import { colors } from './Colors';

const ResponsiveText = ({
  children,
  color,
  weight,
  size,
  fontFamily,
  margin,
  top,
  position,
  padding,
  flex,
  numberOfLines,
  style,
  textAlign,
  cutText,
  maxWidth,
  backgroundColor,
  borderWidth,
  borderRightWidth,
  borderLeftWidth,
  justifyContent,
  flexShrink,
  fontStyle,
  lineHeight,
  marginBottom,
  ...props
}) => {
  const getFontFamily = () => {
    if (!fontFamily) return 'Poppins-Regular';
    return Fonts[fontFamily] || 'Poppins-Regular';
  };

  return (
    <Text
      {...props}
      numberOfLines={numberOfLines}
      style={[
        { ...styles.text },
        style,
        maxWidth && { maxWidth: maxWidth },
        size && { fontSize: isNaN(size) ? wp(3) : wp(size) },
        margin && handleMargin(margin),
        padding && handlePadding(padding),
        position && { alignSelf: position },
        textAlign && { textAlign: textAlign },
        justifyContent && { justifyContent: justifyContent },
        cutText && { textDecorationLine: 'line-through' },
        { top: top },
        { flexShrink: flexShrink },
        { fontWeight: weight },
        { flex: flex },
        { color: color ? color : colors.black },
        { backgroundColor: backgroundColor },
        { fontFamily: getFontFamily() },
        { borderWidth: borderWidth },
        { borderRightWidth: borderRightWidth },
        { borderLeftWidth: borderLeftWidth },
        fontStyle && { fontStyle: fontStyle },
        lineHeight && { lineHeight: wp(lineHeight) },
        marginBottom && { marginBottom: wp(marginBottom) },
      ]}
    >
      {children}
    </Text>
  );
};

const styles = {
  text: {
    fontFamily: 'Poppins-Regular',
    fontSize: wp(3.5),
  },
};

export default ResponsiveText;
