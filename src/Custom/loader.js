import React, {useEffect, version} from 'react';
import {
  StyleSheet,
  Text,
  View,
  StatusBar,
  Image,
  ImageBackground,
  ScrollView,
} from 'react-native';
import {Swing} from 'react-native-animated-spinkit';
import { colors } from './Colors';
import ResponsiveText from './RnText';
export default function Loader(props) {
  return (
    <View
      style={{
        position: 'absolute',
        justifyContent: 'center',
        alignItems: 'center',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        backgroundColor: props.backgroundColor
          ? props.backgroundColor
          : 'rgba(65, 65, 65, 0.4)',
        flex: 1,
      }}>
      <Swing
        size={80}
        color={props.loaderColor ? props.loaderColor : colors.Light_theme_maincolour}
      />
      {/* <ResponsiveText color={colors.DarkBlue1}>Loading....</ResponsiveText> */}
    </View>
  );
}
