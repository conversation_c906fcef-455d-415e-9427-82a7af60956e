// Auth assets 
const logo= require('../Assets/Images/Barber/Logo.png');
const BackImage= require('../Assets/Images/Barber/BackImage.png');
const google= require('../Assets/Images/Barber/google.png');
const facebook= require('../Assets/Images/Barber/facebook.png');
const apple= require('../Assets/Images/Barber/apple.png');
const calendar= require('../Assets/Images/Barber/calendar.png');
const home= require('../Assets/Images/Barber/home.png');
const management= require('../Assets/Images/Barber/management.png');
const logout= require('../Assets/Images/Barber/logout.png');
const settings= require('../Assets/Images/Barber/settings.png');
const dark_image= require('../Assets/Images/Barber/dark_image.png');
const eye= require('../Assets/Images/Barber/eye.png');
const eyeOff= require('../Assets/Images/Barber/eyeoff.png');
//Home Screen Assets
const bars= require('../Assets/Images/Barber/bars.png');
const bell= require('../Assets/Images/Barber/bell.png');
const pending= require('../Assets/Images/Barber/pending.png');
const earnings= require('../Assets/Images/Barber/earnings.png');
const barbers= require('../Assets/Images/Barber/barber.png');
const left= require('../Assets/Images/Barber/left.png');
const goback= require('../Assets/Images/Barber/Back.png');
const search= require('../Assets/Images/Barber/search.png');
const watch= require('../Assets/Images/Barber/watch.png');
const location= require('../Assets/Images/Barber/location.png');
const back1= require('../Assets/Images/Barber/back1.jpg');
const back2= require('../Assets/Images/Barber/back2.jpg');
const back3= require('../Assets/Images/Barber/back3.jpg');
const back4= require('../Assets/Images/Barber/back4.png');
const back5= require('../Assets/Images/Barber/back5.jpg');
const add_service= require('../Assets/Images/Barber/add_service.png');
const tick= require('../Assets/Images/Barber/tick.png');
const cross= require('../Assets/Images/Barber/remove.png');
const eye2= require('../Assets/Images/Barber/eye2.png');
const award= require('../Assets/Images/Barber/award.png');
const choose_file= require('../Assets/Images/Barber/choose_file.png');
const upload= require('../Assets/Images/Barber/choose_file.png');
const delete_icon= require('../Assets/Images/Barber/delete.png');
const arrow_up= require('../Assets/Images/Barber/arrow_up.png');
const arrow_down= require('../Assets/Images/Barber/arrow_down.png');
const down= require('../Assets/Images/Barber/down.png');
const back6= require('../Assets/Images/Barber/back6.jpg');
const back7= require('../Assets/Images/Barber/back7.png');
const back8= require('../Assets/Images/Barber/back8.png');
const back9= require('../Assets/Images/Barber/back9.png');
const product1= require('../Assets/Images/Barber/product1.png');
const product2= require('../Assets/Images/Barber/product2.png');
const product3= require('../Assets/Images/Barber/product3.png');
const product4= require('../Assets/Images/Barber/product4.png');
const detailbarber=require('../Assets/Images/Barber/brbr.png')
const stars=require('../Assets/Images/Barber/stars.png')
const childcut=require('../Assets/Images/Barber/ccc.png')
const childcut1=require('../Assets/Images/Barber/ccc1.png')
const childcut2=require('../Assets/Images/Barber/ccc2.png')
const childcut3=require('../Assets/Images/Barber/ccc3.png')
const edit=require('../Assets/Images/Barber/edit.png')
const up=require('../Assets/Images/Barber/up.png')
const filter=require('../Assets/Images/Barber/filter.png')
const card=require('../Assets/Images/Barber/card.png')
const orders=require('../Assets/Images/Barber/orders.png')
const ratings=require('../Assets/Images/Barber/salons_ratings.png')
const notifications=require('../Assets/Images/Barber/bell.png')
const help=require('../Assets/Images/Barber/help.png')
const about=require('../Assets/Images/Barber/about.png')
const profile=require('../Assets/Images/Barber/profile.png')
const profile_eddit=require('../Assets/Images/Barber/profile_eddit.png')
const theme=require('../Assets/Images/Barber/theme.png')
const user=require('../Assets/Images/Barber/user.png')
const phone=require('../Assets/Images/Barber/phone.png')
const email=require('../Assets/Images/Barber/email.png')
const order_user=require('../Assets/Images/Barber/order_user.png')
const order_location=require('../Assets/Images/Barber/order_location.png')
const order_call=require('../Assets/Images/Barber/order_call.png')
const order_item=require('../Assets/Images/Barber/order_item.png')
const language=require('../Assets/Images/Barber/language.png')

// Customer sections assets:
const book_now=require('../Assets/Images/Barber/Book_now.png')
const Treatments=require('../Assets/Images/Barber/Treatments.png')
const buy_product=require('../Assets/Images/Barber/buy_products.png')
const Gift_card=require('../Assets/Images/Barber/Gift_card.png')
const personal_area=require('../Assets/Images/Barber/personal_area.png')
const my_bookings=require('../Assets/Images/Barber/my_bookings.png')
const track_orders=require('../Assets/Images/Barber/track_orders.png')
const Contact_us=require('../Assets/Images/Barber/Contact_us.png')
const drawer_settings=require('../Assets/Images/Barber/drawer_settings.png')
const barber_icon=require('../Assets/Images/Barber/man.png')
const Add_Cart=require('../Assets/Images/Barber/Add_Cart.png')
const plus=require('../Assets/Images/Barber/plus.png')
const minus=require('../Assets/Images/Barber/minus.png')
const treatment1=require('../Assets/Images/Barber/treatment1.png')
const treatment2=require('../Assets/Images/Barber/treatment2.png')
const treatment3=require('../Assets/Images/Barber/treatment3.png')
const heart=require('../Assets/Images/Barber/heart.png')
const filled_heart=require('../Assets/Images/Barber/filled_heart.png')
const white_tick=require('../Assets/Images/Barber/white_tick.png')
const Scissors=require('../Assets/Images/Barber/Scissors.png')
const coupon=require('../Assets/Images/Barber/coupoun.png')
const wallet=require('../Assets/Images/Barber/wallet.png')
const download=require('../Assets/Images/Barber/DOWNLOAD.png')
const min=require('../Assets/Images/Barber/min.png')
const info=require('../Assets/Images/Barber/info.png')
const customerimage=require('../Assets/Images/Barber/profile1.png')
const loc=require('../Assets/Images/Barber/loc.png')
const user2=require("../Assets/Images/Barber/users.png")
const call=require("../Assets/Images/Barber/call.png")
const additionalnotes=require('../Assets/Images/Barber/ADN.png')
const track=require('../Assets/Images/Barber/checkout.png')
const delivery=require("../Assets/Images/Barber/delivery.png")
const payment=require("../Assets/Images/Barber/credit-card.png")
const Home1=require("../Assets/Images/Barber/homee.png")
const office=require('../Assets/Images/Barber/Office.png')
const editt=require('../Assets/Images/Barber/editt.png')
const add=require('../Assets/Images/Barber/ad.png')
const backk=require("../Assets/Images/Barber/backk.png")
const Vratings=require("../Assets/Images/Barber/rating.png")
const male=require("../Assets/Images/Barber/male.png")
const female=require("../Assets/Images/Barber/female.png")
const Right=require("../Assets/Images/Barber/Right.png")
const save=require("../Assets/Images/Barber/save.png")
const attachment=require('../Assets/Images/Barber/attach.png')
const repeat=require('../Assets/Images/Barber/repeat.png')
const tag=require('../Assets/Images/Barber/tag.png')
const dots=require('../Assets/Images/Barber/dots.png')
const calendar_insurance=require('../Assets/Images/Barber/calendar_insurance.png')
const calendar_user=require('../Assets/Images/Barber/calendar_user.png')









































export const globalpath = {
    logo,
    BackImage,
    google,
    facebook,
    apple,
    calendar,
    home,
    logout,
    settings,
    management,
    dark_image,
    eye,
    eyeOff,
    bars,
    bell,
    pending,
    earnings,
    barbers,
    left,
    goback,
    search,
    watch,
    location,
    back1,
    back2,
    back3,
    back4,
    back5,
    add_service,
    tick,
    cross,
    eye2,
    award,
    choose_file,
    upload,
    delete_icon,
    arrow_up,
    arrow_down,
    down,
    back6,
    back7,
    back8,
    back9,
    product1,
    product2,
    product3,
    product4,
    detailbarber,
    stars,
    childcut,
    childcut1,
    childcut2,
    childcut3,
    edit,
    up,
    filter,
    card,
    orders,
    ratings,
    notifications,
    help,
    about,
    profile,
    profile_eddit,
    theme,
    user,
    phone,
    email,
    order_user,
    order_location,
    order_call,
    order_item,
    language,
    book_now,
    Treatments,
    buy_product,
    Gift_card,
    personal_area,
    my_bookings,
    track_orders,
    Contact_us,
    drawer_settings,
    barber_icon,
    Add_Cart,
    plus,
    minus,
    treatment1,
    treatment2,
    treatment3,
    customerimage,
    loc,
    heart,
    filled_heart,
    user2,
    call,
    additionalnotes,
    track,
    delivery,
    payment,
    Home1,
    office,
    editt,  
    add,
    backk,
    white_tick,
    Scissors,
    coupon,
    wallet,
    Vratings,
    download,
    male,
    female, 
    Right,
    min,
    info,
    save,
    attachment,
    repeat,
    tag,
    dots,
    calendar_insurance,
    calendar_user

};