const Fonts = {
    // Semantic Font Styles
    Main_Heading: 'Poppins-Bold',
    Sub_Heading: 'Gilroy-Regular',
    Button: '<PERSON>roy-Bold',
    Accordion: '<PERSON>roy-Medium',
    Muted_Text: 'Sen-Regular',
    Form_Label: 'PlusJakartaSans-Regular',
    Body_Text: 'Poppins-Regular',

    // Individual Font Families
    // Poppins Font Family
    PoppinsRegular: 'Poppins-Regular',
    PoppinsBold: 'Poppins-Bold',
    PoppinsSemiBold: 'Poppins-SemiBold',
    PoppinsLight: 'Poppins-Light',
    PoppinsLightItalic: 'Poppins-LightItalic',
    PoppinsThin: 'Poppins-Thin',
    PoppinsMedium: 'Poppins-Medium',
    PoppinsBlack: 'Poppins-Black',
    PoppinsBlackItalic: 'Poppins-BlackItalic',
    PoppinsBoldItalic: 'Poppins-BoldItalic',
    PoppinsExtraBold: 'Poppins-ExtraBold',
    PoppinsExtraBoldItalic: 'Poppins-ExtraBoldItalic',
    PoppinsExtraLight: 'Poppins-ExtraLight',
    PoppinsExtraLightItalic: 'Poppins-ExtraLightItalic',
    PoppinsItalic: 'Poppins-Italic',
    PoppinsMediumItalic: 'Poppins-MediumItalic',
    PoppinsSemiBoldItalic: 'Poppins-SemiBoldItalic',
    PoppinsThinItalic: 'Poppins-ThinItalic',
    
    // Sen Font Family
    SenBold: 'Sen-Bold',
    SenExtraBold: 'Sen-ExtraBold',
    SenMedium: 'Sen-Medium',
    SenRegular: 'Sen-Regular',
    SenSemiBold: 'Sen-SemiBold',
    SenVariableFont: 'Sen-VariableFont_wght',
    
    // Gilroy Font Family
    GilroyBold: 'Gilroy-Bold',
    GilroyHeavy: 'Gilroy-Heavy',
    GilroyLight: 'Gilroy-Light',
    GilroyMedium: 'Gilroy-Medium',
    GilroyRegular: 'Gilroy-Regular',
    
    // Plus Jakarta Sans Font Family
    PlusJakartaSansRegular: 'PlusJakartaSans-Regular',
    PlusJakartaSansBold: 'PlusJakartaSans-Bold',
    PlusJakartaSansMedium: 'PlusJakartaSans-Medium',
    
    // Playfair Display
    PlayfairDisplay: 'PlayfairDisplay-Black',
};

export default Fonts;
  