import { globalpath } from './globalpath';

export const salonData = [
  {
    id: 1,
    title: "Luxury Hair Salon",
    location: "123 Beauty Street, City",
    hours: "10:00 - 20:00",
    image: globalpath.back1,
  },
  {
    id: 2,
    title: "Modern Style Barbershop",
    location: "456 Fashion Ave, Town",
    hours: "09:00 - 19:00",
    image: globalpath.back2,
  },
  {
    id: 3,
    title: "Classic Cuts & Styles",
    location: "789 Main St, Downtown",
    hours: "08:00 - 18:00",
    image: globalpath.back3,
  }
];

export const bookNowData = [
  {
    id: 1,
    name: "Classic Barber Shop",
    address_city: "123 Main St, New York",
    hours: "9:00 AM - 7:00 PM",
    shop_image: globalpath.back1,
  },
  {
    id: 2,
    name: "Modern Cuts",
    address_city: "456 Park Ave, Los Angeles",
    hours: "8:00 AM - 8:00 PM",
    shop_image: globalpath.back2,
  },
  {
    id: 3,
    name: "Elite Barbers",
    address_city: "789 Broadway, Chicago",
    hours: "10:00 AM - 6:00 PM",
    shop_image: globalpath.back3,
  },
];

export const book_now_details_mock = [
  {
    id: 1,
    title: "Classic Haircut",
    description: "Professional haircut with modern styling techniques",
    duration: "30 min",
    amount: "25",
    service_image: globalpath.back1
  },
  {
    id: 2,
    title: "Beard Trim",
    description: "Precision beard trimming and shaping",
    duration: "20 min",
    amount: "15",
    service_image: globalpath.back2
  },
  {
    id: 3,
    title: "Hair & Beard Combo",
    description: "Complete grooming package including haircut and beard trim",
    duration: "45 min",
    amount: "35",
    service_image: globalpath.back3
  }
];

export const customerOrdersMock = [
  {
    id: '1',
    title: 'Classic Haircut',
    amount: 25,
    items: 1,
    orderNumber: '12345',
    image: globalpath.back1,
    status: 'pending',
    date: '28 May 2023',
    shipping_details: '123 Main St, New York, NY 10001',
    services: [
      {
        id: '1',
        name: 'Classic Haircut',
        quantity: 1,
        price: 25
      }
    ]
  },
  {
    id: '2',
    title: 'Beard Trim',
    amount: 15,
    items: 1,
    orderNumber: '12346',
    image: globalpath.back2,
    status: 'processing',
    date: '27 May 2023',
    shipping_details: '456 Park Ave, Los Angeles, CA 90001',
    services: [
      {
        id: '2',
        name: 'Beard Trim',
        quantity: 1,
        price: 15
      }
    ]
  }
];

export const customerHistoryMock = [
  {
    id: '1',
    title: 'Hair & Beard Combo',
    amount: 35,
    items: 2,
    orderNumber: '12344',
    date: '26 May 2023',
    status: 'completed',
    image: globalpath.back3,
    services: [
      {
        id: '1',
        name: 'Classic Haircut',
        quantity: 1,
        price: 25
      },
      {
        id: '2',
        name: 'Beard Trim',
        quantity: 1,
        price: 15
      }
    ]
  },
  {
    id: '2',
    title: 'Classic Haircut',
    amount: 25,
    items: 1,
    orderNumber: '12343',
    date: '25 May 2023',
    status: 'cancelled',
    image: globalpath.back1,
    services: [
      {
        id: '1',
        name: 'Classic Haircut',
        quantity: 1,
        price: 25
      }
    ]
  }
];

export const Gift_Mock = [
  {
    id: 1,
    title: "Digital Gift Card",
    description: "Perfect gift for any occasion",
    amount: "50",
    image: globalpath.card
  },
  {
    id: 2,
    title: "Premium Gift Card",
    description: "Treat someone special",
    amount: "100",
    image: globalpath.card
  },
  {
    id: 3,
    title: "Deluxe Gift Card",
    description: "The ultimate grooming experience",
    amount: "200",
    image: globalpath.card
  }
];

export const Treatment_Mock = [
  {
    id: 1,
    title: "Hair Treatment",
    description: "Deep conditioning and repair treatment for damaged hair",
    amount: "50",
    duration: "30 min",
    image: globalpath.treatment1
  },
  {
    id: 2,
    title: "Scalp Treatment",
    description: "Specialized treatment for scalp health and dandruff control",
    amount: "40",
    duration: "45 min",
    image: globalpath.treatment2
  },
  {
    id: 3,
    title: "Keratin Treatment",
    description: "Professional keratin treatment for smooth and shiny hair",
    amount: "80",
    duration: "60 min",
    image: globalpath.treatment3
  }
];

export const barbersData = [
  {
    id: 1,
    name: "John Smith",
    image: globalpath.back1,
    rating: 4.8,
    experience: "5 years",
    specialties: ["Haircut", "Beard Trim"]
  },
  {
    id: 2,
    name: "Mike Johnson",
    image: globalpath.back2,
    rating: 4.9,
    experience: "7 years",
    specialties: ["Haircut", "Hair Coloring"]
  },
  {
    id: 3,
    name: "David Wilson",
    image: globalpath.back3,
    rating: 4.7,
    experience: "4 years",
    specialties: ["Haircut", "Beard Styling"]
  },
  {
    id: 4,
    name: "Alex Brown",
    image: globalpath.back1,
    rating: 4.6,
    experience: "6 years",
    specialties: ["Haircut", "Hair Treatment"]
  },
  {
    id: 5,
    name: "Chris Davis",
    image: globalpath.back2,
    rating: 4.5,
    experience: "3 years",
    specialties: ["Haircut", "Kids Haircut"]
  }
];

const generateTimeSlots = (startHour = 8, endHour = 20) => {
  const slots = [];
  let id = 1;
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += 15) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
      const isBooked = Math.random() < 0.3; // 30% chance of being booked
      const barbers = ['John Doe', 'Mike Smith', 'Sarah Johnson', 'Alex Wong', 'David Brown', 'Emma Wilson'];
      const barber = isBooked ? barbers[Math.floor(Math.random() * barbers.length)] : null;
      
      slots.push({
        id: id++,
        start_time: time,
        is_booked: isBooked,
        barber: barber
      });
    }
  }
  return slots;
};

const generateDateRange = (startDate, endDate) => {
  const slotsData = {};
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    const formattedDate = date.toISOString().split('T')[0].split('-').reverse().join('-');
    
    // Add some dates with no time slots
    // For example, no slots on Sundays and some random dates
    const dayOfWeek = date.getDay();
    const isRandomNoSlotDay = Math.random() < 0.1; // 10% chance of no slots on any day
    
    if (dayOfWeek === 0 || isRandomNoSlotDay) {
      slotsData[formattedDate] = []; // Empty array means no time slots
    } else {
      slotsData[formattedDate] = generateTimeSlots();
    }
  }
  
  return slotsData;
};

export const timeSlotsData = generateDateRange('2025-04-10', '2025-12-31'); 