import { TouchableOpacity, View, StyleSheet } from 'react-native';
import React from 'react';
import { wp } from './Responsiveness';
import { colors } from './Colors';

const CustomCheckbox = ({ value, onValueChange, onTintColor, onCheckColor }) => {
  return (
    <TouchableOpacity
      onPress={() => onValueChange(!value)}
      style={[
        styles.checkbox,
        {
          backgroundColor: value ? onTintColor : 'transparent',
          borderColor: value ? onTintColor : onTintColor,
        },
      ]}
    >
      {value && (
        <View style={[styles.checkmark, { borderColor: onCheckColor }]} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    width: wp(6),
    height: wp(6),
    borderWidth: 2,
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    width: wp(3),
    height: wp(1.5),
    borderLeftWidth: 2,
    borderBottomWidth: 2,
    transform: [{ rotate: '-45deg' }],
    marginTop: -wp(0.5),
  },
});

export default CustomCheckbox; 