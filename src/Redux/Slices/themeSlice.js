import { createSlice } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../../Custom/Colors';

const initialState = {
  themeColor: colors.Light_theme_maincolour,
  selectedTheme: 'light',
  backgroundColor: '#ffffff',
  language: 'en',
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeColor: (state, action) => {
      state.themeColor = action.payload;
      AsyncStorage.setItem('themeColor', action.payload);
    },
    setSelectedTheme: (state, action) => {
      state.selectedTheme = action.payload;
      AsyncStorage.setItem('selectedTheme', action.payload);
    },
    setBackgroundColor: (state, action) => {
      state.backgroundColor = action.payload;
      AsyncStorage.setItem('backgroundColor', action.payload);
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
      AsyncStorage.setItem('language', action.payload);
    },
    loadThemeColor: (state, action) => {
      state.themeColor = action.payload;
    },
    loadBackgroundColor: (state, action) => {
      state.backgroundColor = action.payload;
    },
    loadSelectedTheme: (state, action) => {
      state.selectedTheme = action.payload;
    },
    loadLanguage: (state, action) => {
      state.language = action.payload;
    },
  },
});

export const { 
  setThemeColor, 
  setSelectedTheme, 
  setBackgroundColor,
  setLanguage,
  loadThemeColor,
  loadBackgroundColor,
  loadSelectedTheme,
  loadLanguage
} = themeSlice.actions;

export default themeSlice.reducer; 