import { createSlice, createSelector } from '@reduxjs/toolkit';

const initialState = {
  totalItems: 0,
  cartItems: [],
  totalPrice: 0,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setCartItems: (state, action) => {
      state.totalItems = action.payload;
    },
    setFullCartData: (state, action) => {
      state.cartItems = action.payload.items || [];
      state.totalPrice = action.payload.total_price || 0;
      state.totalItems = action.payload.items?.length || 0;
    },
    updateItemQuantity: (state, action) => {
      const { itemId, quantity, newPrice } = action.payload;
      // this is the new price that is being updated in the cart
      console.log('Updating quantity for item:', itemId, 'New quantity:', quantity, 'New price:', newPrice);
      
      const item = state.cartItems.find(item => item.id === itemId);
      if (item) {
        item.quantity = quantity;
        if (newPrice !== undefined) {
          item.item_price = newPrice;
        }
        // Recalculate total price - using item_price directly as it already includes quantity
        state.totalPrice = state.cartItems.reduce((sum, item) => 
          sum + parseFloat(item.item_price), 0
        );
        console.log('New total price:', state.totalPrice);
      }
    },
    removeCartItem: (state, action) => {
      const itemId = action.payload;
      state.cartItems = state.cartItems.filter(item => item.id !== itemId);
      state.totalItems = state.cartItems.length;
      // Recalculate total price
      state.totalPrice = state.cartItems.reduce((sum, item) => 
        sum + (item.quantity * parseFloat(item.item_price)), 0
      );
    },
    clearCart: (state) => {
      state.cartItems = [];
      state.totalItems = 0;
      state.totalPrice = 0;
    },
  },
});

// Memoized Selectors
export const selectCartState = (state) => state.cart;

export const selectCartData = createSelector(
  [selectCartState],
  (cart) => ({
    items: cart.cartItems,
    total_price: cart.totalPrice
  })
);

export const { 
  setCartItems, 
  setFullCartData, 
  updateItemQuantity, 
  removeCartItem, 
  clearCart 
} = cartSlice.actions;

export default cartSlice.reducer; 