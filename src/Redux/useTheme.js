// useTheme.js
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../Custom/Colors';
import { loadBackgroundColor, loadThemeColor, loadSelectedTheme, loadLanguage } from './Slices/themeSlice';

const useTheme = () => {
  const dispatch = useDispatch();
  const themeColor = useSelector((state) => state.theme.themeColor);
  const backgroundColor = useSelector((state) => state.theme.backgroundColor);
  const selectedTheme = useSelector((state) => state.theme.selectedTheme);

  // Function to determine if the theme is dark
  const isDarkTheme = () => {
    const darkThemes = ['dark'];
    return darkThemes.includes(selectedTheme);
  };

  // Get text color based on theme
  const getTextColor = () => {
    return isDarkTheme() ? colors.white : colors.greyBlack;
  };

  // Get secondary text color based on theme
  const getSecondaryTextColor = () => {
    return isDarkTheme() ? colors.white : colors.greyborder;
  };
  const getborderTextColor = () => {
    return isDarkTheme() ? colors.white : colors.border;
  };
  const getLightGrayBackground = () => {
    return isDarkTheme() ? colors.white : colors.lightGrey1;
  };
  const getBellBackground = () => {
    return isDarkTheme() ? null : colors.lightGrey1;
  };
  const getDarK_mode_LightGrayBackground = () => {
    return isDarkTheme() ? null : colors.lightGrey;
  };
  const getIconColour = () => {
    return isDarkTheme() ? colors.white : colors.iconcolour;
  };
  const getChartColour = () => {
    return isDarkTheme() ? colors.black : colors.white;
  };

  const getDark_Theme = () => {
    return isDarkTheme() ? colors.grey : colors.greyborder;
  };
  const getsky_Theme = () => {
    return isDarkTheme() ? "#212121" : colors.light_sky;
  };
  const get_Font_text_color= () => {
    return isDarkTheme() ? colors.white : "#191A1A";
  };

    const barber_header= () => {
    return isDarkTheme() ?   "#191A1A" :  "#E9ECF2"
  };
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const [savedColor, savedBackgroundColor, savedTheme, savedLanguage] = await Promise.all([
          AsyncStorage.getItem('themeColor'),
          AsyncStorage.getItem('backgroundColor'),
          AsyncStorage.getItem('selectedTheme'),
          AsyncStorage.getItem('language')
        ]);

        if (savedColor) {
          dispatch(loadThemeColor(savedColor));
        }
        if (savedBackgroundColor) {
          dispatch(loadBackgroundColor(savedBackgroundColor));
        }
        if (savedTheme) {
          dispatch(loadSelectedTheme(savedTheme));
        }
        if (savedLanguage) {
          dispatch(loadLanguage(savedLanguage));
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      }
    };
    loadTheme();
  }, [dispatch]);

  return {
    themeColor,
    backgroundColor,
    selectedTheme,
    isDarkTheme,
    getTextColor,
    getSecondaryTextColor,
    getborderTextColor,
    getLightGrayBackground,
    getBellBackground,
    getDarK_mode_LightGrayBackground,
    getIconColour,
    getChartColour,
    getDark_Theme,
    getsky_Theme,
    get_Font_text_color,
    barber_header
  };
};

export default useTheme;