import { SafeAreaView, StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator, Platform,Text } from 'react-native'
import React, { useState } from 'react'
import { colors } from '../Custom/Colors'
import { globalpath } from '../Custom/globalpath'
import { wp, hp } from '../Custom/Responsiveness'
import ResponsiveText from '../Custom/RnText'
import useAppText from '../Custom/AppText'
import PhoneInput from 'react-native-phone-number-input'
import Icon from '../Custom/Icon'
import CustomCheckbox from '../Custom/CustomCheckbox'
import useTheme from '../Redux/useTheme'
import Modal from 'react-native-modal'
import DatePicker from 'react-native-modern-datepicker'
import { parsePhoneNumberFromString } from 'libphonenumber-js';

const Register = ({ navigation }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor ,getDark_Theme} = useTheme();
  const AppText = useAppText();
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [dateOfBirth, setDateOfBirth] = useState('')
  const [isChecked, setIsChecked] = useState(false)
  const [loading, setLoading] = useState(false)
  const [countryCode, setCountryCode] = useState('PK');
  const [error, setError] = useState('');


  const handleRegister = async () => {
    if (!firstName || !lastName || !email || !phoneNumber || !dateOfBirth) {
      alert('Please fill all the fields');
      return;
    }
  
    try {
      setLoading(true);
  
      // ✅ Log each individual value
      console.log('📝 First Name:', firstName);
      console.log('📝 Last Name:', lastName);
      console.log('📧 Email:', email);
      console.log('📞 Phone Number:', phoneNumber);
      console.log('🎂 Date of Birth:', dateOfBirth);
  
      // ✅ Combine into one payload object
      const payload = {
        full_name: firstName + ' ' + lastName,
        email: email,
        phone_number: phoneNumber,
        dob: dateOfBirth,
        "role": "customer",
        "address_city": "",
        "address_postal_code": "",
        "address_country": "",
        "address_state": "",
        "address_street": ""
      };
  
      // ✅ Log the full payload for verification
      console.log('📦 Payload:', payload);
  
      // You can now pass `payload` to your API like:
      // const response = await registerUser(payload);
  
      // For now, maybe navigate:
      navigation.navigate('Set Password', { payload })
      // navigation.navigate('Email Verification', { email });
  
    } catch (error) {
      console.error('❌ Error during registration:', error);
    } finally {
      setLoading(false);
    }
  };
  

  const handleDateChange = (selectedDate) => {
    setDateOfBirth(selectedDate)
    setShowDatePicker(false)
  }

// for number validation:

  const handlePhoneChange = (number) => {
    setPhoneNumber(number);
    
    try {
      const phoneNumberObj = parsePhoneNumberFromString(number, countryCode);
      
      if (phoneNumberObj) {
        if (!phoneNumberObj.isValid()) {
          setError(AppText.INVALID_PHONE_NUMBER);
        } else {
          setError('');
        }
      }
    } catch (e) {
      setError('Invalid phone number format');
    }
  };

  const handleCountryChange = (country) => {
    setCountryCode(country.cca2);
    setError('');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <View style={{flex:1,}}>

     
      <View style={styles.headerContainer}>
        <ResponsiveText weight={'600'} size={7} margin={[hp(4),0,wp(2),wp(4)]} color={getTextColor()}>
          {AppText.REGISTER_TITLE}
        </ResponsiveText>
      </View>

      <View style={styles.formContainer}>
        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.FIRST_NAME_PLACEHOLDER}
            value={firstName}
            onChangeText={setFirstName}
            placeholderTextColor={getborderTextColor()}
          />
        </View>

        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.LAST_NAME_PLACEHOLDER}
            value={lastName}
            onChangeText={setLastName}
            placeholderTextColor={getborderTextColor()}
          />
        </View>

        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.EMAIL_PLACEHOLDER2}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor={getborderTextColor()}
          />
        </View>

        <View style={[styles.phoneinputContainer, { borderColor: getDark_Theme() }]}>
          <PhoneInput
            defaultCode="PK"
            layout="first"
            // onChangeText={(text) => setPhoneNumber(text)}
            onChangeText={handlePhoneChange}
            onChangeCountry={handleCountryChange}
            value={phoneNumber}
            placeholder={AppText.PHONE_PLACEHOLDER}
            placeholderTextColor={getTextColor()}
            containerStyle={styles.phoneInput}
            textContainerStyle={styles.phoneTextContainer}
            textInputStyle={[styles.phoneTextInput, {color: getTextColor()}]}
            codeTextStyle={{color: getTextColor()}}
          />
        </View>
        {error ? <ResponsiveText color={colors.red} margin={[hp(-1.8),0,hp(1.3),0]}>{error}</ResponsiveText> : null}

        <TouchableOpacity 
          style={[styles.inputContainer, { borderColor: getDark_Theme() }]}
          onPress={() => setShowDatePicker(true)}
        >
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.DOB_PLACEHOLDER}
            value={dateOfBirth}
            editable={false}
            placeholderTextColor={getborderTextColor()}
          />
          <Icon
            source={globalpath.calendar}
            size={wp(5)}
          />
        </TouchableOpacity>

        <View style={styles.checkboxContainer}>
          <CustomCheckbox
            value={isChecked}
            onValueChange={setIsChecked}
            onTintColor={colors.Light_theme_maincolour}
            onCheckColor={colors.white}
          />
          <ResponsiveText color={getTextColor()} size={3.5} margin={[hp(1.3),0,0,wp(2)]} >
            {AppText.TERMS_TEXT}
          </ResponsiveText>
        </View>

        {loading ? (
          <View style={styles.registerButton}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
            style={styles.registerButton}
            onPress={handleRegister}
          >
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.REGISTER_BUTTON_TEXT}
            </ResponsiveText>
          </TouchableOpacity>
        )}

        <TouchableOpacity 
          style={styles.loginLink}
          onPress={() => navigation.navigate('Login')}
        >
          <ResponsiveText color={getborderTextColor()} size={4}>
            {AppText.ALREADY_HAVE_ACCOUNT}
          </ResponsiveText>
        </TouchableOpacity>
      </View>

      <Modal
        isVisible={showDatePicker}
        onBackdropPress={() => setShowDatePicker(false)}
        onBackButtonPress={() => setShowDatePicker(false)}
        style={styles.modalStyle}
      >
        <View style={[styles.datePickerContainer, { backgroundColor: backgroundColor }]}>
          <DatePicker
            mode="calendar"
            // maximumDate={new Date().toISOString().split('T')[0]}
            onDateChange={handleDateChange}
            options={{
              textHeaderColor: themeColor,
              mainColor: colors.Light_theme_maincolour,
              textDefaultColor: getTextColor(),
              selectedTextColor: colors.white,
              backgroundColor: backgroundColor,
            }}
          />
          {/* <TouchableOpacity 
            style={styles.closeDatePicker}
            onPress={() => setShowDatePicker(false)}
          >
            <ResponsiveText color={colors.black} size={4}>
              Close
            </ResponsiveText>
          </TouchableOpacity> */}
        </View>
      </Modal>
       </View>
    </SafeAreaView>
  )
}

export default Register

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    // alignItems: 'center',
    marginVertical: wp(3),
  },
  formContainer: {
    width: '90%',
    alignSelf: 'center',
  },
 inputContainer: {
    height:hp(6),
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    paddingHorizontal: Platform.OS === 'android' ? wp(4.7) :null,
    padding: Platform.OS ==='ios' ? wp(4) : null,
    borderWidth: 1,
  },
  phoneinputContainer: {
    flexDirection: 'row',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    borderWidth: 1,
    height: Platform.OS === 'android' ? hp(7) : hp(6),
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    fontSize: wp(4),
  },
  phoneInput: {
    flex: 1,
    height: '100%',
    backgroundColor: 'transparent',
  },
  phoneTextContainer: {
    backgroundColor: 'transparent',
    paddingVertical: 0,
  },
  phoneTextInput: {
    height: '100%',
    fontSize: wp(4),
    padding: 0,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(4),
  },
  termsText: {
    flex: 1,
    marginLeft: wp(2),
  },
  registerButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(3),
  },
  loginLink: {
    alignItems: 'center',
  },
  modalStyle: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  datePickerContainer: {
    backgroundColor: colors.white,
    padding: wp(4),
    borderTopLeftRadius: wp(4),
    borderTopRightRadius: wp(4),
  },
  closeDatePicker: {
    alignItems: 'center',
    padding: wp(4),
  },
})
