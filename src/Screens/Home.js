import { StyleSheet, View, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';
import React, { useState, useEffect } from 'react';
import useTheme from '../Redux/useTheme';
import CustomHeader from '../Components/CustomHeader';
import useAppText from '../Custom/AppText';
import { colors } from '../Custom/Colors';
import { wp, hp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import MainCard from '../Components/MainCard';
import { globalpath } from '../Custom/globalpath';
import { PieChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import DashboardCard from '../Components/dashborad/DashboardCard';
import CustomLineChart from '../Components/dashborad/LineChart';
import CustomBarChart from '../Components/dashborad/BarChart';
import Loader from '../Custom/loader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { get_all_barber } from '../Services/API/Endpoints/Admin/Barber';
import { get_all_giftcard } from '../Services/API/Endpoints/Admin/products';
import { get_all_service } from '../Services/API/Endpoints/Admin/Services';
import { get_all_shops } from '../Services/API/Endpoints/Admin/Salons';


const Home = () => {
  const AppText = useAppText();






    
  const { themeColor,
    getDarK_mode_LightGrayBackground, backgroundColor, getTextColor,getSecondaryTextColor,getLightGrayBackground,getborderTextColor , getDark_Theme} = useTheme();
  const [viewMode, setViewMode] = useState('view'); // 'view' or 'pie'
  const [chartType, setChartType] = useState('line'); // 'line' or 'bar'
  const [timePeriod, setTimePeriod] = useState('7'); // '7', '15', or '30'
  const [loading, setloading] = useState(false);
  const [allBarbers, setAllbabers] = useState([]);
  const [allGiftCard, setAllGiftCard] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [allShops, setAllShops] = useState([]);
  
  const screenWidth = Dimensions.get('window').width;

  // Handle loading when switching view modes
// useEffect(() => {
//     setloading(true);
//     const timer = setTimeout(() => {
//       setloading(false);
//     }, 1000);
//     return () => clearTimeout(timer);
//   }, [viewMode]);

  // Handle loading when switching chart types
  // useEffect(() => {
  //   setloading(true);
  //   const timer = setTimeout(() => {
  //     setloading(false);
  //   }, 1000);
  //   return () => clearTimeout(timer);
  // }, [chartType]);

  // Handle loading when switching time periods
  // useEffect(() => {
  //   setloading(true);
  //   const timer = setTimeout(() => {
  //     setloading(false);
  //   }, 1000);
  //   return () => clearTimeout(timer);
  // }, [timePeriod]);

  useEffect(()=>{
    getUserData();
  },[])


  useEffect(() => {
    fetchAllDashboardData();
  }, []);
  
  const fetchAllDashboardData = async () => {
    try {
      setloading(true);
  
      const [
        barbersRes,
        giftCardRes,
        serviceRes,
        shopsRes
      ] = await Promise.all([
        get_all_barber(),
        get_all_giftcard(),
        get_all_service(),
        get_all_shops()
      ]);
  
      setAllbabers(barbersRes.length);
      setAllGiftCard(giftCardRes.length);
      setAllServices(serviceRes.length);
      setAllShops(shopsRes.length);
  
      console.log('🟢 All dashboard data fetched');
    } catch (error) {
      console.error('❌ Error fetching dashboard data:', error);
    } finally {
      setloading(false);
    }
  };
  
    
    
       const fetch_all_barbers = async () => {
              try {
                setloading(true);
                 
          
                  const response = await get_all_barber();
                  console.log('🟢 All barbers fetched:', response.length);
                  setAllbabers(response.length);
              } catch (error) {
                  console.error('❌ Failed to fetch services:', error);
              } finally {
                setloading(false);
                
              }
          };

          const fetch_all_gift_cards = async () => {
            try {
              setloading(true);
               
        
                const response = await get_all_giftcard();
                console.log('🟢 All  cards:', response.length);
                setAllGiftCard(response.length);
            } catch (error) {
                console.error('❌ Failed to fetch services:', error);
            } finally {
              setloading(false);
              
            }
        };

        const fetch_all_services = async () => {
          try {
            setloading(true);
             
      
              const response = await get_all_service();
              console.log('🟢 All services fetched:', response.length);
              setAllServices(response.length);
          } catch (error) {
              console.error('❌ Failed to fetch services:', error);
          } finally {
            setloading(false);
            
          }
      };

      const fetch_all_salons = async () => {
        try {
          setloading(true);
           
    
            const response = await get_all_shops();
            console.log('🟢 All shops:', response.length);
            setAllShops(response.length);
        } catch (error) {
            console.error('❌ Failed to fetch services:', error);
        } finally {
          setloading(false);
          
        }
    };

    const useAnimatedCount = (targetValue, duration = 1000) => {
      const [count, setCount] = useState(0);
    
      useEffect(() => {
        let start = 0;
        const increment = targetValue / (duration / 16); // Approx 60fps
        const interval = setInterval(() => {
          start += increment;
          if (start >= targetValue) {
            start = targetValue;
            clearInterval(interval);
          }
          setCount(Math.floor(start));
        }, 16); // ~60fps
    
        return () => clearInterval(interval);
      }, [targetValue, duration]);
    
      return count;
    };
    

    const animatedBarbers = useAnimatedCount(allBarbers);
    const animatedGiftCards = useAnimatedCount(allGiftCard);
    const animatedServices = useAnimatedCount(allServices);
    const animatedShops = useAnimatedCount(allShops);
    




  // Get Data from ASYNC 
  const getUserData = async () => {
    const userData = await AsyncStorage.getItem('userData');
    if (userData) {
      const parsed = JSON.parse(userData);
      console.log("🔐 Retrieved user info:", parsed);
      // Use parsed.token, parsed.username, etc.
    }
  };

  const pieData = [
    {
      name: AppText.GIFT_PRODUCTS,
      population:allGiftCard,
      color: colors.Light_theme_maincolour,
      legendFontColor: getTextColor(),
      legendFontSize: wp(3) // Add this line with your desired font size

    },
    {
      name: AppText.SERVICES,
      population: allServices,
      color: colors.c_green,
      legendFontColor: getTextColor(),
      legendFontSize: wp(3) // Add this line with your desired font size

    },
    {
      name: AppText.SALONS,
      population: allShops,
      color: colors.darkGrey,
      legendFontColor: getTextColor(),
      legendFontSize: wp(3) // Add this line with your desired font size

    },
    {
      name: AppText.BARBERS,
      population: allBarbers,
      color: colors.lightGrey,
      legendFontColor: getTextColor(),
      legendFontSize: wp(3) // Add this line with your desired font size

    },
  ];

  // Sample data for charts
  const chartData = {
    labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    datasets: [{
      data: [20, 45, 28, 80, 99, 43, 50]
    }]
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.DASHBOARD} />
      
      <MainCard style={styles.mainCardStyle}>
        <View style={styles.headerContainer}>
          <ResponsiveText size={4.5} color={getTextColor()} weight={'700'}>
            {AppText.TOTAL_STORES}
          </ResponsiveText>
          <View style={[styles.toggleContainer,{borderColor:getDark_Theme(),backgroundColor:getDarK_mode_LightGrayBackground()}]}>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                viewMode === 'view' && { backgroundColor: colors.Light_theme_maincolour }
              ]}
              onPress={() => setViewMode('view')}
            >
              <ResponsiveText 
                size={3.5} 
                color={viewMode === 'view' ? colors.white : getTextColor()}
              >
                {AppText.VIEW}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                viewMode === 'pie' && { backgroundColor: colors.Light_theme_maincolour }
              ]}
              onPress={() => setViewMode('pie')}
            >
              <ResponsiveText 
                size={3.5} 
                color={viewMode === 'pie' ? colors.white : getTextColor()}
              >
                {AppText.PIE_CHART}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.contentContainer} scrollEnabled={false}>
          {viewMode === 'view' ? (
            <View style={styles.cardsContainer}>
              <View style={styles.row}>
                <DashboardCard
                  icon={globalpath.pending}
                  value={animatedGiftCards}
                  label={AppText.GIFT_PRODUCTS}
                />
                <DashboardCard
                  icon={globalpath.pending}
                  value={animatedServices}
                  label={AppText.SERVICES}
                />
              </View>
              <View style={styles.row}>
                <DashboardCard
                  icon={globalpath.earnings}
                  value={animatedShops}
                  label={AppText.SALONS}
                />
                <DashboardCard
                  icon={globalpath.barbers}
                  value={animatedBarbers}
                  label={AppText.BARBERS}
                />
              </View>
            </View>
          ) : (
            <View style={styles.pieChartContainer}>
              <PieChart
                data={pieData}
                width={screenWidth - wp(10)}
                height={hp(22)}
                chartConfig={{
                  color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                }}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="-3"
                absolute
              />
            </View>
          )}
        </ScrollView>
      </MainCard>

      <View style={styles.timePeriodContainer}>
          <TouchableOpacity
            style={[
              [styles.timeButton,{borderColor:getDark_Theme()}],
              timePeriod === '7' && { backgroundColor: colors.c_green }
            ]}
            onPress={() => setTimePeriod('7')}
          >
            <ResponsiveText 
              size={3.5} 
              color={timePeriod === '7' ? colors.white : getTextColor()}
            >
              {AppText.SEVEN_DAYS}
            </ResponsiveText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              [styles.timeButton,{borderColor:getDark_Theme()}],
              timePeriod === '15' && { backgroundColor: colors.c_green }
            ]}
            onPress={() => setTimePeriod('15')}
          >
            <ResponsiveText 
              size={3.5} 
              color={timePeriod === '15' ? colors.white : getTextColor()}
            >
              {AppText.FIFTEEN_DAYS}
            </ResponsiveText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              [styles.timeButton,{borderColor:getDark_Theme()}],
              timePeriod === '30' && { backgroundColor: colors.c_green }
            ]}
            onPress={() => setTimePeriod('30')}
          >
            <ResponsiveText 
              size={3.5} 
              color={timePeriod === '30' ? colors.white : getTextColor()}
            >
              {AppText.THIRTY_DAYS}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

      {/* New Chart Card */}
      <MainCard style={styles.chartCardStyle}>
        <View style={styles.chartHeaderContainer}>
          <ResponsiveText size={4.5} color={getTextColor()} weight={'700'}>
            {AppText.CHATS}
          </ResponsiveText>
          <View style={[styles.toggleContainer,{borderColor:getDark_Theme(),backgroundColor:getDarK_mode_LightGrayBackground()}]}>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                chartType === 'line' && { backgroundColor: colors.Light_theme_maincolour }
              ]}
              onPress={() => setChartType('line')}
            >
              <ResponsiveText 
                size={3.5} 
                color={chartType === 'line' ? colors.white : getTextColor()}
              >
                {AppText.LINE_CHART}
              </ResponsiveText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                chartType === 'bar' && { backgroundColor: colors.Light_theme_maincolour }
              ]}
              onPress={() => setChartType('bar')}
            >
              <ResponsiveText 
                size={3.5} 
                color={chartType === 'bar' ? colors.white : getTextColor()}
              >
                {AppText.BAR_CHART}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </View>

    

        <View style={styles.chartContainer}>
          <ScrollView horizontal={true}>
          {chartType === 'line' ? (
            <CustomLineChart data={chartData} />
          ) : (
            <CustomBarChart data={chartData} />
          )}
          </ScrollView>
        </View>
      </MainCard>
      {loading ? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainCardStyle: {
    height: "38%",
  },
  chartCardStyle: {
    height: "40.5%",
    marginTop: hp(1),
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal:wp(5),
    marginVertical:hp(1),
    marginTop:hp(2.5)
  },
  chartHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal:wp(5),
    marginVertical:hp(0.5),
    marginTop:hp(2)
  },
  toggleContainer: {
    flexDirection: 'row',
    borderRadius: wp(6),
    padding: wp(1),
    borderWidth: 1,
  },
  toggleButton: {
    paddingHorizontal: wp(4.5),
    paddingVertical: wp(1.5),
    borderRadius: wp(6),
  },
  timePeriodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: wp(5),
    marginVertical: hp(1),
  },
  timeButton: {
    paddingHorizontal: wp(6),
    paddingVertical: wp(2),
    borderRadius: wp(1.5),
    borderWidth: 1,
    // borderColor: colors.Light_theme_maincolour,
  },
  contentContainer: {
    flex: 1,
  },
  cardsContainer: {
    paddingHorizontal: wp(2),
  },
  row: {
    flexDirection: 'row',
  },
  pieChartContainer: {
    alignItems: 'center',
  },
  chartContainer: {
    // flex: 1,
    justifyContent: 'center',
    justifyContent:"center",
    marginHorizontal:wp(1.5)
  },
});