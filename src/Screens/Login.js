import {
  SafeAreaView,
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {colors} from '../Custom/Colors';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {wp, hp} from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import useAppText from '../Custom/AppText';
import useTheme from '../Redux/useTheme';
import {deviceToken, login} from '../Services/API/Endpoints/Auth/Auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {isTablet} from '../Custom/TabResoponsive/TabResponsive';
import { useFocusEffect } from '@react-navigation/native';
import { get_customer_details } from '../Services/API/Endpoints/Customer/Get_Customer_Details';
import { get_admin_details } from '../Services/API/Endpoints/Admin/Get_Admin_Details';
import { get_all_barber } from '../Services/API/Endpoints/Admin/Barber';

import messaging, {AuthorizationStatus} from '@react-native-firebase/messaging';
import { getToken } from '@react-native-firebase/messaging';
import { getApp } from '@react-native-firebase/app';

const Login = ({navigation}) => {

  useFocusEffect(
    useCallback(() => {
      setLoading(false); // Reset loader every time screen is focused
    }, [])
  );


  const {
    themeColor,
    backgroundColor,
    getTextColor,
    getSecondaryTextColor,
    selectedTheme,
    getborderTextColor,
    getDark_Theme,
    get_Font_text_color,
  } = useTheme();
  const AppText = useAppText();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
    const [fcmToken, setFcmToken] = useState(null);
    const [user_id, setUser_id] = useState(null);

const getFCMToken = async () => {
  try {
    const existingToken = await AsyncStorage.getItem('FCMToken');
    if (existingToken) {
      console.log('📦 Existing FCM Token:', existingToken);
      setFcmToken(existingToken);
      return existingToken;
    }

    const token = await messaging().getToken();
    if (token) {
      await AsyncStorage.setItem('FCMToken', token);
      console.log('✅ New FCM Token generated and saved:', token);
      setFcmToken(token);
    }

    return token;
  } catch (error) {
    console.error('❌ Error getting FCM token:', error);
    return null;
  }
};
useEffect(() => {
  getFCMToken();
}, []);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password.');
      return;
    }
  
    setLoading(true);
  
    try {
      const payload = {
        username: email,
        password: password,
      };
  
      const response = await login(payload);
   const user_id = response?.user_id; // ✅ Extract user_id
    console.log('LOGIN RESPONSE user_id:', user_id);

    setUser_id(user_id); // ✅ Correct value
          const user_id_sent_to = await AsyncStorage.getItem('user_id');

 
      // Save basic login info
      const loginData = {
        email: response.email,
        role: response.role,
        token: response.token,
        user_id: response.user_id,
        username: response.username,
      };
  
      await AsyncStorage.setItem('userData', JSON.stringify(loginData));
      await AsyncStorage.setItem('loginResponse', JSON.stringify(response)); // if you still want to keep this
  
      // 🔁 Fetch role-specific details
      const role = response.role?.toLowerCase();
      console.log("current LOGIN ROLE IS === = = =   = =  =  = = == = ",role)
      let detailedResponse;
      let storageKey;
  
      switch (role) {
        case 'customer':
          detailedResponse = await get_customer_details();
          storageKey = 'customerData';
          break;
        case 'barber':
          detailedResponse = await get_all_barber();
          storageKey = 'barberData';
          break;
          case 'admin':
            try {
              detailedResponse = await get_admin_details();
              console.log('Admin Details:', detailedResponse);
              storageKey = 'adminData';
              await AsyncStorage.setItem(storageKey, JSON.stringify(detailedResponse));
            } catch (err) {
              console.error('❌ Failed to fetch or save admin details:', err);
            }
            break;
        default:
          console.warn('❌ Unknown role:', role);
          break;
      }
  
      if (detailedResponse && storageKey) {
        await AsyncStorage.setItem(storageKey, JSON.stringify(detailedResponse));
        console.log(`✅ ${role} details saved to ${storageKey}`);
      }
  
          const tokenToSend = await AsyncStorage.getItem('FCMToken');
          console.log('user_id and tokenToSend:', user_id, tokenToSend);

          if (user_id && tokenToSend) {
            await handle_token(user_id, tokenToSend);
          }

  
      navigation.replace('AppStack');
    } catch (error) {
      console.error('❌ Error in Login:', error);
  
      const errorData = error?.response?.data;
  
      if (errorData && typeof errorData === 'object') {
        const firstKey = Object.keys(errorData)[0];
        const firstMessage = errorData[firstKey]?.[0];
  
        if (firstMessage) {
          Alert.alert('Login Failed', firstMessage);
        } else {
          Alert.alert('Login Failed', 'Something went wrong.');
        }
      } else {
        Alert.alert('Login Failed', error.message || 'Something went wrong.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handle_token = async (user_id, fcmToken) => {
  try {
    const payload = {
      user: user_id,
      token: fcmToken,
    };
    console.log('Payload for token:', payload);

    const response = await deviceToken(payload);
    console.log('Device token response:', response);
  } catch (error) {
    console.error('Error sending FCM token:', error);
  }
};


  useEffect(() => {
    const fetchSessionCookie = async () => {
      const sessionCookie = await AsyncStorage.getItem('sessionCookie');
      console.log('🍪 Session Cookie from AsyncStorage:', sessionCookie);
    };

    fetchSessionCookie(); // Call the async function
  }, []);

  const handleRegister = async () => {
    try {
      // setLoading(false);
      navigation.navigate('Register');
    } catch (error) {
      setLoading(false);
    }
  };

  const handleSocialLogin = platform => {
    // Handle social login logic here
    console.log(`Login with ${platform}`);
  };



  useEffect(() => {
    const fetchOrGenerateFCMToken = async () => {
      try {
        // 1. Check AsyncStorage first
        const storedToken = await AsyncStorage.getItem('FCMToken');
        
        if (storedToken) {
          console.log('Retrieved stored FCM token in login screen', storedToken);
          setFcmToken(storedToken);
        } else {
          // 2. If no token exists, generate a new one
          console.log('No stored token. Generating new FCM token...');
          // const newToken = await getFCMToken();
          // setFcmToken(newToken);
        }
      } catch (error) {
        console.error('FCM token error:', error);
      }
    };

    fetchOrGenerateFCMToken();

  }, []);


  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: backgroundColor}]}>
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <Icon
            source={globalpath.logo}
            size={isTablet ? wp(22) : wp(29)}
            // margin={[hp(2), 0]}
            margin={[hp(6), 0, 0, 0]}
            tintColor={getTextColor()}
          />
          <ResponsiveText
            weight={'500'}
            size={isTablet ? 6 : 7}
            margin={[hp(3), 0, wp(5), 0]}
            color={get_Font_text_color()}
            fontFamily="PlayfairDisplay">
            {AppText.WELCOME_TITLE_LOGIN}
          </ResponsiveText>
        </View>

        <View style={styles.formContainer}>
          <View style={[styles.inputContainer, {borderColor: getDark_Theme()}]}>
            <TextInput
              style={[styles.input, {color: getTextColor()}]}
              placeholder={AppText.EMAIL_PLACEHOLDER}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor={getborderTextColor()}
            />
          </View>

          <View style={[styles.inputContainer, {borderColor: getDark_Theme()}]}>
            <TextInput
              style={[styles.input, {color: getTextColor()}]}
              placeholder={AppText.PASSWORD_PLACEHOLDER}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              placeholderTextColor={getborderTextColor()}
            />
            <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
              <Icon
                source={showPassword ? globalpath.eyeOff : globalpath.eye}
                size={wp(5)} margin={[0,0,0,wp(3)]}
                tintColor={getborderTextColor()}
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            onPress={() => navigation.navigate('ForgotPassword')}
            style={styles.forgotPasswordContainer}>
            <ResponsiveText
              color={getborderTextColor()}
              size={isTablet ? 3.2 : 3.5}>
              {AppText.FORGOT_PASSWORD}
            </ResponsiveText>
          </TouchableOpacity>

          {loading ? (
            <View style={styles.loginButton}>
              <ActivityIndicator size={isTablet?hp(3):"small"} color={colors.white} />
            </View>
          ) : (
            <TouchableOpacity
              style={styles.loginButton}
              onPress={() => handleLogin()}>
              {/* onPress={()=>navigation.navigate('BarberHome')}> */}
              <ResponsiveText color={colors.white} size={4} weight="bold">
                {AppText.LOGIN_BUTTON}
              </ResponsiveText>
            </TouchableOpacity>
          )}

          <View style={styles.dividerContainer}>
            <View
              style={[styles.divider, {backgroundColor: getDark_Theme()}]}
            />
            <ResponsiveText
              style={styles.dividerText}
              color={getborderTextColor()}>
              {AppText.OR_LOGIN_WITH}
            </ResponsiveText>
            <View
              style={[styles.divider, {backgroundColor: getDark_Theme()}]}
            />
          </View>

          <View style={styles.socialLoginContainer}>
            <TouchableOpacity
              style={[styles.socialButton, {borderColor: getDark_Theme()}]}
              onPress={() => handleSocialLogin('google')}>
              <Icon
                source={globalpath.google}
                size={isTablet ? wp(4) : wp(5)}
                margin={[0, wp(2), 0, 0]}
              />
              <ResponsiveText color={getTextColor()} size={isTablet ? 3.2 : 4}>
                {AppText.Google}
              </ResponsiveText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.socialButton, {borderColor: getDark_Theme()}]}
              onPress={() => handleSocialLogin('facebook')}>
              <Icon
                source={globalpath.facebook}
                size={isTablet ? wp(4) : wp(5)}
                margin={[0, wp(2), 0, 0]}
              />
              <ResponsiveText color={getTextColor()} size={isTablet ? 3.2 : 4}>
                {AppText.Facebook}
              </ResponsiveText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.socialButton, {borderColor: getDark_Theme()}]}
              onPress={() => handleSocialLogin('apple')}>
              <Icon
                source={globalpath.apple}
                size={isTablet ? wp(4) : wp(5)}
                margin={[0, wp(2), 0, 0]}
                tintColor={getTextColor()}
              />
              <ResponsiveText color={getTextColor()} size={isTablet ? 3.2 : 4}>
                {AppText.Apple}
              </ResponsiveText>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.registerButton, {borderColor: getDark_Theme()}]}
            onPress={handleRegister}>
            <ResponsiveText
              color={getborderTextColor()}
              weight={'600'}
              size={isTablet ? 3.2 : 4}>
              {AppText.REGISTER_BUTTON}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    alignItems: 'center',
    // marginVertical: wp(3),
    marginTop: Platform.OS === 'android' ? hp(5) : null,
  },
  formContainer: {
    width: '90%',
    alignSelf: 'center',
  },
  inputContainer: {
    height:hp(6),
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    paddingHorizontal: Platform.OS === 'android' ? wp(4.7) : null,
    padding: Platform.OS === 'ios' ? wp(4) : null,
    borderWidth: 1,
  },
  input: {
    flex: 1,
    fontSize: isTablet ? wp(3.5) : wp(4),
    // color: colors.greyBlack,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: hp(4),
  },
  loginButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: isTablet ? wp(2.3) : wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(3),
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(3),
  },
  divider: {
    flex: 1,
    height: 1,
    // backgroundColor: colors.greyborder,
  },
  dividerText: {
    marginHorizontal: wp(3),
    color: colors.darkGrey,
  },
  socialLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: isTablet ? hp(8) : hp(4),
  },
  socialButton: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: isTablet?wp(4.2): wp(3),
    paddingVertical: isTablet?wp(1.6): wp(2),
    borderRadius: wp(1.5),
    borderWidth: 1,
    // borderColor: colors.border,
    // marginHorizontal: wp(1),
  },
  registerButton: {
    width: '100%',
    padding: isTablet ? wp(3) : wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
});
