import { StyleSheet, View, SafeAreaView, FlatList, ScrollView, RefreshControl, TextInput, Platform } from 'react-native'
import React, { useState, useCallback, useEffect } from 'react'
import { useNavigation } from '@react-navigation/native';
import BarberHeader from '../../Components/Barber_Section_Components/Barber_Home_Components/BarberHeader'
import { colors } from '../../Custom/Colors';
import { hp,wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import Customer_Book_Now_Card from '../../Components/CustomerComponents/Book_Now_Components/Customer_Book_Now_Card';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { get_all_shops } from '../../Services/API/Endpoints/Admin/Salons';
import { isTablet } from '../../Custom/TabResoponsive/TabResponsive';
import ResponsiveText from '../../Custom/RnText';
import Loader from '../../Custom/loader';
import CustomHeader from '../../Components/CustomHeader';
import Admin_Appointment_Card from '../../Components/Admin_Appointment_Component/Admin_Appointment_Card';

const Admin_Appointment = () => {
  const {backgroundColor, getDark_Theme, getTextColor} = useTheme();
  const AppText = useAppText();
  const navigation = useNavigation();
  const [shops, setShops] = useState([]);
  const [filteredShops, setFilteredShops] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [AllShops, setAllShops] = useState([]);

  const getAllshops = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const response = await get_all_shops();
      console.log('🟢 Shops fetched in admin_appointment:', response);
      setAllShops(response);
      if (Array.isArray(response)) {
        const sortedServices = response.sort((a, b) => b.id - a.id);
        setShops(sortedServices);
        setFilteredShops(sortedServices);
      }
    } catch (error) {
      console.error('❌ Failed to fetch shops in admin:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleSearch = (text) => {
    setSearchQuery(text);
    if (text) {
      const filtered = shops.filter(shop =>
        shop.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredShops(filtered);
    } else {
      setFilteredShops(shops);
    }
  };

  useEffect(() => {
    setFilteredShops(shops);
  }, [shops]);

  const onRefresh = useCallback(() => {
    getAllshops();
    setSearchQuery('');
  }, []);

  useEffect(() => {
    getAllshops();
  }, []);

  const handleSalonPress = (salon ) => {
    console.log('Salon pressed:', salon);
    navigation.navigate('Barber_Home_Stack', { screen: 'BarberHome', params: { salonData: salon  , AllShopsData : filteredShops} });  // Pass the salon data
  };

  const renderSalonCard = ({ item }) => (
    <Admin_Appointment_Card 
      item={item} 
      onPress={() => handleSalonPress(item)}
    />
  );

  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]}>
      <CustomHeader title={AppText.APPOINTMENT} />
      <ScrollView 
        style={styles.container} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.Light_theme_maincolour]}
            tintColor={colors.Light_theme_maincolour}
          />
        }
      >

<View style={styles.searchContainer}>
          <View style={[styles.searchInputContainer, {borderColor: getDark_Theme()}]}>
            <Icon source={globalpath.search} size={wp(4)} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_SALONS}
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
            />
          </View>
        </View>
        {/* <View style={styles.mainCard}>
          <View style={styles.cardContent}>
            <View style={styles.leftSection}>
              <ResponsiveText 
                size={4.8} 
                weight={'800'} 
                color={colors.white}
                margin={[0,0,wp(1.5),0]}
              >
                {AppText.DISCOVER_SALONS}
              </ResponsiveText>
              
              <ResponsiveText 
                size={3.5} 
                weight={'500'} 
                color={colors.black}
                style={styles.bookText}
              >
                {AppText.BOOK_PERFECT_BARBER}
              </ResponsiveText>
            </View>
          </View>
        </View> */}
{/* 
        <View style={styles.rightSection}>
          <Icon 
            source={globalpath.barber_icon} 
            size= { isTablet?wp(38): Platform.OS==="android"? wp(38.3):wp(45) } 
            style={styles.barberIcon}
            resizeMode="cover"
          />
        </View> */}

  

        <View style={styles.content}>
          <FlatList
            data={filteredShops}
            renderItem={renderSalonCard}
            keyExtractor={item => item.id.toString()}
            scrollEnabled={false}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <View style={styles.noResults}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {AppText.NO_RESULTS_FOUND}
                </ResponsiveText>
              </View>
            )}
          />
        </View>
      </ScrollView>
      {loading && <Loader />}
    </SafeAreaView>
  )
}

export default Admin_Appointment

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainCard: {
    backgroundColor: colors.Light_theme_maincolour,
    marginHorizontal: wp(3),
    marginTop: hp(2),
    borderRadius: wp(4),
    padding: wp(4),
    height: hp(17),
    overflow: 'hidden',
    marginTop: hp(5)
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '100%',
  },
  leftSection: {
    flex: 1,
    justifyContent: 'center',
  },
  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginTop: hp(2),
  },
  bookText: {
    opacity: 0.8,
  },
  barberIcon: {
    position: 'absolute',
    right: wp(10),
    top: isTablet?-hp(25): Platform.OS==='android'?-hp(20.7): -hp(23),
  },
  searchContainer: {
    paddingHorizontal: wp(3),
    marginTop: hp(2),
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
    height: isTablet?hp(6): hp(5)
  },
  searchInput: {
    flex: 1,
    fontSize:  isTablet?16:14,
  },
  content: {
    padding: wp(3),
  },
  listContent: {
    paddingBottom: hp(2),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
    height: hp(55)
  }
})