import { SafeAreaView, StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native'
import React, { useState } from 'react'
import { colors } from '../Custom/Colors'
import { wp, hp } from '../Custom/Responsiveness'
import ResponsiveText from '../Custom/RnText'
import useAppText from '../Custom/AppText'
import Icon from '../Custom/Icon'
import { globalpath } from '../Custom/globalpath'
import useTheme from '../Redux/useTheme'

const ResetPassword = ({ navigation }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor ,getDark_Theme} = useTheme();
  const AppText = useAppText();
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  // const isPasswordValid = password && confirmPassword && password === confirmPassword;


  const validatePassword = () => {
    if (!password || !confirmPassword) {
      alert('Please fill all fields');
      return false;
    }
    if (password !== confirmPassword) {
      alert('Passwords do not match');
      return false;
    }
    // Check for at least one number or special character
    if (!/(?=.*[0-9!@#$%^&*])/.test(password)) {
      alert('Password must contain at least 1 number or special character');
      return false;
    }
    return true;
  };

  const isPasswordValid = 
  password && 
  confirmPassword && 
  password === confirmPassword && 
  /(?=.*[0-9!@#$%^&*])/.test(password); 

  const handleResetPassword = async () => {
    if (!validatePassword()) return

    try {
      setLoading(true)
      // Add your reset password logic here
      navigation.navigate('Login')
    } catch (error) {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <View style={styles.contentContainer}>
        <ResponsiveText weight={'600'} size={7} margin={[hp(2), 0, wp(3), 0]} textAlign={'center'} color={getTextColor()}>
          {AppText.RESET_PASSWORD_TITLE}
        </ResponsiveText>

        <ResponsiveText color={getborderTextColor()} size={4} margin={[0, 0, wp(5), wp(0)]} textAlign={'center'}>
          {AppText.RESET_PASSWORD_SUBTITLE}
        </ResponsiveText>

        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input, { color: getTextColor() }]}
            placeholder={AppText.ENTER_PASSWORD_PLACEHOLDER}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            placeholderTextColor={getborderTextColor()}
          />
          <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
            <Icon
              source={showPassword ? globalpath.eyeOff : globalpath.eye}
              size={wp(5)} margin={[0,0,0,wp(3)]}
              tintColor={getborderTextColor()}
            />
          </TouchableOpacity>
        </View>

        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input, { color: getTextColor() }]}
            placeholder={AppText.CONFIRM_PASSWORD_PLACEHOLDER}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showConfirmPassword}
            placeholderTextColor={getborderTextColor()}
          />
          <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
            <Icon
              source={showConfirmPassword ? globalpath.eyeOff : globalpath.eye}
              size={wp(5)} margin={[0,0,0,wp(3)]}
              tintColor={getborderTextColor()}
            />
          </TouchableOpacity>
        </View>
        {password && confirmPassword && password !== confirmPassword && (
  <ResponsiveText color="red" size={3} margin={[hp(-1.3), 0, hp(1), 0]} textAlign={'left'}>
    {AppText.PASSWORD_DONT_MATCH}
  </ResponsiveText>
)}

{!isPasswordValid && (
  <ResponsiveText
    color={getborderTextColor()}
    size={3.5}
    margin={[0, 0, wp(5), wp(2)]}
    textAlign={'left'}
  >
    {AppText.PASSWORD_REQUIREMENT}
  </ResponsiveText>
)}

        {loading ? (
          <View style={styles.resetButton}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
          style={[
            styles.resetButton,
            { backgroundColor: isPasswordValid ? colors.Light_theme_maincolour : getDark_Theme() } // Optional: visual feedback
          ]}
          onPress={handleResetPassword}
          disabled={!isPasswordValid}
        >
          <ResponsiveText color={ isPasswordValid ? colors.white : colors.grey} size={4} weight="600">
            {AppText.RESET_PASSWORD_BUTTON}
          </ResponsiveText>
        </TouchableOpacity>
        )}
      
      </View>
    </SafeAreaView>
  )
}

export default ResetPassword

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    padding: wp(5),
    marginTop: hp(7),
  },

     inputContainer: {
      height:hp(6),
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: wp(1.5),
        marginBottom: hp(2),
        paddingHorizontal: Platform.OS === 'android' ? wp(4.7) :null,
        padding: Platform.OS ==='ios' ? wp(4) : null,
        borderWidth: 1,
      },
  input: {
    flex: 1,
    fontSize: wp(4),
  },
  resetButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(2),
  },
})