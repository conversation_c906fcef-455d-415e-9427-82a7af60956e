import { SafeAreaView, StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView } from 'react-native'
import React, { useState } from 'react'
import { colors } from '../Custom/Colors'
import { wp, hp } from '../Custom/Responsiveness'
import ResponsiveText from '../Custom/RnText'
import useAppText from '../Custom/AppText'
import useTheme from '../Redux/useTheme'
import { useFocusEffect } from '@react-navigation/native';
import Icon from '../Custom/Icon'
import { globalpath } from '../Custom/globalpath'

const ForgotPassword = ({ navigation }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor,getDark_Theme } = useTheme();
  const AppText = useAppText();
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)

  const isValidEmail = (email) => {
    const gmailRegex = /^[a-zA-Z0-9._%+-]+@gmail\.com$/;
    return gmailRegex.test(email);
  };
  
  const handleSendCode = async () => {
    if (!email) {
      alert('Please enter your email');
      return;
    }
  
    if (!isValidEmail(email)) {
      alert('Please enter a valid Gmail address');
      return;
    }
  
    try {
      setLoading(true);
      navigation.navigate('ForgotPasswordOTP', { email });
    } catch (error) {
      setLoading(false);
    }
  };
  useFocusEffect(
    React.useCallback(() => {
      // Reset loading when screen comes into focus
      setLoading(false);
    }, [])
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <KeyboardAvoidingView style={{flex:1}} behavior={Platform.OS?"height":"padding"}>
      <View style={styles.contentContainer}>
        <ResponsiveText weight={'600'} size={7} margin={[hp(2), 0, wp(3), 0]} textAlign={'center'} color={getTextColor()}>
          {AppText.FORGOT_PASSWORD_TITLE}
        </ResponsiveText>

        <ResponsiveText color={getborderTextColor()} size={4} margin={[0, 0, wp(5), wp(5)]} textAlign={'center'}>
          {AppText.FORGOT_PASSWORD_SUBTITLE}
        </ResponsiveText>

        <View style={[styles.inputContainer, { borderColor: getDark_Theme() }]}>
          <TextInput
            style={[styles.input, { color: getTextColor() }]}
            placeholder={AppText.EMAIL_PLACEHOLDER}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor={getborderTextColor()}
          />
        </View>

        {loading ? (
          <View style={[styles.sendButton, { backgroundColor: 'grey' }]}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
            style={[
              styles.sendButton,
              { backgroundColor: isValidEmail(email) ? colors.Light_theme_maincolour : getDark_Theme() },
            ]}
            onPress={handleSendCode}
            disabled={!isValidEmail(email)}
          >
            <ResponsiveText color={ isValidEmail(email) ? colors.white : colors.grey} size={4} weight="600">
              {AppText.SEND_CODE_BUTTON}
            </ResponsiveText>
          </TouchableOpacity>
        )}

        <TouchableOpacity 
          style={[styles.backButton, { borderColor: getDark_Theme() }]} 
          onPress={() => navigation.navigate('Login')}
        >
          <View style={styles.backButtonContent}>
            <Icon 
              source={globalpath.back_arrow} 
              size={wp(5)} 
              tintColor={colors.Light_theme_maincolour}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText 
              color={colors.Light_theme_maincolour} 
              size={4} 
              weight={'600'}
            >
              {AppText.BACK_TO_LOGIN}
            </ResponsiveText>
          </View>
        </TouchableOpacity>
      </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default ForgotPassword

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    padding: wp(5),
    marginTop: hp(2),
  },
  inputContainer: {
    height:hp(6),
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: wp(1.5),
    marginBottom: hp(2),
    paddingHorizontal: Platform.OS === 'android' ? wp(4.7) :null,
    padding: Platform.OS ==='ios' ? wp(4) : null,
    borderWidth: 1,
  },
  input: {
    flex: 1,
    fontSize: wp(4),
  },
  sendButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(3),
  },
  backButton: {
    // marginTop: 'auto',
    marginBottom: Platform.OS === 'ios' ? hp(2) : hp(4),
    borderWidth: 1,
    borderRadius: wp(1.5),
    backgroundColor: 'transparent',
  },
  backButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: wp(3),
  },
})