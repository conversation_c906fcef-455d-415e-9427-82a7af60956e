import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import CustomerProfile from '../../../Components/CustomerComponents/Customer_Settings/CustomerProfile'
import CustomerMenuItem from '../../../Components/CustomerComponents/Customer_Settings/CustomerMenuItem'
import { globalpath } from '../../../Custom/globalpath'
import { hp } from '../../../Custom/Responsiveness'
import useTheme from '../../../Redux/useTheme'

const Customer_Settings = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();

  const menuItems = [
    { icon: globalpath.theme, title: AppText.LIGHT_MODE, isThemeSwitch: true, isLanguageSwitch: false },
    { icon: globalpath.language, title: AppText.ENGLISH, isThemeSwitch: false, isLanguageSwitch: true },
    { icon: globalpath.notifications, title: AppText.NOTIFICATIONS, isThemeSwitch: false, isLanguageSwitch: false },
    { icon: globalpath.help, title: AppText.HELP, isThemeSwitch: false, isLanguageSwitch: false },
    { icon: globalpath.about, title: AppText.ABOUT, isThemeSwitch: false, isLanguageSwitch: false },
  ];

  const handleMenuItemPress = (title) => {
    // Handle navigation or other actions for non-switch items
    console.log(`Pressed: ${title}`);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <Customer_Header title={AppText.SETTINGS} />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <CustomerProfile />
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <CustomerMenuItem
              key={index}
              icon={item.icon}
              title={item.title}
              onPress={() => handleMenuItemPress(item.title)}
              isThemeSwitch={item.isThemeSwitch}
              isLanguageSwitch={item.isLanguageSwitch}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Customer_Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  menuContainer: {
    marginTop: hp(2),
  },
});