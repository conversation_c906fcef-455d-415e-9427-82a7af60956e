import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { wp } from '../../../Custom/Responsiveness';
import { useNavigation } from '@react-navigation/native';

const Order_Success = () => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { backgroundColor, getTextColor, getDark_Theme } = useTheme();
  const handleLeftIconPress = () => {
    navigation.navigate('Products'); // <- replace 'Products' with your actual screen name if different
  };
  
  
  return (
       <SafeAreaView style={[styles.container, { backgroundColor }]}>
         <Customer_Header title={AppText.ORDER_PLACED} leftIconType="back" showCartIcon={false}
         onPress={handleLeftIconPress}
          />
         <View style={styles.content}>
            <ResponsiveText>{AppText.ORDER_PLACED_SUCCESSFULLY}</ResponsiveText>
            <TouchableOpacity style={styles.button} onPress={() => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Track_Orders' }],
              });
            }}>
              <ResponsiveText color={colors.white}>{AppText.VIEW_ORDERS}</ResponsiveText>
            </TouchableOpacity>
            </View>
            </SafeAreaView>
  )
}

export default Order_Success

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    backgroundColor: colors.Light_theme_maincolour,
   padding:wp(2),
   borderRadius:wp(1.5),
   marginTop:wp(3)
  }
})
