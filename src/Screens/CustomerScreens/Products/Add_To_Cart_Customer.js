import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { hp, wp } from '../../../Custom/Responsiveness';
import Customer_Product_Image_Carousel from '../../../Components/CustomerComponents/ProductComponents/Customer_Product_Image_Carousel';
import Customer_ProductSizeDropdown from '../../../Components/CustomerComponents/ProductComponents/Customer_ProductSizeDropdown';
import Customer_ProductDescription from '../../../Components/CustomerComponents/ProductComponents/Customer_ProductDescription';
import CustomerProductFavorite from '../../../Components/CustomerComponents/ProductComponents/CustomerProductFavorite';
import CustomerProductQuantity from '../../../Components/CustomerComponents/ProductComponents/CustomerProductQuantity';
import CustomerAddToCartButton from '../../../Components/CustomerComponents/ProductComponents/CustomerAddToCartButton';
import { globalpath } from '../../../Custom/globalpath';

const Add_To_Cart_Customer = ({ route, navigation }) => {
  const { product } = route.params;
  console.log("product",product)
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [currentPrice, setCurrentPrice] = useState(parseFloat(product.price));
  const [quantity, setQuantity] = useState(1);


  const productPrice = product.price;
  let imagesToShow = [];

  // 1️⃣ Collect exact price matched variant images
  const exactPriceVariantImages = product.variants
    .filter(variant => variant.price_override === productPrice && Array.isArray(variant.variant_images))
    .flatMap(variant => variant.variant_images);

  // 2️⃣ Collect all other variant images
  const allVariantImages = product.variants
    .filter(variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0)
    .flatMap(variant => variant.variant_images);

  // 3️⃣ Always append product images at the end
  const productImages = Array.isArray(product.product_images) ? product.product_images : [];

  // 🔥 Combine ALL images into imagesToShow
  imagesToShow = [...exactPriceVariantImages, ...allVariantImages, ...productImages];

  console.log('✅ Final imagesToShow:', imagesToShow);



  const handleSizeSelect = (variant) => {
    setSelectedVariant(variant);
    setCurrentPrice(parseFloat(variant.price_override));
  };

  const handleQuantityChange = (newQuantity) => {
    setQuantity(newQuantity);
  };

  const handleAddToCart = () => {
  };

  const images = product.product_images?.map(img => ({ uri: img.image })) || [];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Customer_Header title={AppText.PRODUCTS_DETAILS} leftIconType="back" showCartIcon={true} />
      <Customer_Product_Image_Carousel images={imagesToShow} />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.detailsContainer}>
          <View style={styles.titleRow}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="bold"
              flex={1}
              numberOfLines={2}
            >
              {product.name}
            </ResponsiveText>
            <CustomerProductFavorite />
          </View>

          <Customer_ProductDescription description={product.description} />

          <View style={styles.infoRow}>
            <Customer_ProductSizeDropdown
              product={product}
              size={selectedVariant}
              onSelectSize={handleSizeSelect}
            />
          </View>
          {/* <View style={styles.infoRow}>
       <ResponsiveText color={getTextColor()} size={4} weight="bold">
        {AppText.STOCK}
       </ResponsiveText>
   
          </View> */}

          <CustomerProductQuantity
            price={currentPrice}
            onQuantityChange={handleQuantityChange}
          />

          <CustomerAddToCartButton
            product={product}
            quantity={quantity}
            selectedVariant={selectedVariant}
            onPress={handleAddToCart}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Add_To_Cart_Customer;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  detailsContainer: {
    padding: wp(4),
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
});