import { StyleSheet, View, SafeAreaView, KeyboardAvoidingView, Platform } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import { globalpath } from '../../../Custom/globalpath'
import { colors } from '../../../Custom/Colors'
import { wp, hp } from '../../../Custom/Responsiveness'
import CustomerProductGrid from '../../../Components/CustomerComponents/ProductComponents/CustomerProductGrid'
import CustomerProductSearch from '../../../Components/CustomerComponents/ProductComponents/CustomerProductSearch'
import { Product_mock } from '../../../Mocks/Product_mock'
import { useFocusEffect } from '@react-navigation/native'
import { list_product_category, get_add_to_cart } from '../../../Services/API/Endpoints/Customer/Product'
import { useDispatch } from 'react-redux'
import { setCartItems } from '../../../Redux/Slices/cartSlice'

const Find_Products = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();
  const [searchText, setSearchText] = useState('');
  const dispatch = useDispatch();

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchCartData = async () => {
    try {
      console.log('Fetching cart data...');
      const response = await get_add_to_cart();
      console.log('Cart response:', response);
      
      if (response && response.length > 0) {
        const cartData = response[0];
        console.log("cartData in find products", cartData.items.length)
        // Calculate total items by summing up quantities
        // const totalQuantity = cartData.items.reduce((sum, item) => sum + (item.quantity || 0), 0);
        const totalQuantity = cartData.items.length;

        console.log('Total quantity calculated:', totalQuantity);
        // Dispatch to Redux store
        dispatch(setCartItems(totalQuantity));
      } else {
        console.log('No cart data found, setting total items to 0');
        dispatch(setCartItems(0));
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      dispatch(setCartItems(0));
    }
  };

  const fetchProducts = async () => {
    try {
      if (!refreshing) setLoading(true);
      const response = await list_product_category();
      console.log('API response:', response);
      setProducts(response || []);
    } catch (error) {
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchProducts();
      fetchCartData(); // Fetch cart data when screen comes into focus
    }, [])
  );

  useEffect(() => {
    console.log('Updated products:', products);
  }, [products]);

console.log("the product name is the = = == === = = ==",products[0]?.name)

  const onRefresh = () => {
    setRefreshing(true);
    fetchProducts();
  };

  const handleProductPress = (product) => {
    // Handle product press
    console.log('Product pressed:', product);
  };

  const filteredProducts = products.filter(product =>
    product.name?.toLowerCase().includes(searchText.toLowerCase())
  );
console.log('the filtered data is the -------',filteredProducts)
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <Customer_Header title={AppText.FIND_PRODUCTS} showCartIcon={true} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{ flex: 1 }}>
        <View style={styles.content}>
          <View style={styles.searchRow}>
            <CustomerProductSearch
              searchText={searchText}
              setSearchText={setSearchText}
              
            />
          </View>
          <CustomerProductGrid
  data={filteredProducts}
  onProductPress={handleProductPress}
  onRefresh={onRefresh} // ✅ Pass the refresh function
/>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default Find_Products

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
})