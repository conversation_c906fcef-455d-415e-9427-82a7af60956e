import React, { useState } from 'react';
import { StyleSheet, View, TextInput, KeyboardAvoidingView, Platform, FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import CustomerProductCard from '../../../Components/CustomerComponents/ProductComponents/CustomerProductCard';
import { Product_Details_mock } from '../../../Mocks/Product_mock';
import CustomerProductSearch from '../../../Components/CustomerComponents/ProductComponents/CustomerProductSearch';
import CustomerProductFilter from '../../../Components/CustomerComponents/ProductComponents/CustomerProductFilter';

const Find_Products_Details = ({ route, navigation }) => {
  const { product } = route.params;
  console.log("Recived SubcategoryProducts in the response is =========",product)
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [searchText, setSearchText] = useState('');

  const handleProductPress = (product) => {
    // Handle product press - navigate to product details
    console.log('Product pressed:', product);
  };

  const handleFilterPress = () => {
    // Handle filter press - navigate to filter screen
    navigation.navigate('Products_Filter');
  };

  const filteredProducts = product.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const renderProductItem = ({ item }) => {
    const productPrice = item.price;
    console.log("The price is ", productPrice);
    
    let imagesToShow = [];
  
    const exactMatchVariant = item.variants.find(variant => {
      if (variant.price_override === productPrice) {
        console.log(`✅ Price Matched: productPrice = ${productPrice}, variant.price_override = ${variant.price_override}`);
        return true;
      } else {
        console.log(`❌ Price Not Matched: productPrice = ${productPrice}, variant.price_override = ${variant.price_override}`);
        return false;
      }
    });
  
    if (exactMatchVariant?.variant_images?.length > 0) {
      imagesToShow = exactMatchVariant.variant_images;
      console.log('✅ Exact price match found. Showing variant_images:', imagesToShow.map(img => img.image));
    } 
    else if (
      item.variants.some(variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0)
    ) {
      const firstWithImages = item.variants.find(
        variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0
      );
      imagesToShow = firstWithImages.variant_images;
      console.log('📦 No exact price match, but found variant with images.');
      console.log('🖼️ Variant Images Used:', imagesToShow.map(img => img.image));
    } 
    else {
      imagesToShow = item.product_images;
      console.log('⚠️ No variant images found. Using product_images.');
      console.log('🖼️ Product Images Used:', imagesToShow.map(img => img.image));
    }
  
    return (
      <View style={styles.productCardContainer}>
        <CustomerProductCard
          product={item}
          images={imagesToShow} // ✅ Passing the correct image array
          onPress={() => handleProductPress(item)}
        />
      </View>
    );
  };
  

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Customer_Header title={product.title} leftIconType="back" showCartIcon={true} />
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : ''} style={styles.container}>
        <View style={styles.content}>
          <View style={styles.searchRow}>
            <CustomerProductSearch
              searchText={searchText}
              setSearchText={setSearchText}
            />
            {/* <CustomerProductFilter onPress={handleFilterPress} /> */}
          </View>

          <FlatList
            data={filteredProducts}
            renderItem={renderProductItem}
            keyExtractor={item => item.id.toString()}
            numColumns={2}
            contentContainerStyle={styles.productsGrid}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Find_Products_Details;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  productsGrid: {
    padding: wp(2),
  },
  productCardContainer: {
    width: '50%',
    padding: wp(1),
  },
});