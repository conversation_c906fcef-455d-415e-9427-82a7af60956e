import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { wp, hp } from '../../../Custom/Responsiveness';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import { removeFromCart, updateQuantity, setCartItems, setFullCartData, updateItemQuantity, removeCartItem, clearCart, selectCartData } from '../../../Redux/Slices/cartSlice';
import Cart_CustomerProductQuantity from '../../../Components/CustomerComponents/ProductComponents/Cart_CustomerProductQuantity';
import { get_add_to_cart, add_item_in_cart, remove_item_in_cart, order_plac_to_cart, delete_cart_item } from '../../../Services/API/Endpoints/Customer/Product';
import Loader from '../../../Custom/loader';
import { list_customer_Address } from '../../../Services/API/Endpoints/Customer/Add_New_Address';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { payment_session_checkout } from '../../../Services/API/Endpoints/Customer/BookAppointment';
import { Linking } from 'react-native';

const Main_Add_Cart = ({ navigation }) => {
  const AppText = useAppText();
  const { backgroundColor, getTextColor, getDark_Theme } = useTheme();
  const dispatch = useDispatch();
  const cartData = useSelector(selectCartData);
  console.log("cartData is now received in the main add cart screen :::::::::",cartData)
  const [refreshing, setRefreshing] = useState(false);
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [cartId, setCartId] = useState(null);
  const [customerAddresses, setCustomerAddresses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);


  
  

  // Fetch cart data
  const fetchCartData = async () => {
    setIsLoading(true);
    try {
      const response = await get_add_to_cart();
      console.log("res of fetch cart data",response)


      if (response && response.length > 0) {
        setCartId(response[0]?.items[0]?.cart);
        dispatch(setFullCartData(response[0]));
      } else {
        dispatch(setFullCartData({ items: [], total_price: 0 }));
      }

    } catch (error) {
      console.error('❌ Error fetching cart:', error);
      Alert.alert('Error', 'Failed to fetch cart items. Please try again.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchCartData();
  }, []);

  const handleQuantityChange = async (itemId, newQuantity, isIncrement) => {
    // Find current item
    const currentItem = cartData.items.find(item => item.id === itemId);
    if (!currentItem) return;

    // Calculate new quantity
    const updatedQuantity = currentItem.quantity + (isIncrement ? 1 : -1);
    
    // Don't calculate price locally - it will come from the API
    try {
      const payload = { cart_item: itemId };
      const response = await (isIncrement ? add_item_in_cart(payload) : remove_item_in_cart(payload));
      
      // Update with the price from API response
      if (response && response.item_price) {
        dispatch(updateItemQuantity({ 
          itemId, 
          quantity: response.quantity,
          newPrice: response.item_price
        }));
      }
    } catch (error) {
      // Handle specific error messages
      const errorMessage = error.response?.data?.error || 'Failed to update quantity';
      Alert.alert('Alert', errorMessage);
      
      // Revert optimistic update
      fetchCartData();
    }
  };

  const handleRemoveItem = async (itemId) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: async () => {
            // Optimistically update UI
            dispatch(removeCartItem(itemId));
            
            try {
              await delete_cart_item(itemId);
            } catch (error) {
              const errorMessage = error.response?.data?.error || 'Failed to remove item';
              Alert.alert('Alert', errorMessage);
              // Revert optimistic update
              fetchCartData();
            }
          }
        }
      ]
    );
  };

  const handleCheckout = async () => {
    if (!cartId || !cartData.items.length) {
      Alert.alert('Error', 'No items in cart');
      return;
    }

    try {
      setCheckoutLoading(true);
      
      // Get customer ID
      const customerDataString = await AsyncStorage.getItem('customerData');
      const customerDataArray = JSON.parse(customerDataString);
      const customerData = customerDataArray[0];
      console.log("customerData is now received in the main add cart screen :::::::::",customerData)
      const customerId = customerData?.id;
      const full_naame = customerData?.full_name;
      const email = customerData?.email;
      const userId = customerData?.user;


      if (!customerId) {
        // Alert.alert('Error', 'Customer information not found');
        Alert.alert(
          'No Shipping Address',
          'You haven\'t added any shipping address. Would you like to add one now?',
          [
            {
              text: 'Add Address',
              onPress: () => navigation.navigate('Add_New_Addres'), // Navigate to your address adding screen
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        return;
      }

      // Fetch customer addresses
      const addressResponse = await list_customer_Address(customerId);
      
      // Check if customer has any addresses
      if (!addressResponse || !Array.isArray(addressResponse) || addressResponse.length === 0) {
        Alert.alert(
          'No Shipping Address',
          'You haven\'t added any shipping address. Would you like to add one now?',
          [
            {
              text: 'Add Address',
              onPress: () => navigation.navigate('Add_New_Addres'), // Navigate to your address adding screen
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        return;
      }

      // Get the first address
      const shippingAddress = addressResponse[0];

      Alert.alert(
        'Confirm Order',
        `Proceed with shipping to:\n${shippingAddress.title}\n${shippingAddress.address}`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Proceed',
            onPress: async () => {
              try {
                // const payload = {
                //   cart: cartId,
                //   total_price: cartData.total_price,
                //   shipping_address: shippingAddress.address,
                //   payment_id: "pending"
                // };
                    const payload = {
                  type: "ecommerce",
                  cart_id: cartId,
                  EmailId: email,
                  UserName: full_naame,
                  UserId: userId,
                  CustomerId: customerId,
                  Order: ""
                };
                console.log("payload of order place to cart", payload)
                
                // await order_plac_to_cart(payload);
               const response =  await payment_session_checkout(payload);
                 if (response?.url && response.status === 200) {
                     Linking.openURL(response.url);
                   } else {
                     console.warn('❌ Stripe URL missing or response invalid:', response);
                   }


                dispatch(clearCart());
                navigation.navigate('OrderSuccess');
              } catch (error) {
                const errorMessage = error.response?.data?.error || 'Failed to place order';
                Alert.alert('Error', errorMessage);
                fetchCartData();
              }
            },
          },
        ]
      );
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to process checkout';
      Alert.alert('Error', errorMessage);
    } finally {
      setCheckoutLoading(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchCartData();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Customer_Header title={AppText.CART} leftIconType="back" showCartIcon={false} />

      {isLoading ? (
        <View style={styles.loaderContainer}>
          <Loader />
        </View>
      ) : cartData.items?.length > 0 ? (
        <>
          <ScrollView 
            style={styles.content}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.Light_theme_maincolour]}
              />
            }
          >
            {cartData.items.map((item) => (
              <View key={item.id.toString()} style={[styles.itemContainer, { borderBottomColor: getDark_Theme() }]}>
                <View style={styles.itemImageContainer}>
                  <Icon 
                    source={item.product_image ? { uri: item.product_image } : globalpath.logo} 
                    size={wp(20)} 
                  />
                </View>
                <View style={styles.itemDetails}>
                  <View style={styles.titleRow}>
                    <ResponsiveText color={getTextColor()} size={4} weight="bold" numberOfLines={2} maxWidth={wp(58)}>
                      {item.product_name}
                    </ResponsiveText>
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => handleRemoveItem(item.id)}
                    >
                      <Icon source={globalpath.delete_icon} 
                      size={wp(4.4)} 
                      tintColor={colors.red}
                      margin={[0,0,0,0]}
                      />
                    </TouchableOpacity>
                  </View>
                  <ResponsiveText color={colors.Light_theme_maincolour} size={4} weight="bold" style={styles.price}>
                    ${parseFloat(item.item_price).toFixed(2)}
                  </ResponsiveText>
                  <Cart_CustomerProductQuantity
                    price={parseFloat(item.item_price) / item.quantity}
                    onQuantityChange={(newQuantity, isIncrement) => handleQuantityChange(item.id, newQuantity, isIncrement)}
                    initialQuantity={item.quantity}
                    onRemove={() => handleRemoveItem(item.id)}
                  />
                </View>
              </View>
            ))}
          </ScrollView>
          
          <View style={[styles.footer, { borderTopColor: getDark_Theme() }]}>
            <View style={styles.totalContainer}>
              <ResponsiveText color={getTextColor()} size={4.5} weight="bold">
                {AppText.TOTAL}
              </ResponsiveText>
              <ResponsiveText color={colors.Light_theme_maincolour} size={4.5} weight="bold" numberOfLines={1} maxWidth={wp(30)}>
                ${cartData.total_price.toFixed(2)}
              </ResponsiveText>
            </View>
            <TouchableOpacity
              style={[
                styles.checkoutButton,
                { backgroundColor: colors.Light_theme_maincolour },
                checkoutLoading && styles.disabledButton
              ]}
              onPress={handleCheckout}
              disabled={checkoutLoading || !cartData.items?.length}
            >
              {checkoutLoading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                  {AppText.GO_TO_CHECKOUT}
                </ResponsiveText>
              )}
            </TouchableOpacity>
          </View>
        </>
      ) : (
        <ScrollView
          contentContainerStyle={{ flex: 1, alignItems: "center", justifyContent: "center" }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.Light_theme_maincolour]}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          <ResponsiveText color={colors.grey} size={4}>
            {AppText.NO_ITEMS_IN_YOUR_CARD}
          </ResponsiveText>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default Main_Add_Cart;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemContainer: {
    flexDirection: 'row',
    padding: wp(4),
    borderBottomWidth: 1,
    alignItems: 'flex-start',
  },
  itemImageContainer: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(2),
    overflow: 'hidden',
    marginRight: wp(3),
  },
  itemDetails: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
    // width: wp(62),
    // backgroundColor:"pink",
  },
  price: {
    marginBottom: hp(1),
  },
  removeButton: {
    padding: wp(2),
    marginTop:hp(0.5)
  },
  footer: {
    padding: wp(4),
    borderTopWidth: 1,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(2),
  },
  checkoutButton: {
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.7,
  },
    removeButton: {
   backgroundColor:colors.lightGrey,
  //  marginHorizontal:wp(10),
   paddingHorizontal:wp(2),
   paddingVertical:hp(1),
   borderRadius:wp(7),
  },
});