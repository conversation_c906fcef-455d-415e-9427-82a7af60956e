import { StyleSheet, View, FlatList, SafeAreaView, RefreshControl, TextInput } from 'react-native'
import React, { useState, useCallback, useEffect } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import Book_Now_Service_Card from '../../../Components/CustomerComponents/Book_Now_Components/Book_Now_Service_Card'
import Book_Now_Details_Modal from '../../../Components/CustomerComponents/Book_Now_Components/Book_Now_Details_Modal'
import { wp, hp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import useTheme from '../../../Redux/useTheme'
import { get_all_service } from '../../../Services/API/Endpoints/Admin/Services'
import Loader from '../../../Custom/loader'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'

const Details_Book_Now = ({ route }) => {
  const AppText = useAppText();
  const { getTextColor, backgroundColor, getDark_Theme } = useTheme();
  const [selectedService, setSelectedService] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { salon } = route.params;

  const fetchServices = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const response = await get_all_service();
      console.log('🟢 Services fetched:', response);
      if (Array.isArray(response)) {
        const sortedServices = response.sort((a, b) => b.id - a.id);
        setServices(sortedServices);
        setFilteredServices(sortedServices);
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleSearch = (text) => {
    setSearchQuery(text);
    if (text) {
      const filtered = services.filter(service =>
        service.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredServices(filtered);
    } else {
      setFilteredServices(services);
    }
  };

  useEffect(() => {
    setFilteredServices(services);
  }, [services]);

  const onRefresh = useCallback(() => {
    fetchServices();
    setSearchQuery('');
  }, []);

  useEffect(() => {
    fetchServices();
  }, []);

  const handleServicePress = (service) => {
    setSelectedService(service);
  };

  const handleSeeDetails = (service) => {
    setSelectedService(service);
    setModalVisible(true);
  };

  const renderServiceCard = ({ item }) => (
    <Book_Now_Service_Card
      item={{
        id: item.id,
        service_image: item.service_image ? { uri: item.service_image } : undefined,
        title: item.name,
        description: item.description,
        duration: `${item.duration} min`,
        amount: item.price,
      }}
      isSelected={selectedService?.id === item.id}
      onPress={() => handleServicePress(item)}
      onSeeDetails={() => handleSeeDetails(item)}
      salon={salon}
    />
  );

  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]}>   
      <View style={styles.container}>
        <Customer_Header title={AppText.SERVICES} leftIconType='back' showBellIcon={false} />
        <ResponsiveText size={4.5} weight={'600'} color={getTextColor()} margin={[hp(2),0,wp(1),wp(4)]}>
          {AppText.OUR_SERVICES}
        </ResponsiveText>

        <View style={styles.searchContainer}>
          <View style={[styles.searchInputContainer, {borderColor: getDark_Theme()}]}>
            <Icon source={globalpath.search} size={wp(4)} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_SERVICES}
              // placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
            />
          </View>
        </View>

        <FlatList
          data={filteredServices}
          renderItem={renderServiceCard}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.Light_theme_maincolour]}
              tintColor={colors.Light_theme_maincolour}
            />
          }
          ListEmptyComponent={() => (
            <View style={styles.noResults}>
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.NO_RESULTS_FOUND}
              </ResponsiveText>
            </View>
          )}
        />
        <Book_Now_Details_Modal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          service={selectedService}
        />
      </View>
      {loading && <Loader />}
    </SafeAreaView> 
  )
}

export default Details_Book_Now

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: wp(3),
    marginTop: hp(2),
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
    height: hp(5),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  listContent: {
    padding: wp(3),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
    height: hp(55)
  }
})