import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import {useNavigation, useRoute} from '@react-navigation/native';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import DateSelection from '../../../Components/CustomerComponents/Book_Now_Components/DateSelection';
import BarberSelection from '../../../Components/CustomerComponents/Book_Now_Components/BarberSelection';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SelectedServicesList from '../../../Components/CustomerComponents/Book_Now_Components/SelectedServicesList';
import {
  getAvailableBarbers,
  getbarbarsAvailability,
} from '../../../Services/API/Endpoints/Customer/BookAppointment';
import Loader from '../../../Custom/loader';

const Book_Now_Buy = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {getTextColor, backgroundColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const {service, salon} = route.params;
  const [selectedMonth, setSelectedMonth] = useState('');
  const [selectedServices, setSelectedServices] = useState([]);
  const [selectedBarber, setSelectedBarber] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [barbarsData, setBarbarsData] = useState([]);
  const [barbarsAvailibilityData, setBarbarsAvailibilityData] = useState({});
  const [selectedDate, setSelectedDate] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [customerData,setCustomerData] = useState({})
  const barbarAvailibility = async () => {
    console.log('selectedMonth in barbar availability', selectedMonth);

    const params = {
      barber_id: selectedBarber?.id,
      month: Number(selectedMonth.split('-')[1]),
      year: Number(selectedMonth.split('-')[0]),
      is_booked: false,
    };

    console.log('params in barbar availability', params);
    setIsLoading(true);
    try {
      const response = await getbarbarsAvailability(params); // ✅ Await here

      // setIsLoading(false)
      setBarbarsAvailibilityData(response);
    } catch (error) {
      console.error('error fetching barbar availability', error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    setIsLoading(false);
    if (!selectedBarber?.id) return;
    
    if (selectedMonth) {
      console.log('onMonthSelect-', selectedMonth);
      barbarAvailibility();
    }
  }, [selectedMonth, selectedBarber]);

  useEffect(() => {
    console.log('🟢 Service Data in Book_Now_Buy:', service);
    console.log('🟢 Book_now Data in Book_Now_Buy:', salon);
    console.log('🟢 selectedServices Data in Book_Now_Buy:-', selectedServices);

    // Initialize with the service from route params if it exists
    if (service) {
      setSelectedServices([service]);
    }
  }, [service]);
  useEffect(() => {
    const getCustomerData = async () => {
      try {
        const jsonValue = await AsyncStorage.getItem('customerData');
        if (jsonValue != null) {
          const parsedData = JSON.parse(jsonValue);
          setCustomerData(parsedData);
          console.log('📦 Retrieved customer data:', parsedData);
        } else {
          console.log('No customer data found');
        }
      } catch (e) {
        console.error('❌ Error reading customer data from AsyncStorage:', e);
      }
    };

    getCustomerData();
  }, []);
  const getAvailableBarber = async () => {
    try {
      if (!selectedServices.length || !selectedServices[0]?.id || !salon?.id) {
        return;
      }
      const params = {
        shop: salon.id,
        service: selectedServices[0].id,
      };
      console.log('params of get barbers', params);
      const response = await getAvailableBarbers(params);
      setBarbarsData(response);
      setSelectedBarber(response[0]);
    } catch (error) {
      console.log('error in get barbers', error);
    }
  };
  useEffect(() => {
    if (selectedServices.length > 0) {
      getAvailableBarber();
    }
  }, [selectedServices]);
  const handleMonthSelect = date => {
    setSelectedMonth(date);
    console.log('Selected Month:', date);
  };
  const handleDateSelect = date => {
    console.log('Selected Date:===??', date);
    setSelectedDate(date);
  };
  const handleServicesUpdate = newServices => {
    setSelectedServices(newServices);
  };

  const handleBarberSelect = barber => {
    setSelectedBarber(barber);
    console.log('Selected Barber:============', barber);
  };

  const handleTimeSlotSelect = ({selectedTimeSlot}) => {
    console.log('Time slot received in parent:', selectedTimeSlot);
    setSelectedTimeSlot(selectedTimeSlot);
  };
  const handleGetDirections = () => {
    // Handle get directions functionality
    console.log('Get Directions clicked');
  };

  const handleProceed = () => {
    // Handle proceed functionality
    console.log('Proceed clicked with data:', {
      selectedServices,
      selectedMonth,
      selectedBarber,
      selectedTimeSlot,
      salon,
      selectedDate,
      customerData
    });
    navigation.navigate('Proceed_book_now', {
      selectedServices,
      selectedMonth,
      selectedBarber,
      selectedTimeSlot,
      salon,
      selectedDate,
      customerData
    });
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: backgroundColor}]}>
      <Customer_Header
        title={AppText.BOOK_APPOINTMENT}
        leftIconType="back"
        showBellIcon={false}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.salonHeader}>
          <Image
            source={
              salon.shop_image ? {uri: salon.shop_image} : globalpath.logo
            }
            style={styles.salonImage}
          />
          <View style={styles.salonTitleContainer}>
            <ResponsiveText
              size={5.5}
              // weight={'600'}
              color={colors.white}
              numberOfLines={2}>
              {salon.name}
            </ResponsiveText>
          </View>
        </View>

        <SelectedServicesList
          selectedServices={selectedServices}
          onServicesUpdate={handleServicesUpdate}
        />

        <DateSelection
          onMonthSelect={handleMonthSelect}
          onTimeSlotSelect={handleTimeSlotSelect}
          availabilityData={barbarsAvailibilityData}
          onDateSelected={handleDateSelect}
        />

        <BarberSelection
          onBarberSelect={handleBarberSelect}
          barbersData={barbarsData}
        />
        {/* <OtherBarbers />
        <SuggestedBarbers /> */}

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.directionsButton,
              {borderColor: getDark_Theme(), backgroundColor},
            ]}
            onPress={handleGetDirections}>
            <Icon
              source={globalpath.location}
              size={wp(4)}
              tintColor={colors.darkGrey}
            />
            <ResponsiveText
              color={colors.darkGrey}
              size={3.5}
              weight="500"
              margin={[0, 0, 0, wp(1)]}>
              {AppText.GET_DIRECTIONS}
            </ResponsiveText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.proceedButton}
            onPress={handleProceed}>
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.PROCEED}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {isLoading ? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default Book_Now_Buy;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  salonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    backgroundColor: '#212121',
    borderRadius: wp(2),
    marginBottom: hp(2),
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  salonImage: {
    width: wp(25),
    height: wp(25),
    borderRadius: wp(2),
    resizeMode: 'cover',
  },
  salonTitleContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(1),
  },
  serviceInfo: {
    marginTop: hp(2),
  },
  serviceCard: {
    backgroundColor: colors.white,
    borderRadius: wp(2),
    padding: wp(3),
    marginTop: hp(1),
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  serviceTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    borderRadius: wp(1),
  },
  serviceDetails: {
    marginTop: hp(1),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  buttonContainer: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    paddingHorizontal: wp(3),
    // marginTop: hp(3),
    // marginBottom: hp(2),
    marginVertical: hp(2),
  },
  directionsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.8),
    borderRadius: wp(2),
    borderWidth: 1,
    // flex: 1,
    marginRight: wp(2),
    // backgroundColor:"pink",
    justifyContent: 'center',
    marginBottom: hp(2),
  },
  proceedButton: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.8),
    borderRadius: wp(2),
    // flex: 1,
    alignItems: 'center',
  },
});
