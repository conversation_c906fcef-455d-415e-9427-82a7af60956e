import {
  StyleSheet,
  SafeAreaView,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import {hp, wp} from '../../../Custom/Responsiveness';
import {colors} from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';

const Pay_Now_Checkout = ({route, navigation}) => {
  const {getTextColor, backgroundColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const {
    selectedServices,
    selectedDate,
    selectedTimeSlot,
    selectedBarber,
    salon,
    selectedMonth,
  } = route.params;
  const handlePress = () => {
    // Your logic on button press
    console.log('Pay Now Pressed');
    navigation.navigate('Pay_Now', {
        selectedServices,
        selectedMonth,
        selectedBarber,
        selectedTimeSlot,
        salon,
        selectedDate,
      });
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <Customer_Header
        title={AppText.BOOK_APPOINTMENT}
        leftIconType="back"
        showBellIcon={false}
      />
      <View style={styles.content}>
        <Text style={[styles.messageText, {color: getTextColor()}]}>
          {AppText.CHECK_OUT_PAYMENT_COMINGSOON}
        </Text>

        <TouchableOpacity style={[styles.paymentButton, styles.payNowButton]} onPress={handlePress}>
          <ResponsiveText size={4} weight={'500'} color={colors.white}>
            {AppText.CHECKOUT}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default Pay_Now_Checkout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(5),
  },
  messageText: {
    fontSize: wp(4),
    textAlign: 'center',
    marginBottom: hp(3),
    fontWeight: '500',
  },
  paymentButton: {
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: wp(1),
    paddingVertical: hp(1.2),
    paddingHorizontal: wp(3.8),
  },
  payNowButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});
