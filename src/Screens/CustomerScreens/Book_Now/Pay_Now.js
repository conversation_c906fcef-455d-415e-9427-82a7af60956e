import {
  StyleSheet,
  View,
  <PERSON>rollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import {useNavigation, useRoute} from '@react-navigation/native';
import {wp, hp} from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import PayNowHeader from '../../../Components/CustomerComponents/Book_Now_Components/PayNowHeader';
import PayNowDateTime from '../../../Components/CustomerComponents/Book_Now_Components/PayNowDateTime';
import PayNowSpecialist from '../../../Components/CustomerComponents/Book_Now_Components/PayNowSpecialist';
import PayNowServices from '../../../Components/CustomerComponents/Book_Now_Components/PayNowServices';
import PayNowSummary from '../../../Components/CustomerComponents/Book_Now_Components/PayNowSummary';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';

const Pay_Now = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {getTextColor, backgroundColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const {
    selectedServices,
    selectedDate,
    selectedTimeSlot,
    selectedBarber,
    salon,
    selectedMonth,
  } = route.params;
  console.warn("🚀 ~ file: Pay_Now.js:59 ~ Pay_Now ~ salon:", selectedServices)
  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <Customer_Header
        title={AppText.PAY_NOW}
        leftIconType="back"
        showBellIcon={false}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <PayNowHeader />
        <View style={[styles.detailsCard, {backgroundColor: '#E3FEFF'}]}>
          <PayNowDateTime
            date={selectedDate}
            startTime={selectedTimeSlot}
          />
          <PayNowSpecialist
            specialist={selectedBarber}
            duration="1 hour"
          />
          <PayNowServices services={selectedServices} />
          <PayNowSummary
            services={selectedServices}
            discount={5}
          />
        </View>
        <View
          style={[
            styles.buttonContainer,
            {borderColor: getDark_Theme(), backgroundColor},
          ]}>
          <TouchableOpacity
            style={[styles.button, styles.backButton]}
            onPress={() => navigation.navigate('Bookings')}>
            <ResponsiveText size={4} weight={'500'} color={colors.white}>
              {AppText.VIEW}
            </ResponsiveText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.downloadButton,
              {borderColor: getDark_Theme()},
            ]}>
            <ResponsiveText size={4} weight={'500'} color={getTextColor()}>
              {AppText.DOWNLOAD}
            </ResponsiveText>
            <Icon
              source={globalpath.download}
              size={wp(4.2)}
              tintColor={getTextColor()}
              margin={[0, 0, 0, wp(2)]}
            />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Pay_Now;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  detailsCard: {
    borderRadius: wp(1.5),
    padding: wp(4),
    marginTop: hp(1),
    marginHorizontal: wp(2),
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: wp(1.8),
    // borderTopWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(4),
  },
  button: {
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: wp(2),
    paddingVertical: hp(1.2),
    paddingHorizontal: wp(3.8),
  },
  backButton: {
    backgroundColor: colors.c_green,
    paddingHorizontal: wp(8),
  },
  downloadButton: {
    borderWidth: 1,
  },
});
