import {
  StyleSheet,
  View,
  <PERSON>rollView,
  SafeAreaView,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import {useNavigation, useRoute} from '@react-navigation/native';
import {wp, hp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import useTheme from '../../../Redux/useTheme';
import RelatedProducts from '../../../Components/CustomerComponents/ProductComponents/RelatedProducts';
import {Product_Details_mock} from '../../../Mocks/Product_mock';
import DateTimeDisplay from '../../../Components/CustomerComponents/Book_Now_Components/DateTimeDisplay';
import ServiceListHeader from '../../../Components/CustomerComponents/Book_Now_Components/ServiceListHeader';
import SelectedServicesDisplay from '../../../Components/CustomerComponents/Book_Now_Components/SelectedServicesDisplay';
import CouponInput from '../../../Components/CustomerComponents/Book_Now_Components/CouponInput';
import PaymentSummary from '../../../Components/CustomerComponents/Book_Now_Components/PaymentSummary';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import {
  checkBarbarAvailability,
  customerAppointment,
  payment_session_checkout,
} from '../../../Services/API/Endpoints/Customer/BookAppointment';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Linking } from 'react-native';

const Proceed_book_now = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {
    selectedServices,
    selectedDate,
    selectedTimeSlot,
    selectedBarber,
    salon,
    selectedMonth,
    customerData,
  } = route.params;
  const {getTextColor, backgroundColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const [appliedCoupon, setAppliedCoupon] = useState(null);

  useEffect(() => {
    // Log all the received data
    console.log('🟢 Selected Services:', selectedServices);
    console.log('🟢 Selected Date:', selectedDate);
    console.log('🟢 Selected Barber:', selectedBarber);
    console.log('🟢 Selected Time Slot:', selectedTimeSlot);
    console.log('🟢 Salon Details:', salon);
    console.log('🟢 selectedMonth:', selectedMonth);
    console.log('🟢 customerData:', customerData[0]?.id);
  }, [route.params]);

  const serviceIds = selectedServices.map(service => service.id);
  const handleProductPress = product => {
    // Handle product press here
    console.log('Product pressed:', product);
  };

  const handleCouponApply = coupon => {
    // Handle coupon application here
    setAppliedCoupon(coupon);
  };

  const handlePayNow = () => {
    // Handle pay now action
    checkBarbarFittingDuration(false);
    console.log('Pay now clicked');
    // navigation.navigate('Pay_Now', {
    //   selectedServices: route.params?.selectedServices,
    //   selectedDate: route.params?.selectedDate,
    //   selectedTimeSlot: route.params?.selectedTimeSlot,
    //   selectedBarber: route.params?.selectedBarber,
    //   salon: route.params?.salon
    // })
  };

  const handlePayInSalon = () => {
    checkBarbarFittingDuration(true);
  };
  const checkBarbarFittingDuration = async (payInSaloon = false) => {
    try {
      const queryParams = {
        barber_id: selectedBarber?.id,
        date: selectedDate,
        start_time: selectedTimeSlot?.slice(0, 5),
      };

      const payload = {
        is_booked: true,
        service_ids: serviceIds,
      };
      console.log('queryParams', queryParams);
      console.log('payload', payload);
      const response = await checkBarbarAvailability(queryParams, payload);
      const successStatuses = [200, 201, 202];

      if (successStatuses.includes(response.status)) {
        MakeCustomerAppointment(payInSaloon);
      }
      console.log('response', response);
    } catch (error) {
      Alert.alert('Alert', error?.response?.data?.message);
    }
  };
  const MakeCustomerAppointment = async (payInSaloon = false) => {

    try {
      const userData = await AsyncStorage.getItem('userData');
      const data = JSON.parse(userData);
      console.log('data-->', data)
      const { user_id } = JSON.parse(userData);
      console.log('user_id-->', user_id)
      const {username} = JSON.parse(userData);
      console.log('username-->', username)
      const {email} = JSON.parse(userData);
      console.log('email id -->', email)

      // Calculate subtotal from selected services
      const subtotal = selectedServices.reduce((sum, service) => sum + parseFloat(service.price), 0);

      const payload = {
        type: "customer appointment",
        barber: selectedBarber?.id,
        customer: customerData[0]?.id,
        EmailId: email,
        UserName: username,
        shop: salon?.id,
        price: subtotal.toString(),
        services: serviceIds,
        date: selectedDate,
        status: "booked",
        start_time: selectedTimeSlot?.slice(0, 5),
        notes: "Your hair is so rough.",
  
      };

      console.log("CUSTOMER APPOINTMENT PAYLOAD ", payload)

      // const response = await customerAppointment(payload);
      const response = await payment_session_checkout(payload);
      console.log('response of payment session checkout', response);
       if (response?.url && response.status === 200) {
      Linking.openURL(response.url);
    } else {
      console.warn('❌ Stripe URL missing or response invalid:', response);
    }



      if (response) {
        if (payInSaloon) {
          navigation.navigate('Invoice', {
            selectedServices,
            selectedMonth,
            selectedBarber,
            selectedTimeSlot,
            salon,
            selectedDate,
          });
        } else {
          navigation.navigate('Pay_Now_Checkout', {
            selectedServices,
            selectedMonth,
            selectedBarber,
            selectedTimeSlot,
            salon,
            selectedDate,
          });
        }
      } else {
        Alert.alert('Alert', 'Something went wrong');
      }
    } catch (error) {}
  };
  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <Customer_Header
        title={AppText.BOOK_APPOINTMENT}
        leftIconType="back"
        showBellIcon={false}
      />
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: hp(5)}}>
        <View style={styles.salonHeader}>
          <Image
            source={
              salon.shop_image ? {uri: salon.shop_image} : globalpath.logo
            }
            style={styles.salonImage}
          />
          <View style={styles.salonTitleContainer}>
            <ResponsiveText
              size={5.9}
              weight={'600'}
              color={colors.white}
              numberOfLines={2}>
              {salon.name}
            </ResponsiveText>
          </View>
        </View>
        <RelatedProducts
          products={Product_Details_mock}
          onProductPress={handleProductPress}
        />

        <DateTimeDisplay
          selectedDate={selectedDate}
          selectedTimeSlot={selectedTimeSlot}
        />

        <ServiceListHeader />

        <SelectedServicesDisplay selectedServices={selectedServices} />

        <CouponInput onApplyCoupon={handleCouponApply} />

        <PaymentSummary
          selectedServices={selectedServices}
          appliedCoupon={appliedCoupon}
        />
      </ScrollView>

      <View
        style={[
          styles.paymentButtonsContainer,
          {borderColor: getDark_Theme(), backgroundColor: backgroundColor},
        ]}>
        <TouchableOpacity
          style={[styles.paymentButton, styles.payNowButton]}
          onPress={handlePayNow}>
          <ResponsiveText size={4} weight={'500'} color={colors.white}>
            {AppText.PAY_NOW}
          </ResponsiveText>
          <Icon
            source={globalpath.wallet}
            size={wp(4.2)}
            tintColor={colors.white}
            margin={[0, 0, 0, wp(2)]}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.paymentButton,
            styles.payInSalonButton,
            {borderColor: getDark_Theme()},
          ]}
          onPress={handlePayInSalon}>
          <ResponsiveText size={4} weight={'500'} color={getTextColor()}>
            {AppText.PAY_IN_SALON}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default Proceed_book_now;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  salonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    backgroundColor: '#212121',
    borderRadius: wp(2),
    marginBottom: hp(2),
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  salonImage: {
    width: wp(25),
    height: wp(25),
    borderRadius: wp(2),
    resizeMode: 'cover',
  },
  salonTitleContainer: {
    flex: 1,
    marginLeft: wp(3),
  },
  paymentButtonsContainer: {
    flexDirection: 'row',
    padding: wp(1.8),
    // backgroundColor: backgroundColor,
    borderTopWidth: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    // justifyContent:'center',
  },
  paymentButton: {
    // flex: 1,
    // height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: wp(1),
    paddingVertical: hp(1.2),
    paddingHorizontal: wp(3.8),
  },
  payNowButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  payInSalonButton: {
    borderWidth: 1,
  },
});
