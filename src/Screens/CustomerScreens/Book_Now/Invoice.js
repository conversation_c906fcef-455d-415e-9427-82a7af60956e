import {
  StyleSheet,
  View,
  <PERSON>rollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import {useNavigation, useRoute} from '@react-navigation/native';
import {wp, hp} from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import InvoiceHeader from '../../../Components/CustomerComponents/Book_Now_Components/InvoiceHeader';
import InvoiceDateTime from '../../../Components/CustomerComponents/Book_Now_Components/InvoiceDateTime';
import InvoiceSpecialist from '../../../Components/CustomerComponents/Book_Now_Components/InvoiceSpecialist';
import InvoiceServices from '../../../Components/CustomerComponents/Book_Now_Components/InvoiceServices';
import InvoiceSummary from '../../../Components/CustomerComponents/Book_Now_Components/InvoiceSummary';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';

const Invoice = () => {
  const navigation = useNavigation();
  const route = useRoute();
  console.warn("🚀 ~ file: Invoice.js:23 ~ Invoice ~ route.params:", route.params)
  const {getTextColor, backgroundColor} = useTheme();
  const AppText = useAppText();

const {salon,selectedServices,selectedDate,selectedTimeSlot,selectedBarber,selectedMonth} = route.params
  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <Customer_Header
        title={AppText.INVOICE}
        leftIconType="back"
        showBellIcon={false}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <InvoiceHeader />
        <View style={[styles.detailsCard, {backgroundColor: '#FFF1D8'}]}>
          <InvoiceDateTime
            date={selectedDate}
            startTime={selectedTimeSlot}
          />
          <InvoiceSpecialist
            specialist={selectedBarber.full_name}
            duration="1 hour"
          />
          <InvoiceServices services={selectedServices} />
          <InvoiceSummary
            services={selectedServices}
            discount={5}
          />
        </View>
        <View style={[styles.buttonContainer, {backgroundColor}]}>
          <TouchableOpacity
            style={[styles.button, styles.backButton]}
            onPress={() => navigation.navigate("Bookings")}>
            <ResponsiveText size={4} weight={'500'} color={colors.white}>
              {AppText.VIEW}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Invoice;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  detailsCard: {
    borderRadius: wp(1.5),
    padding: wp(4),
    marginTop: hp(1),
    marginHorizontal: wp(2),
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: wp(1.8),
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: hp(4),
  },
  button: {
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    paddingVertical: hp(1.2),
    paddingHorizontal: wp(8),
  },
  backButton: {
    backgroundColor: colors.Light_theme_maincolour,
  },
});
