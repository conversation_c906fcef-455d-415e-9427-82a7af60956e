import {StyleSheet, View, SafeAreaView} from 'react-native';
import React from 'react';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Booking_ToggleButton_Filter from '../../../Components/Bookings/Booking_ToggleButton_Filter';

const Bookings = ({navigation}) => {
  const AppText = useAppText();
  const {backgroundColor} = useTheme();

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: backgroundColor}]}>
      <Customer_Header title={AppText.BOOKINGS} />
      <View style={styles.content}>
        <Booking_ToggleButton_Filter />
      </View>
    </SafeAreaView>
  );
};

export default Bookings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
});
