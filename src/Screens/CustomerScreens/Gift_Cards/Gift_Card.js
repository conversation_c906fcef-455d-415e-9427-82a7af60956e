import { StyleSheet, View, SafeAreaView, FlatList, TextInput, KeyboardAvoidingView, Platform, RefreshControl } from 'react-native'
import React, { useState, useEffect, useCallback } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import CustomerGiftCard from '../../../Components/CustomerComponents/Customer_Gift_Card_Components/CustomerGiftCard'
import { Gift_Mock } from '../../../Custom/mockData'
import { useFocusEffect, useNavigation } from '@react-navigation/native'
import { hp, wp } from '../../../Custom/Responsiveness'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'
import { colors } from '../../../Custom/Colors'
import ResponsiveText from '../../../Custom/RnText'
import { get_all_giftcard } from '../../../Services/API/Endpoints/Customer/GiftCard'

const Gift_Card = () => {
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const navigation = useNavigation();
  const [searchText, setSearchText] = useState('');
  const [filteredGiftCards, setFilteredGiftCards] = useState(Gift_Mock);
  const [allcardData, setAllCradData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const handleGiftCardPress = (item) => {
    navigation.navigate('View_Customer_Gift_Card', {item });
  };


 const onRefresh = useCallback(() => {
    getAllgiftcard();
        // setSearchQuery('');
      }, []);
    
      useFocusEffect(
        useCallback(() => {
          getAllgiftcard();
          onRefresh()
        }, []),
      );




   const getAllgiftcard = async () => {
          try {
              setLoading(true);
              setRefreshing(true);
      
              const response = await get_all_giftcard();
              console.log('🟢 Cards fetched:', response);
      
              if (Array.isArray(response)) {
                  const sortedServices = response.sort((a, b) => b.id - a.id);
                  setAllCradData(sortedServices);
              }
          } catch (error) {
              console.error('❌ Failed to fetch services:', error);
          } finally {
              setLoading(false);
              setRefreshing(false);
          }
      };




  const handleSearch = (text) => {
    setSearchText(text);
    if (text) {
      const filtered = Gift_Mock.filter(giftCard =>
        giftCard.title.toLowerCase().includes(text.toLowerCase()) ||
        giftCard.description.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredGiftCards(filtered);
    } else {
      setFilteredGiftCards(Gift_Mock);
    }
  };

  const renderGiftCardItem = ({ item }) => (
    <CustomerGiftCard
      item={item}
      onPress={() => handleGiftCardPress(item)}
    />
  );

  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]}>
      <Customer_Header title={AppText.GIFT_CARD} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex: 1}}>
        <View style={styles.content}>
          <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
            <Icon 
              source={globalpath.search} 
              size={20} 
              tintColor={getTextColor()} 
              margin={[0,wp(2),0,0]} 
            />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_CARD}
              placeholderTextColor={colors.grey}
              value={searchText}
              onChangeText={handleSearch}
            />
          </View>

          <FlatList
            data={Array.isArray(allcardData) ? allcardData : []}
            renderItem={renderGiftCardItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.giftCardsContainer}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.Light_theme_maincolour]} // Android
                tintColor={colors.Light_theme_maincolour} // iOS
              />}
            ListEmptyComponent={() => (
              <View style={styles.noResults}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {AppText.NO_RESULTS_FOUND}
                </ResponsiveText>
              </View>
            )}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default Gift_Card

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  searchContainer: {
    height: hp(5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    marginBottom: hp(2),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  giftCardsContainer: {
    flexGrow: 1,
    paddingBottom: hp(10),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: hp(20),
  }
})