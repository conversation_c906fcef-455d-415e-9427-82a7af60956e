import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, KeyboardAvoidingView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { hp, wp } from '../../../Custom/Responsiveness';
import CustomerCardImageCarousel from '../../../Components/CustomerComponents/Customer_Gift_Card_Components/CustomerCardImageCarousel';
import CustomerQuantitySelector from '../../../Components/CustomerComponents/Customer_Gift_Card_Components/CustomerQuantitySelector';
import CustomerGiftTypeDropdown from '../../../Components/CustomerComponents/Customer_Gift_Card_Components/CustomerGiftTypeDropdown';
import GiftCardDescription from '../../../Components/Gift_Cards/GiftCardDescription';
import CustomTextInput from '../../../Components/CustomTextInput';
import { get_gift_product_name } from '../../../Services/API/Endpoints/Admin/Giftcard';
import { globalpath } from '../../../Custom/globalpath';
import DynamicDropdown from '../../../Components/Services_Components/DynamicDropdown';
import { get_add_giftproduct_type } from '../../../Services/API/Endpoints/Admin/products';



const View_Customer_Gift_Card = ({ route, navigation }) => {
  const { item } = route.params;
  console.log("Item recieved is -----",item)
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [quantity, setQuantity] = useState(1);
  const [giftType, setGiftType] = useState(null);
  const [personalMessage, setPersonalMessage] = useState('');
  const [recipientEmail, setRecipientEmail] = useState('');

  const handleQuantityChange = (newQuantity) => {
    setQuantity(newQuantity);
  };

  const handleGiftTypeSelect = (type) => {
    setGiftType(type);
  };

  const handleBuyNow = () => {
    if (!item || !item.id) {
      console.error("Gift card item is missing or invalid.");
      return;
    }
  
    console.log('Buying gift card:', {
      giftCard: item,
      quantity,
      giftType,
      personalMessage,
      recipientEmail
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Customer_Header title={AppText.GIFT_CARD_DETAILS} leftIconType="back" />
     <View>
        <Image style={{height:hp(40),width:wp(80),resizeMode:'contain',alignSelf:"center"}}
              source={item.giftcard_image
                                    ? { uri: item.giftcard_image } // Remote image (from API)
                                    : globalpath.logo }
              
             /> 
     </View>
      <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : ''}
            style={{flex: 1}}>  
      <ScrollView style={styles.content}>
        <View style={styles.detailsContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={4}
            weight="bold"
            margin={[hp(2), 0, hp(1), wp(4)]}
          >
            {item.product_name}
          </ResponsiveText>

          <CustomerQuantitySelector 
            amount={parseInt(item.price)} 
            onQuantityChange={handleQuantityChange}
          />

          <GiftCardDescription description={item.description} />

        <View style={{marginHorizontal:wp(5)}}>
        <DynamicDropdown
                    title={AppText.PRODUCT_TYPE}
                    // placeholder={AppText.ENTER_YOUR_NAME}
                    value={giftType}
                    onChange={value => {
                        setGiftType(value)
                    
                    }}
                    fetchData={get_add_giftproduct_type}
                    // error={errors.product_type}
                  />
        </View>

          {/* <CustomerGiftTypeDropdown onSelect={handleGiftTypeSelect} /> */}

          {giftType === 'digital' && (
            <View style={styles.digitalGiftContainer}>
              <ResponsiveText
                color={getTextColor()}
                size={4}
                weight="bold"
                margin={[hp(1), 0, hp(2), wp(4)]}
              >
                {AppText.SET_AS_GIFT}
              </ResponsiveText>

              <CustomTextInput
                label={AppText.PERSONAL_MESSAGE}
                placeholder={AppText.ENTER_PERSONAL_MESSAGE}
                value={personalMessage}
                onChangeText={setPersonalMessage}
                multiline
                numberOfLines={4}
                containerStyle={styles.inputContainer}
              />

              <CustomTextInput
                label={AppText.RECIPIENTS_EMAIL}
                placeholder={AppText.ENTER_RECIPIENTS_EMAIL}
                value={recipientEmail}
                onChangeText={setRecipientEmail}
                keyboardType="email-address"
                containerStyle={styles.inputContainer}
              />
            </View>
          )}

          <TouchableOpacity 
            style={[styles.buyButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={handleBuyNow}
          >
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.BUY_NOW}
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default View_Customer_Gift_Card;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  detailsContainer: {
    paddingBottom: hp(4),
  },
  digitalGiftContainer: {
    marginTop: hp(2),
  },
  inputContainer: {
    marginHorizontal: wp(4),
    marginBottom: hp(2),
  },
  buyButton: {
    marginHorizontal: wp(4),
    marginTop: hp(4),
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
});