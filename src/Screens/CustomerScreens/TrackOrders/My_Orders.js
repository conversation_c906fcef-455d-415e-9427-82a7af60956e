import { StyleSheet, View, SafeAreaView } from 'react-native'
import React from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import Customer_Order from './Customer_Order'
import Customer_History from './Customer_History'
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs'
import { colors } from '../../../Custom/Colors'
import { useRoute } from '@react-navigation/native'

const Tab = createMaterialTopTabNavigator()
const My_Orders = () => {
  
  const AppText = useAppText();
  const { backgroundColor, getTextColor } = useTheme();
  const route = useRoute();
  
  // Get the from parameter from the route
  const from = route.params?.from;
  
  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]}>
      <Customer_Header 
        title={AppText.MY_ORDERS} 
        leftIconType={from === 'Personal_Area' ? 'back' : 'bars'} 
      />
      <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarActiveTintColor: colors.Light_theme_maincolour,
          tabBarInactiveTintColor: getTextColor(),
          tabBarPressColor: 'transparent',
          tabBarShowIcon: false,
          tabBarLabelStyle: {
            textTransform: 'none',
            fontSize: 14,
            fontWeight: '500',
          },
        }}
      >
        <Tab.Screen 
          name="Orders" 
          component={Customer_Order}
          options={{
            tabBarLabel: AppText.ORDERS
          }}
        />
        <Tab.Screen 
          name="History" 
          component={Customer_History}
          options={{
            tabBarLabel: AppText.HISTORY
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  )
}

export default My_Orders

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    backgroundColor: 'transparent',
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey,
  },
  tabIndicator: {
    backgroundColor: colors.Light_theme_maincolour,
    height: 2,
  },
})