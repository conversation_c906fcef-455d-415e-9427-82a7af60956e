import { StyleSheet, View, ScrollView } from 'react-native'
import React, { useState } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import { SafeAreaView } from 'react-native-safe-area-context'
import Customer_OrderCheckbox from '../../../Components/CustomerComponents/Track_Order_Components/Customer_OrderCheckbox'
import { wp } from '../../../Custom/Responsiveness'
import Customer_OrderItemsCard from '../../../Components/CustomerComponents/Track_Order_Components/Customer_OrderItemsCard'
import Customer_ShippingDetailsCard from '../../../Components/CustomerComponents/Track_Order_Components/Customer_ShippingDetailsCard'
import Customer_OrderItemsModal from '../../../Components/CustomerComponents/Track_Order_Components/Customer_OrderItemsModal'

const Customer_Order_Details = ({ route }) => {
  const AppText = useAppText();
  const { getTextColor, backgroundColor } = useTheme();
  const { order } = route.params;
  const [orderStatus, setOrderStatus] = useState({
    isPlaced: true,
    isConfirmed: false,
    isShipped: false,
    isDelivered: false
  });
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleStatusChange = (status) => {
    setOrderStatus(prev => ({
      ...prev,
      [status]: !prev[status]
    }));
  };

  const handleViewAllItems = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <Customer_Header title={`${AppText.ORDER} #${order.id}`} showBellIcon={false} leftIconType='back' />
      <ScrollView style={styles.content}>
        <Customer_OrderCheckbox 
          value={orderStatus.isPlaced}
          onValueChange={() => handleStatusChange('isPlaced')}
          date="28 May"
          label={AppText.ORDER_PLACED}
          style={styles.checkbox}
        />
        <Customer_OrderCheckbox 
          value={orderStatus.isConfirmed}
          onValueChange={() => handleStatusChange('isConfirmed')}
          date="28 May"
          label={AppText.ORDER_CONFIRMED}
          style={styles.checkbox}
        />
        <Customer_OrderCheckbox 
          value={orderStatus.isShipped}
          onValueChange={() => handleStatusChange('isShipped')}
          date="28 May"
          label={AppText.SHIPPED}
          style={styles.checkbox}
        />
        <Customer_OrderCheckbox 
          value={orderStatus.isDelivered}
          onValueChange={() => handleStatusChange('isDelivered')}
          date="28 May"
          label={AppText.DELIVERED}
          style={styles.checkbox}
        />

        <Customer_OrderItemsCard onViewAll={handleViewAllItems} />
        <Customer_ShippingDetailsCard order={order} />

        <Customer_OrderItemsModal
          visible={isModalVisible}
          onClose={handleCloseModal}
          order={order}
        />
      </ScrollView>
    </SafeAreaView>
  )
}

export default Customer_Order_Details

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(4),
  },
  checkbox: {
    marginVertical: wp(4),
  },
})