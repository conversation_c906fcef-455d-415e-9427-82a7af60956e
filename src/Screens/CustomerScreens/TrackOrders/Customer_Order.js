import { StyleSheet, View, SafeAreaView, ScrollView, ActivityIndicator, RefreshControl } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useFocusEffect } from '@react-navigation/native';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import Customer_Order_Card from '../../../Components/CustomerComponents/Track_Order_Components/Customer_Order_Card'
import { get_the_order_list } from '../../../Services/API/Endpoints/Customer/Product'
import { colors } from '../../../Custom/Colors'
import Loader from '../../../Custom/loader';

const Customer_Order = () => {
    const AppText = useAppText();
    const { backgroundColor } = useTheme();
    const [allorder_data, setAllorder_data] = useState([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    const get_orders = async () => {
        try {
            if (!refreshing) setLoading(true);
            const response = await get_the_order_list();
            console.log('Order List fetchedddd ---', response);

            if (Array.isArray(response)) {
                const sortedServices = response.sort((a, b) => b.id - a.id);
                setAllorder_data(sortedServices);
            }
        } catch (error) {
            console.error('❌ Failed to fetch orders:', error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            get_orders();
        }, [])
    );

    const onRefresh = React.useCallback(() => {
        setRefreshing(true);
        get_orders();
    }, []);

    if (loading && !refreshing) {
        return (
            <SafeAreaView style={[styles.container, { backgroundColor }]}>
                {/* <View style={styles.loaderContainer}>
                    <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
                </View> */}
                <Loader/>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={[styles.container, { backgroundColor }]}>
            {/* <Customer_Header title={AppText.MY_ORDERS} leftIconType="back" showCartIcon={false} /> */}
            <ScrollView 
                style={styles.content}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[colors.Light_theme_maincolour]}
                    />
            
                }
                showsVerticalScrollIndicator={false}
            >
                {allorder_data.map((order) => (
                    <Customer_Order_Card key={order.id} order={order} />
                ))}
            </ScrollView>
        </SafeAreaView>
    )
}

export default Customer_Order

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    content: {
        flex: 1,
        padding: 16,
    },
    loaderContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
})