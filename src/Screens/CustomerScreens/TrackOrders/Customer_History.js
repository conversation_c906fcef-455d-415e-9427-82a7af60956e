import { StyleSheet, View, SafeAreaView, ScrollView, ActivityIndicator, RefreshControl } from 'react-native'
import React, { useState, useEffect } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import Customer_History_Card from '../../../Components/CustomerComponents/Track_Order_Components/Customer_History_Card'
import { get_the_order_list } from '../../../Services/API/Endpoints/Customer/Product'
import { colors } from '../../../Custom/Colors'
import { useFocusEffect } from '@react-navigation/native'
import Loader from '../../../Custom/loader'

const Customer_History = () => {
    const AppText = useAppText();
    const { backgroundColor } = useTheme();
    const [historyOrders, setHistoryOrders] = useState([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    const get_orders = async () => {
        try {
            if (!refreshing) setLoading(true);
            const response = await get_the_order_list();
            
            if (Array.isArray(response)) {
                // Filter orders that are either completed or cancelled
                const filteredOrders = response.filter(order => 
                    order.status.toLowerCase() === 'delivered' || 
                    order.status.toLowerCase() === 'cancelled'
                );
                // Sort by most recent first
                const sortedOrders = filteredOrders.sort((a, b) => b.id - a.id);
                setHistoryOrders(sortedOrders);
            }
        } catch (error) {
            console.error('❌ Failed to fetch history orders:', error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            get_orders();
        }, [])
    );

    const onRefresh = React.useCallback(() => {
        setRefreshing(true);
        get_orders();
    }, []);

    if (loading && !refreshing) {
        return (
            <SafeAreaView style={[styles.container, { backgroundColor }]}>
                <Loader />
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={[styles.container, { backgroundColor }]}>
            <ScrollView 
                style={styles.content}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[colors.Light_theme_maincolour]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {historyOrders.map((order) => (
                    <Customer_History_Card key={order.id} order={order} />
                ))}
            </ScrollView>
        </SafeAreaView>
    )
}

export default Customer_History

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    content: {
        flex: 1,
        padding: 16,
    }
})