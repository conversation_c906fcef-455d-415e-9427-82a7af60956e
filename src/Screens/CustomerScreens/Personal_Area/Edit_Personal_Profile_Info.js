import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, KeyboardAvoidingView, SafeAreaView, Platform, ScrollView } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import CustomTextInput from '../../../Components/CustomTextInput';
import { hp, wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import EditCustomerProfileImage from '../../../Components/CustomerComponents/Personal_Area_Components/EditCustomerProfileImage';
import CustomeProfileInfoHeader from '../../../Components/CustomerComponents/Personal_Area_Components/CustomeProfileInfoHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';


const Edit_Personal_Profile_Info = () => {
  const { getsky_Theme,backgroundColor } = useTheme();
  const AppText = useAppText();
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    bio: '',
  });

  const [customer, setCustomer] = useState(null);

  useEffect(() => {
    const fetchCustomerDetails = async () => {
      try {
        const data = await AsyncStorage.getItem('customerData');
        if (data) {
          const parsed = JSON.parse(data);
          const customerInfo = Array.isArray(parsed) ? parsed[0] : parsed;
          setCustomer(customerInfo);
          console.log('✅ Customer object retrieved in the Personal info card:', customerInfo);
        }
      } catch (error) {
        console.log('❌ Error fetching customer details:', error);
      }
    };

    fetchCustomerDetails();
  }, []);

  useEffect(() => {
    if (customer) {
      setFormData({
        fullName: customer.full_name || '',
        email: customer.email || '',
        phoneNumber: customer.phone_number || '',
        bio: customer.description || '',
      });
    }
  }, [customer]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Handle save logic here
    console.log('Saving profile:', formData);
  };

  return (
    <SafeAreaView style={{flex:1,backgroundColor:backgroundColor}}>
      <KeyboardAvoidingView behavior={Platform.OS?"height":"padding"}>
        <ScrollView contentContainerStyle={{paddingBottom:hp(10)}}>
          <View style={styles.container}>
      <CustomeProfileInfoHeader/>
      <EditCustomerProfileImage/>
<View style={{paddingHorizontal:wp(5)}}>
      <CustomTextInput
        label={AppText.FULL_NAME}
        placeholder={AppText.FULL_NAME}
        value={formData.fullName}
        onChangeText={(text) => handleChange('fullName', text)}
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.EMAIL}
        placeholder={AppText.EMAIL}
        value={formData.email}
        onChangeText={(text) => handleChange('email', text)}
        keyboardType="email-address"
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.PHONE_NUMBER}
        placeholder={AppText.PHONE_NUMBER}
        value={formData.phoneNumber}
        onChangeText={(text) => handleChange('phoneNumber', text)}
        keyboardType="phone-pad"
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.BIO}
        placeholder={AppText.BIO}
        value={formData.bio}
        onChangeText={(text) => handleChange('bio', text)}
        multiline={true}
        numberOfLines={4}
        inputStyle={{ 
          backgroundColor: getsky_Theme(),
          height: hp(15),
        }}
      />
      <TouchableOpacity 
        style={[styles.saveButton, { backgroundColor: colors.Light_theme_maincolour }]}
        onPress={handleSave}
      >
        <ResponsiveText 
          color={colors.white} 
          size={4.5}
          weight={'bold'}
        >
          {AppText.SAVE}
        </ResponsiveText>
      </TouchableOpacity>
      </View>
    </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    // paddingHorizontal: wp(4),
    flex:1
  },
  saveButton: {
    height: hp(6),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: hp(4),
    marginBottom: hp(2),
  },
});

export default Edit_Personal_Profile_Info; 