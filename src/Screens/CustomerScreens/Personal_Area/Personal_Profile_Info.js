import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React from 'react'
import useTheme from '../../../Redux/useTheme'
import PersonalInfoHeader from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoHeader';
import PersonalInfoProfile from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoProfile';
import PersonalInfoCard from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoCard';

const Personal_Profile_Info = () => {
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <PersonalInfoHeader />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <PersonalInfoProfile />
        <PersonalInfoCard />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Personal_Profile_Info;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});