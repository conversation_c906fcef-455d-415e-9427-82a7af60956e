import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import {hp, wp} from '../../../Custom/Responsiveness';
import {useNavigation} from '@react-navigation/native';
import CustomTextInput from '../../../Components/CustomTextInput';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import {Add_Address, Update_Address} from '../../../Services/API/Endpoints/Customer/Add_New_Address';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Add_New_Addres = ({route,}) => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const {backgroundColor, getsky_Theme, getTextColor, getDark_Theme} = useTheme();

  const AddressID = route?.params?.item?.id;

  const [Address, setAddress] = useState(route?.params?.item?.address || '');
  const [Street, setStreet] = useState(route?.params?.item?.street || 'Model Colony');
  const [PostalCode, setPostalCode] = useState(route?.params?.item?.postal_code || '56700');
  const [Apartment, setApartment] = useState(route?.params?.item?.apartment || '#Room 345');
  const [labels, setLabels] = useState(['Home', 'Work', 'Other']);

  const initialTitle = route?.params?.item?.title || '';

  const isHomeOrWork = initialTitle === 'Home' || initialTitle === 'Work';

  const [selectedButton, setSelectedButton] = useState(() => {
    if (initialTitle === 'Home') return 0;
    if (initialTitle === 'Work') return 1;
    if (initialTitle) return 2;
    return 0;
  });

  const [customLabel, setCustomLabel] = useState(() => {
    if (!isHomeOrWork && initialTitle) {
      return initialTitle;
    }
    return '';
  });
  const [loading, setLoading] = useState(false);

  const handleButtonPress = (index) => {
    setSelectedButton(index);
    if (labels[index] !== 'Other') {
      setCustomLabel('');  // clear custom label if not Other
    }
  };
  const handleSaveLocation = async () => {
    console.log("Save location button pressed.");


    if (Address.trim() === '') {
      Alert.alert(AppText.VALIDATION_ERROR, AppText.ENTER_ADDRESS);
      return;
    }
    
    if (selectedButton === 2 && customLabel.trim() === '') {
      Alert.alert(AppText.VALIDATION_ERROR, AppText.CUSTOM_LABEL);
      return;
    }



    setLoading(true);
  
    try {
      const customerDataString = await AsyncStorage.getItem('customerData');
      const customerDataArray = customerDataString ? JSON.parse(customerDataString) : null;
  
      if (!customerDataArray || customerDataArray.length === 0 || !customerDataArray[0].id) {
        console.log('Customer data not found or invalid.');
        setLoading(false);
        return;
      }
  
      const customerId = customerDataArray[0].id;
  
      // Decide the title to send
      const selectedLabel = selectedButton === 2 && customLabel.trim() !== '' 
        ? customLabel.trim()
        : labels[selectedButton];
  
      if (AddressID) {
        const updatePayload = {
          customer: customerId,
          title: selectedLabel,
          address: Address,
        };
  
        const response = await Update_Address(updatePayload, AddressID);
        console.log("Address updated successfully:", response);
      } else {
        const addPayload = {
          title: selectedLabel,
          address: Address,
        };
  
        const response = await Add_Address(addPayload);
        console.log("Address added successfully:", response);
      }
  
      navigation.goBack();
  
    } catch (error) {
      console.log("Error in handleSaveLocation:", error?.response?.data || error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor}}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}
      >
        <ScrollView contentContainerStyle={{flexGrow: 1}}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon
              source={globalpath.backk}
              size={hp(6)}
              margin={[hp(2), 0, 0, wp(5)]}
            />
          </TouchableOpacity>

          <View style={[styles.FixedView, {backgroundColor}]}>
            <CustomTextInput
              placeholder={AppText.ENTER_YOUR_ADDRESS}
              label={AppText.ADDRESS}
              inputStyle={[
                styles.TextInputStyle,
                {color: getTextColor(), backgroundColor: getsky_Theme()},
              ]}
              value={Address}
              onChangeText={setAddress}
            />

            {/* <View style={styles.RowInputs}>
              <View style={{flex: 1, marginRight: wp(3)}}>
                <ResponsiveText
                  size={4}
                  color={getTextColor()}
                  weight="500"
                  margin={[0, 0, hp(1), 0]}
                >
                  {AppText.STREET}
                </ResponsiveText>
                <TextInput
                  placeholder={AppText.STREET}
                  placeholderTextColor={colors.grey}
                  value={Street}
                  onChangeText={setStreet}
                  style={[
                    styles.input,
                    {
                      color: getTextColor(),
                      borderColor: getDark_Theme(),
                      backgroundColor: getsky_Theme(),
                    },
                  ]}
                />
              </View>

              <View style={{flex: 1}}>
                <ResponsiveText
                  size={4}
                  color={getTextColor()}
                  weight="500"
                  margin={[0, 0, hp(1), 0]}
                >
                  {AppText.POSTAL_CODE}
                </ResponsiveText>
                <TextInput
                  placeholder={AppText.POSTAL_CODE}
                  placeholderTextColor={colors.grey}
                  value={PostalCode}
                  onChangeText={setPostalCode}
                  style={[
                    styles.input,
                    {
                      color: getTextColor(),
                      borderColor: getDark_Theme(),
                      backgroundColor: getsky_Theme(),
                    },
                  ]}
                />
              </View>
            </View> */}

            {/* <CustomTextInput
              placeholder={AppText.APARTMENT}
              label={AppText.APARTMENT}
              inputStyle={[
                styles.TextInputStyle,
                {color: getTextColor(), backgroundColor: getsky_Theme()},
              ]}
              value={Apartment}
              onChangeText={setApartment}
            /> */}

            <ResponsiveText
              size={4}
              weight="500"
              color={getTextColor()}
              margin={[hp(2), 0, hp(1), 0]}
            >
              {AppText.LABEL_AS}
            </ResponsiveText>

            <View style={styles.RowButtonOption}>
              {labels.map((label, index) => (
                <TouchableOpacity
                  key={label}
                  style={[
                    styles.ButtonStyle,
                    selectedButton === index
                      ? {backgroundColor: colors.Light_theme_maincolour}
                      : {backgroundColor: getsky_Theme()},
                  ]}
                  onPress={() => handleButtonPress(index)}
                >
                  <ResponsiveText
                    size={4.5}
                    weight="500"
                    color={selectedButton === index ? colors.white : colors.grey}
                  >
                    {label}
                  </ResponsiveText>
                </TouchableOpacity>
              ))}
            </View>

            {selectedButton === 2 && (
  <CustomTextInput
    placeholder="Enter Custom Label"
    inputStyle={[
      styles.TextInputStyle,
      { color: getTextColor(), backgroundColor: getsky_Theme() },
    ]}
    value={customLabel}
    onChangeText={(text) => setCustomLabel(text)}  // <-- just set the customLabel
  />
)}

            <TouchableOpacity
              style={[
                styles.SaveLocation_Button_style,
                {opacity: loading ? 0.7 : 1},
              ]}
              disabled={loading}
              onPress={handleSaveLocation}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} weight="400" size={4.5}>
                  {AppText.SAVE_LOCATION}
                </ResponsiveText>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  FixedView: {
    height: hp(62),
    width: '100%',
    position: 'absolute',
    bottom: 0,
    elevation: 5,
    paddingHorizontal: wp(7),
    paddingVertical: wp(4),
  },
  TextInputStyle: {
    height: hp(6),
  },
  RowInputs: {
    flexDirection: 'row',
    marginBottom: hp(2.5),
    marginTop: hp(1),
  },
  input: {
    width: '100%',
    height: hp(6),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    fontSize: wp(3.8),
  },
  RowButtonOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(1),
  },
  ButtonStyle: {
    paddingHorizontal: wp(6),
    paddingVertical: hp(1.6),
    borderRadius: wp(8),
  },
  SaveLocation_Button_style: {
    backgroundColor: colors.Light_theme_maincolour,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2),
    borderRadius: wp(3),
    marginVertical: hp(2),
  },

  
});

export default Add_New_Addres;
