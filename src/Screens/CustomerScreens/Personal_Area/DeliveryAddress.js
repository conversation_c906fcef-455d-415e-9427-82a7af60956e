import { SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import CutomerDeliveryAddressHeader from '../../../Components/CustomerComponents/Personal_Area_Components/CutomerDeliveryAddressHeader'
import DeliveryAddressCard from '../../../Components/CustomerComponents/Personal_Area_Components/DeliveryAddressCard'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import { hp, wp } from '../../../Custom/Responsiveness'
import { useNavigation } from '@react-navigation/native'
const DeliveryAddress = () => {
  const navigation=useNavigation()
  const AppText=useAppText();
  const { backgroundColor,getsky_Theme,getTextColor } = useTheme();




  return (
    <SafeAreaView style={{flex:1,backgroundColor:backgroundColor}}>
    <View style={{flex:1}}>
    <CutomerDeliveryAddressHeader/>
    <DeliveryAddressCard/>
    </View>
      {/* <TouchableOpacity style={styles.AddNewAddress_Button_Style} onPress={()=>navigation.navigate('Add_New_Addres')}>
        <ResponsiveText  color={colors.white} weight={'400'} size={4.5}>{AppText.ADD_NEW_ADDRESS}</ResponsiveText>
      </TouchableOpacity> */}
    </SafeAreaView>
  )
}

export default DeliveryAddress

const styles = StyleSheet.create({
//   AddNewAddress_Button_Style:{
    
// backgroundColor:colors.Light_theme_maincolour,
// alignItems:"center",
// justifyContent:"center",
// paddingVertical:hp(2),
// marginHorizontal:wp(5),
// borderRadius:wp(3),
// marginVertical:hp(2)
//   },



//   AddNewAddress_Button_Style: {
//     backgroundColor: colors.Light_theme_maincolour,
//     alignItems: "center",
//     justifyContent: "center",
//     paddingVertical: hp(2),
//     marginHorizontal: wp(5),
//     borderRadius: wp(3),
//     marginBottom: hp(2),
//   }
})