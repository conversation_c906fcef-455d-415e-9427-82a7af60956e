import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React, { useState } from 'react'
import { useNavigation } from '@react-navigation/native'
import useAppText from '../../../Custom/AppText';
import { globalpath } from '../../../Custom/globalpath';
import Personal_MenuItem from '../../../Components/CustomerComponents/Personal_Area_Components/Personal_MenuItems';
import Personal_ProfileSection from '../../../Components/CustomerComponents/Personal_Area_Components/Personal_ProfileSection';
import { hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import CustomHeader from '../../../Components/CustomHeader';
import AppearanceSettings from '../../../Components/Settings_Components/AppearanceSettings';

const Personal_Area = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();
  const [showAppearanceModal, setShowAppearanceModal] = useState(false);
  const navigation = useNavigation();

  const menuItems = [
    { icon: globalpath.track, title: AppText.PERSONAL_TRACK_ORDERS },
    { icon: globalpath.delivery, title: AppText.DELIVERY_ADDRESS },
    { icon: globalpath.payment, title: AppText.PAYMENT_METHOD },
   
  ];

  const handleMenuItemPress = (title) => {
    if (title === AppText.PERSONAL_TRACK_ORDERS) {
      navigation.navigate('Track_Orders_Stack', { 
        screen: 'My_Orders',
        params: { from: 'Personal_Area' }
      });
    } else if (title === AppText.DELIVERY_ADDRESS) {
      navigation.navigate('DeliveryAddress');
    } else if (title === AppText.PAYMENT_METHOD) {
      navigation.navigate('PaymentMethod');
     } else {
      console.log(`Pressed: ${title}`);
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader title={AppText.SETTINGS} showBellIcon={false} /> 
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Personal_ProfileSection />
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <Personal_MenuItem
              key={index}
              icon={item.icon}
              title={item.title}
              onPress={() => handleMenuItemPress(item.title)}
            />
          ))}
        </View>
      </ScrollView>

     
    </SafeAreaView>
  );
};

export default Personal_Area;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  menuContainer: {
    marginTop: hp(2),
  },
});