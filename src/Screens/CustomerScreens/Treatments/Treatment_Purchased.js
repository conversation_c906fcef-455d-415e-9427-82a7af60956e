import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { hp, wp } from '../../../Custom/Responsiveness';
import CustomerTreatmentImageCarousel from '../../../Components/CustomerComponents/Treatment_Components/CustomerTreatmentImageCarousel';
import CustomerTreatmentQuantitySelector from '../../../Components/CustomerComponents/Treatment_Components/CustomerTreatmentQuantitySelector';
import CustomerTreatmentDescription from '../../../Components/CustomerComponents/Treatment_Components/CustomerTreatmentDescription';
import CustomerTreatmentTypeDropdown from '../../../Components/CustomerComponents/Treatment_Components/CustomerTreatmentTypeDropdown';
import CustomTextInput from '../../../Components/CustomTextInput';

const Treatment_Purchased = ({ route, navigation }) => {
  const { treatment } = route.params;
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const [quantity, setQuantity] = useState(1);
  const [treatmentType, setTreatmentType] = useState(null);
  const [personalMessage, setPersonalMessage] = useState('');
  const [recipientEmail, setRecipientEmail] = useState('');

  const handleQuantityChange = (newQuantity) => {
    setQuantity(newQuantity);
  };

  const handleTreatmentTypeSelect = (type) => {
    setTreatmentType(type);
  };

  const handleBuyNow = () => {
    // Handle buy now action
    console.log('Buying treatment:', {
      treatment,
      quantity,
      treatmentType,
      personalMessage,
      recipientEmail
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Customer_Header title={AppText.TREATMENT_DETAILS} leftIconType="back" />
      <CustomerTreatmentImageCarousel image={treatment.image} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex: 1}}>  
        <ScrollView style={styles.content}>
          <View style={styles.detailsContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="bold"
              margin={[hp(2), 0, hp(1), wp(4)]}
            >
              {treatment.title}
            </ResponsiveText>

            <CustomerTreatmentQuantitySelector 
              amount={parseInt(treatment.amount)} 
              onQuantityChange={handleQuantityChange}
            />

            <CustomerTreatmentDescription description={treatment.description} />

            <CustomerTreatmentTypeDropdown onSelect={handleTreatmentTypeSelect} />

            {treatmentType === 'digital' && (
              <View style={styles.digitalGiftContainer}>
                <ResponsiveText
                  color={getTextColor()}
                  size={4}
                  weight="bold"
                  margin={[hp(1), 0, hp(2), wp(4)]}
                >
                  {AppText.SET_AS_GIFT}
                </ResponsiveText>

                <CustomTextInput
                  label={AppText.PERSONAL_MESSAGE}
                  placeholder={AppText.ENTER_PERSONAL_MESSAGE}
                  value={personalMessage}
                  onChangeText={setPersonalMessage}
                  multiline
                  numberOfLines={4}
                  containerStyle={styles.inputContainer}
                />

                <CustomTextInput
                  label={AppText.RECIPIENTS_EMAIL}
                  placeholder={AppText.ENTER_RECIPIENTS_EMAIL}
                  value={recipientEmail}
                  onChangeText={setRecipientEmail}
                  keyboardType="email-address"
                  containerStyle={styles.inputContainer}
                />
              </View>
            )}

            <TouchableOpacity 
              style={[styles.buyButton, { backgroundColor: colors.Light_theme_maincolour }]}
              onPress={handleBuyNow}
            >
              <ResponsiveText color={colors.white} size={4} weight="bold">
                {AppText.BUY_NOW}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Treatment_Purchased;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  detailsContainer: {
    paddingBottom: hp(4),
  },
  digitalGiftContainer: {
    marginTop: hp(2),
  },
  inputContainer: {
    marginHorizontal: wp(4),
    marginBottom: hp(2),
  },
  buyButton: {
    marginHorizontal: wp(4),
    marginTop: hp(4),
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
});