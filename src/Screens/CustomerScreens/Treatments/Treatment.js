import { StyleSheet, View, SafeAreaView, FlatList, TextInput, KeyboardAvoidingView, Platform, TouchableOpacity } from 'react-native'
import React, { useState, useEffect } from 'react'
import Customer_Header from '../../../Components/CustomerComponents/Customer_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import TreatmentCard from '../../../Components/CustomerComponents/Treatment_Components/TreatmentCard'
import { Treatment_Mock } from '../../../Custom/mockData'
import { useNavigation } from '@react-navigation/native'
import { hp, wp } from '../../../Custom/Responsiveness'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'
import { colors } from '../../../Custom/Colors'
import ResponsiveText from '../../../Custom/RnText'

const Treatment = () => {
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();
  const navigation = useNavigation();
  const [searchText, setSearchText] = useState('');
  const [filteredTreatments, setFilteredTreatments] = useState(Treatment_Mock);

  const handleTreatmentPress = (treatment) => {
    navigation.navigate('View_Treatment', { treatment });
  };

  const handleSearch = (text) => {
    setSearchText(text);
    if (text) {
      const filtered = Treatment_Mock.filter(treatment =>
        treatment.title.toLowerCase().includes(text.toLowerCase()) ||
        treatment.description.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredTreatments(filtered);
    } else {
      setFilteredTreatments(Treatment_Mock);
    }
  };

  const renderTreatmentItem = ({ item }) => (
    <TreatmentCard
      item={item}
      onPress={() => handleTreatmentPress(item)}
    />
  );

  const handlePurchase = () => {
    navigation.navigate('Treatment_Purchased',{treatment: Treatment_Mock[0]});
  };

  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]}>
      <Customer_Header title={AppText.TREATMENTS} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex: 1}}>
        <View style={styles.content}>
          <View style={styles.searchRow}>
            <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
              <Icon 
                source={globalpath.search} 
                size={20} 
                tintColor={getTextColor()} 
                margin={[0,wp(2),0,0]} 
              />
              <TextInput
                style={[styles.searchInput, {color: getTextColor()}]}
                placeholder={AppText.SEARCH_TREATMENTS}
                placeholderTextColor={colors.grey}
                value={searchText}
                onChangeText={handleSearch}
              />
            </View>
            <TouchableOpacity 
              style={[styles.purchasedButton, { backgroundColor: colors.Light_theme_maincolour }]}
            //   onPress={handlePurchase}
            >
              <ResponsiveText color={colors.white} size={3.5}>
                {AppText.PURCHASED}
              </ResponsiveText>
            </TouchableOpacity>
          </View>

          <FlatList
            data={filteredTreatments}
            renderItem={renderTreatmentItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.treatmentsContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <View style={styles.noResults}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {AppText.NO_RESULTS_FOUND}
                </ResponsiveText>
              </View>
            )}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default Treatment

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(3),
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  searchContainer: {
    flex: 1,
    height: hp(5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    marginRight: wp(2),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  purchasedButton: {
    paddingVertical: hp(1.5),
    paddingHorizontal: wp(4),
    borderRadius: wp(1.5),
    alignItems: 'center',
  },
  treatmentsContainer: {
    flexGrow: 1,
    paddingBottom: hp(10),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: hp(20),
  }
})