import { StyleSheet, View, <PERSON><PERSON>rea<PERSON>ie<PERSON>, <PERSON>rollView } from 'react-native'
import React, { useState } from 'react'
import useTheme from '../Redux/useTheme'
import CustomHeader from '../Components/CustomHeader'
import { globalpath } from '../Custom/globalpath'
import useAppText from '../Custom/AppText'
import ProfileSection from '../Components/Settings_Components/ProfileSection'
import MenuItem from '../Components/Settings_Components/MenuItem'
import { hp } from '../Custom/Responsiveness'
import AppearanceSettings from '../Components/Settings_Components/AppearanceSettings'
import { useNavigation } from '@react-navigation/native'

const Settings = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();
  const [showAppearanceModal, setShowAppearanceModal] = useState(false);
  const navigation = useNavigation();

  const menuItems = [
    { icon: globalpath.orders, title: AppText.ORDERS },
    { icon: globalpath.ratings, title: AppText.SALON_RATINGS },
    { icon: globalpath.notifications, title: AppText.NOTIFICATIONS },
    { icon: globalpath.help, title: AppText.HELP },
    { icon: globalpath.about, title: AppText.ABOUT },
    { icon: globalpath.theme, title: AppText.APPEARANCE },
  ];

  const handleMenuItemPress = (title) => {
    if (title === AppText.APPEARANCE) {
      setShowAppearanceModal(true);
      console.log('Appearance pressed');
    } else if (title === AppText.ORDERS) {
      navigation.navigate('Orders');
      console.log('Orders pressed');
    } else if (title === AppText.SALON_RATINGS) {
      navigation.navigate('SalonRatings');
      console.log('Salon Ratings pressed');
    } else {
      console.log(`Pressed: ${title}`);
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader title={AppText.SETTINGS} showBellIcon={false} /> 
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ProfileSection />
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              title={item.title}
              onPress={() => handleMenuItemPress(item.title)}
            />
          ))}
        </View>
      </ScrollView>

      <AppearanceSettings 
        visible={showAppearanceModal}
        onClose={() => setShowAppearanceModal(false)}
      />
    </SafeAreaView>
  );
};

export default Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  menuContainer: {
    marginTop: hp(2),
  },
});