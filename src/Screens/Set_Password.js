import { SafeAreaView, StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator, Platform } from 'react-native'
import React, { useState } from 'react'
import { colors } from '../Custom/Colors'
import { wp, hp } from '../Custom/Responsiveness'
import ResponsiveText from '../Custom/RnText'
import useAppText from '../Custom/AppText'
import Icon from '../Custom/Icon'
import { globalpath } from '../Custom/globalpath'
import useTheme from '../Redux/useTheme'
import { send_OTP } from '../Services/API/Endpoints/Auth/Auth'
import CustomHeader from '../Components/CustomHeader'

const Set_Password = ({ navigation , route }) => {
  console.log("Routes Data in Password ", route.params.payload)
  const {data} = route.params.payload;
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  const isPasswordValid =
  password &&
  confirmPassword &&
  password === confirmPassword &&
  /(?=.*[0-9!@#$%^&*])/.test(password);

  const validatePassword = () => {
    if (!password || !confirmPassword) {
      alert('Please fill all fields')
      return false
    }
    if (password !== confirmPassword) {
      alert('Passwords do not match')
      return false
    }
    if (!/(?=.*[0-9]|[!@#$%^&*])/.test(password)) {
      alert('Password must contain at least 1 number or special character')
      return false
    }
    return true
  }

  const handleRegister = async () => {
    if (!validatePassword()) return

    try {
      await sendOtp();
      setLoading(true);
      // ✅ Append the password to the existing data
      const updatedData = {
        ...route.params.payload,
        password: password, // 🔐 add password
      };

      console.log("updatedData------", updatedData)
     
       // ✅ Navigate with updated payload
       setLoading(false);
     navigation.navigate('Email Verification', { data: updatedData });
      // navigation.navigate('Login')
    } catch (error) {
      setLoading(false)
    }
  }
  
  
    const sendOtp = async () => {
      try {
        setLoading(true);
  
        const payload = {
          email: route.params.payload.email,
        };
  
        const response = await send_OTP(payload);
        console.log('📩 OTP sent on screen load:', response);
      } catch (error) {
        console.error('❌ Failed to send OTP on mount:', error);
      } finally {
        setLoading(false);
      }
    };
  

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <View style={styles.contentContainer}>
        <CustomHeader leftIconType='back' showBellIcon={false}/>
        <ResponsiveText weight={'600'} size={7} margin={[hp(2), 0, wp(3), 0]} textAlign={'center'} color={getTextColor()}>
          {AppText.SET_PASSWORD_TITLE}
        </ResponsiveText>

        <ResponsiveText color={getborderTextColor()} size={4} margin={[0, 0, wp(5), wp(0)]} textAlign={'center'}>
          {AppText.SET_PASSWORD_SUBTITLE}
        </ResponsiveText>

        <View style={[styles.inputContainer,{borderColor: getDark_Theme()}]}>
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.ENTER_PASSWORD_PLACEHOLDER}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            placeholderTextColor={colors.darkGrey}
          />
          <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
            <Icon
              source={showPassword ? globalpath.eyeOff : globalpath.eye}
              size={wp(5)}
              tintColor={getborderTextColor()}
            />
          </TouchableOpacity>
        </View>

        <View style={[styles.inputContainer,{borderColor: getDark_Theme()}]}>
          <TextInput
            style={[styles.input,{color: getTextColor()}]}
            placeholder={AppText.CONFIRM_PASSWORD_PLACEHOLDER}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showConfirmPassword}
            placeholderTextColor={colors.darkGrey}
          />
          <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
            <Icon
              source={showConfirmPassword ? globalpath.eyeOff : globalpath.eye}
              size={wp(5)}
              tintColor={getborderTextColor()}
            />
          </TouchableOpacity>
        </View>

        {password && confirmPassword && password !== confirmPassword && (
  <ResponsiveText color="red" size={3} margin={[hp(-1.3), 0, hp(1), 0]} textAlign={'left'}>
    {AppText.PASSWORD_DONT_MATCH}
  </ResponsiveText>
)}

{!isPasswordValid && (
  <ResponsiveText
    color={getborderTextColor()}
    size={3.5}
    margin={[0, 0, wp(5), wp(2)]}
    textAlign={'left'}
  >
    {AppText.PASSWORD_REQUIREMENT}
  </ResponsiveText>
)}

        {loading ? (
          <View style={styles.registerButton}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
          style={[
            styles.registerButton,
            { backgroundColor: isPasswordValid ? colors.Light_theme_maincolour : colors.grey }
          ]}
          onPress={handleRegister}
          disabled={!isPasswordValid}
        >
          <ResponsiveText color={colors.white} size={4} weight="bold">
            {AppText.SET_PASSWORD_BUTTON}
          </ResponsiveText>
        </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  )
}

export default Set_Password

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    padding: wp(5),
    marginTop: hp(2),
  },
   inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: wp(1.5),
      marginBottom: hp(2),
      paddingHorizontal: Platform.OS === 'android' ? wp(4.7) :null,
      padding: Platform.OS ==='ios' ? wp(4) : null,
      borderWidth: 1,
    },
  input: {
    flex: 1,
    fontSize: wp(4),
  },
  registerButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(2),
  },
})