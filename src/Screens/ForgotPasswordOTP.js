import { SafeAreaView, StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator, Platform } from 'react-native'
import React, { useState, useRef, useCallback } from 'react'
import { colors } from '../Custom/Colors'
import { wp, hp } from '../Custom/Responsiveness'
import ResponsiveText from '../Custom/RnText'
import useAppText from '../Custom/AppText'
import useTheme from '../Redux/useTheme'
import { useFocusEffect } from '@react-navigation/native';
import Icon from '../Custom/Icon'
import { globalpath } from '../Custom/globalpath'

const ForgotPasswordOTP = ({ route, navigation }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor,getDark_Theme } = useTheme();
  const AppText = useAppText();
  const { email } = route.params
  const [otp, setOtp] = useState(['', '', '', '', '', ''])
  const [loading, setLoading] = useState(false)
  const inputRefs = useRef([])

  const isOtpComplete = otp.every(digit => digit !== '');

  useFocusEffect(
    useCallback(() => {
      // Reset loading and OTP when coming back
      setLoading(false);
      setOtp(['', '', '', '', '', '']);
    }, [])
  );


  const handleOtpChange = (text, index) => {
    const newOtp = [...otp]
    newOtp[index] = text
    setOtp(newOtp)

    // Auto-focus next input
    if (text && index < 5) {
      inputRefs.current[index + 1].focus()
    }
  }

  const handleKeyPress = (e, index) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1].focus()
    }
  }

  const handleVerify = async () => {
    const otpString = otp.join('')
    if (otpString.length !== 6) {
      alert('Please enter complete OTP')
      return
    }

    try {
      setLoading(true)
      // Add your verification logic here
      navigation.navigate('ResetPassword')
    } catch (error) {
      setLoading(false)
    }
  }

  const handleResendCode = () => {
    // Add your resend code logic here
    alert('Code resent successfully')
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <View style={styles.contentContainer}>
        <ResponsiveText weight={'600'} size={7} margin={[hp(2), 0, wp(5), 0]} textAlign={'center'} color={getTextColor()}>
          {AppText.EMAIL_VERIFICATION_TITLE}
        </ResponsiveText>

        <ResponsiveText color={getborderTextColor()} size={4} margin={[0, 0, wp(5), wp(5)]} textAlign={'center'}>
          {AppText.EMAIL_VERIFICATION_SUBTITLE}{'\n'}<ResponsiveText color={colors.Light_theme_maincolour} size={4} weight={'bold'}>{email}</ResponsiveText>
        </ResponsiveText>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              style={[
                [styles.otpInput, { borderColor: getDark_Theme(), color: getTextColor() }],
                digit ? styles.otpInputActive : null
              ]}
              value={digit}
              onChangeText={(text) => handleOtpChange(text, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              keyboardType="number-pad"
              maxLength={1}
              contextMenuHidden
            />
          ))}
        </View>

        <View style={styles.resendContainer}>
          <ResponsiveText color={getTextColor()} size={4}>
            {AppText.RESEND_CODE_TEXT}{' '}
          </ResponsiveText>
          <TouchableOpacity onPress={handleResendCode}>
            <ResponsiveText color={colors.Light_theme_maincolour} size={4}>
              {AppText.RESEND_AGAIN}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={[styles.verifyButton, { backgroundColor: 'grey' }]}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
            style={[
              styles.verifyButton,
              { backgroundColor: isOtpComplete ? colors.Light_theme_maincolour : getDark_Theme()}
            ]}
            onPress={handleVerify}
            disabled={!isOtpComplete}
          >
            <ResponsiveText color={ isOtpComplete ? colors.white : colors.grey} size={4} weight="600">
              {AppText.VERIFY_BUTTON}
            </ResponsiveText>
          </TouchableOpacity>
        )}

        <TouchableOpacity 
          style={[styles.backButton, { borderColor: getDark_Theme() }]} 
          onPress={() => navigation.navigate('Login')}
        >
          <View style={styles.backButtonContent}>
            <Icon 
              source={globalpath.back_arrow} 
              size={wp(5)} 
              tintColor={colors.Light_theme_maincolour}
              margin={[0, wp(2), 0, 0]}
            />
            <ResponsiveText 
              color={colors.Light_theme_maincolour} 
              size={4} 
              weight={'600'}
            >
              {AppText.BACK_TO_LOGIN}
            </ResponsiveText>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

export default ForgotPasswordOTP

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    padding: wp(5),
    marginTop: hp(2),
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: hp(4),
  },
  otpInput: {
    width: wp(12),
    height: wp(12),
    borderWidth: 1,
    borderRadius: wp(2),
    textAlign: 'center',
    fontSize: wp(5),
  },
  otpInputActive: {
    borderColor: colors.Light_theme_maincolour,
  },
  resendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(4),
  },
  verifyButton: {
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(3),
  },
  backButton: {
    // marginTop: 'auto',
    marginBottom: Platform.OS === 'ios' ? hp(2) : hp(4),
    borderWidth: 1,
    borderRadius: wp(1.5),
    backgroundColor: 'transparent',
  },
  backButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: wp(3),
  },
}) 