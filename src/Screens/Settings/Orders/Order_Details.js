import { StyleSheet, Text, View, ScrollView } from 'react-native'
import React, { useState } from 'react'
import CustomHeader from '../../../Components/CustomHeader'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import { SafeAreaView } from 'react-native-safe-area-context'
import OrderCheckbox from '../../../Components/Settings_Components/OrderCheckbox'
import { wp } from '../../../Custom/Responsiveness'
import OrderItemsCard from '../../../Components/Settings_Components/OrderItemsCard'
import ShippingDetailsCard from '../../../Components/Settings_Components/ShippingDetailsCard'
import OrderItemsModal from '../../../Components/Settings_Components/OrderItemsModal'

const Order_Details = ({ route }) => {
  const AppText = useAppText();
  const { getTextColor, backgroundColor } = useTheme();
  const { order } = route.params;
  console.log("orders details", order);
  const [orderStatus, setOrderStatus] = useState({
    isPlaced: true,
    isConfirmed: false,
    isShipped: false,
    isDelivered: false
  });
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleStatusChange = (status) => {
    setOrderStatus(prev => ({
      ...prev,
      [status]: !prev[status]
    }));
  };

  const handleViewAllItems = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  // Mock order items data - replace with actual data from your order
  const orderItems = [
    { id: '1', name: 'Hair Cut', quantity: 1 },
    { id: '2', name: 'Beard Trim', quantity: 1 },
    { id: '3', name: 'Hair Color', quantity: 1 },
    { id: '4', name: 'Hair Wash', quantity: 1 },
  ];

  return (
   <SafeAreaView style={[styles.container,{backgroundColor: backgroundColor}]}>
      <CustomHeader title={`${AppText.ORDER} #${order.id}`} showBellIcon={false} leftIconType='back' />
      <ScrollView style={styles.content}>
        <OrderCheckbox 
          value={orderStatus.isPlaced}
          onValueChange={() => handleStatusChange('isPlaced')}
          date="28 May"
          label={AppText.ORDER_PLACED}
          style={styles.checkbox}
        />
        <OrderCheckbox 
          value={orderStatus.isConfirmed}
          onValueChange={() => handleStatusChange('isConfirmed')}
          date="28 May"
          label={AppText.ORDER_CONFIRMED}
          style={styles.checkbox}
        />
        <OrderCheckbox 
          value={orderStatus.isShipped}
          onValueChange={() => handleStatusChange('isShipped')}
          date="28 May"
          label={AppText.SHIPPED}
          style={styles.checkbox}
        />
        <OrderCheckbox 
          value={orderStatus.isDelivered}
          onValueChange={() => handleStatusChange('isDelivered')}
          date="28 May"
          label={AppText.DELIVERED}
          style={styles.checkbox}
        />

        <OrderItemsCard onViewAll={handleViewAllItems} />
        <ShippingDetailsCard order={order} />

        <OrderItemsModal
          visible={isModalVisible}
          onClose={handleCloseModal}
          order={order}
        />
      </ScrollView>
   </SafeAreaView>
  )
}

export default Order_Details

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    content: {
        flex: 1,
        padding: wp(4),
    },
    checkbox: {
        marginVertical: wp(4),
    },
})