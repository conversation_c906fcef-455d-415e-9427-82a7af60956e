import React, { useState } from 'react'
import { StyleSheet, View, ScrollView, RefreshControl, ActivityIndicator } from 'react-native'
import ResponsiveText from '../../../Custom/RnText'
import useTheme from '../../../Redux/useTheme'
import { wp, hp } from '../../../Custom/Responsiveness'
import useAppText from '../../../Custom/AppText'
import OrderStatusTabs from '../../../Components/Settings_Components/OrderStatusTabs'
import ProcessingOrders from '../../../Components/Settings_Components/ProcessingOrders'
import ShippedOrders from '../../../Components/Settings_Components/ShippedOrders'
import DeliveredOrders from '../../../Components/Settings_Components/DeliveredOrders'
import CancelledOrders from '../../../Components/Settings_Components/CancelledOrders'
import { useFocusEffect } from '@react-navigation/core'
import { get_the_order_list } from '../../../Services/API/Endpoints/Customer/Product'
import { colors } from '../../../Custom/Colors'
import Loader from '../../../Custom/loader'

const OrdersTab = ({loading, setLoading}) => {
  // const { loading, setLoading } = route.params;

  const { getTextColor, backgroundColor } = useTheme()
  const AppText = useAppText()
  const [activeTab, setActiveTab] = useState('processing')
  const [allOrders, setAllOrders] = useState([])
  // const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const get_orders = async () => {
    try {
      if (!refreshing) setLoading(true)
      const response = await get_the_order_list()
      console.log('Order list in admin orders', response)

      if (Array.isArray(response)) {
        const sortedOrders = response.sort((a, b) => b.id - a.id)
        setAllOrders(sortedOrders)
      }
    } catch (error) {
      console.error('❌ Failed to fetch admin orders:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useFocusEffect(
    React.useCallback(() => {
      get_orders()
    }, [])
  )

  const onRefresh = React.useCallback(() => {
    setRefreshing(true)
    get_orders()
  }, [])

  const getFilteredOrders = (status) => {
    return allOrders.filter(order => {
      switch (status) {
        case 'processing':
          return order.status === 'PROCESSING' || order.status === 'PENDING'
        case 'shipped':
          return order.status === 'SHIPPED'
        case 'delivered':
          return order.status === 'DELIVERED'
        case 'cancelled':
          return order.status === 'CANCELLED'
        default:
          return false
      }
    })
  }

  const renderOrderContent = () => {
    const filteredOrders = getFilteredOrders(activeTab)

    // if (loading && !refreshing) {
    //   return <Loader />
    // }

    switch (activeTab) {
      case 'processing':
        return <ProcessingOrders orders={filteredOrders} />
      case 'shipped':
        return <ShippedOrders orders={filteredOrders} />
      case 'delivered':
        return <DeliveredOrders orders={filteredOrders} />
      case 'cancelled':
        return <CancelledOrders orders={filteredOrders} />
      default:
        return <ProcessingOrders orders={filteredOrders} />
    }
  }

  return (
    <ScrollView 
      style={[styles.container, {backgroundColor: backgroundColor}]} 
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[colors.Light_theme_maincolour]}
        />
      }
    >
      <View style={styles.content}>
        <OrderStatusTabs 
          activeTab={activeTab}
          onTabPress={setActiveTab}
        />
        {renderOrderContent()}
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
  },
  title: {
    marginBottom: hp(2),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(10),
  },
  emptyText: {
    textAlign: 'center',
  },
})

export default OrdersTab 