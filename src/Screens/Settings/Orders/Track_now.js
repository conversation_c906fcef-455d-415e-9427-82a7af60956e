import { SafeAreaView, StyleSheet, View, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native'
import React, { useState } from 'react'
import useTheme from '../../../Redux/useTheme'
import CustomHeader from '../../../Components/CustomHeader' 
import useAppText from '../../../Custom/AppText'
import OrderDetailsCard from '../../../Components/Settings_Components/OrderDetailsCard'
import OrderStatusDropdown from '../../../Components/Settings_Components/OrderStatusDropdown'
import CustomerDetailsCard from '../../../Components/Settings_Components/CustomerDetailsCard'
import ShippingDetailsCard from '../../../Components/Settings_Components/ShippingDetailsCard'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import { wp, hp } from '../../../Custom/Responsiveness'
import { update_order_status } from '../../../Services/API/Endpoints/Customer/Product'
import Loader from '../../../Custom/loader'

const Track_now = ({ route, navigation }) => {
  const { backgroundColor } = useTheme()
  const AppText = useAppText()
  const { order } = route.params
  const [status, setStatus] = useState(order.status)
  const [isSaveEnabled, setIsSaveEnabled] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  console.log('order inside track now',order)

  const handleStatusChange = (newStatus) => {
    setStatus(newStatus)
    setIsSaveEnabled(newStatus !== order.status)
    setError(null)
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      setError(null)

      // Include all required fields in the payload
      const payload = {
        customer: order.customer,
        status: status,
        total_price: order.total_price,
        shipping_address: order.shipping_address,
        payment_id: order.payment_id,
        items: order.items.map(item => ({
          id: item.id,
          order: item.order,
          product: item.product,
          variant: item.variant,
          quantity: item.quantity,
          price_at_purchase: item.price_at_purchase,
          product_name: item.product_name,
          product_image: item.product_image
        }))
      }

      const response = await update_order_status(order.id, payload)
      console.log('Status updated successfully:', response)
      
      // Navigate back after successful update
      navigation.goBack()
    } catch (error) {
      console.error('Failed to update order status:', error)
      if (error.response?.data) {
        // Create a readable error message from the API response
        const errorMessages = Object.entries(error.response.data)
          .map(([key, value]) => `${key}: ${value.join(', ')}`)
          .join('\n')
        setError(errorMessages)
      } else {
        setError(AppText.STATUS_UPDATE_ERROR || 'Failed to update status. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  // Format first item details for OrderDetailsCard
  const firstItem = order.items && order.items.length > 0 ? order.items[0] : null
  const orderDetailsProps = {
    title: firstItem ? firstItem.product_name : 'No Product',
    amount: order.total_price,
    items: order.items.length,
    image: firstItem?.product_image || null
  }

  // Format customer details
  const customerDetails = {
    user: `Customer ID: ${order.customer}`,
    location: order.shipping_address,
    call: 'N/A' // Add phone number field if available in your API response
  }

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader title={`${AppText.ORDER} #${order.id}`} showBellIcon={false} leftIconType='back' />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <OrderDetailsCard order={orderDetailsProps} />
        
        <OrderStatusDropdown 
          value={status}
          onChange={handleStatusChange}
          error={error}
        />

        <CustomerDetailsCard order={customerDetails} />

        <ShippingDetailsCard order={order} />

        {error && (
          <ResponsiveText color={colors.red} size={3.5} margin={[0, 0, hp(2), 0]} style={styles.errorText}>
            {error}
          </ResponsiveText>
        )}

        <TouchableOpacity 
          style={[
            styles.saveButton, 
            { 
              backgroundColor: isSaveEnabled ? colors.Light_theme_maincolour : colors.lightGrey1,
              opacity: loading ? 0.5 : 1
            }
          ]}
          disabled={!isSaveEnabled || loading}
          onPress={handleSave}
        
        >
          {/* {loading ? (
            <ActivityIndicator color={colors.white} />
          ) : ( */}
            <ResponsiveText
              color={isSaveEnabled ? colors.white : colors.grey}
             weight={'600'}>
              {AppText.SAVE}
            </ResponsiveText>
          {/* )} */}
        </TouchableOpacity>
      </ScrollView>
      {loading && <Loader />}
    </SafeAreaView>
  )
}

export default Track_now

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: wp(4),
  },
  saveButton: {
    height: hp(6),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(2),
  },
  errorText: {
    textAlign: 'left',
  }
})