import React from 'react'
import { StyleSheet, View, ScrollView } from 'react-native'
import ResponsiveText from '../../../Custom/RnText'
import useTheme from '../../../Redux/useTheme'
import { wp, hp } from '../../../Custom/Responsiveness'
import useAppText from '../../../Custom/AppText'
import HistoryOrderCard from '../../../Components/Settings_Components/HistoryOrderCard'
import { mockOrders } from '../../../Data/mockOrders'

const HistoryTab = () => {
  const { getTextColor, backgroundColor } = useTheme()
  const AppText = useAppText()

  const historyOrders = mockOrders.filter(order => 
    order.status === 'completed' || order.status === 'cancelled'
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: backgroundColor }]} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        <ResponsiveText 
          color={getTextColor()} 
          size={4} 
          weight={'600'}
          style={styles.title}
        >
          {AppText.ORDER_HISTORY}
        </ResponsiveText>
        
        {historyOrders.length > 0 ? (
          historyOrders.map(order => (
            <HistoryOrderCard key={order.id} order={order} />
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <ResponsiveText 
              color={getTextColor()} 
              size={3.5} 
              weight={'400'}
              style={styles.emptyText}
            >
              {AppText.NO_ORDER_HISTORY}
            </ResponsiveText>
          </View>
        )}
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
  },
  title: {
    marginBottom: hp(2),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(10),
  },
  emptyText: {
    textAlign: 'center',
  },
})

export default HistoryTab 