import { StyleSheet, View, SafeAreaView, ScrollView, KeyboardAvoidingView, Platform } from 'react-native'
import React from 'react'
import useTheme from '../../Redux/useTheme'
import CustomHeader from '../../Components/CustomHeader'
import useAppText from '../../Custom/AppText'
import EditProfileImage from '../../Components/Settings_Components/EditProfileImage'
import EditProfileForm from '../../Components/Settings_Components/EditProfileForm'

const Edit_Profilr = () => {
  const { backgroundColor } = useTheme();
  const AppText = useAppText();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader 
        title={AppText.EDIT_PROFILE} 
        showBellIcon={false} 
        leftIconType='back' 
      />
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{flex: 1}}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        
        <EditProfileImage />
        <EditProfileForm />
      </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Edit_Profilr;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});