import { StyleSheet, View, SafeAreaView } from 'react-native'
import React, { useState } from 'react'
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs'
import CustomHeader from '../../Components/CustomHeader'
import useAppText from '../../Custom/AppText'
import useTheme from '../../Redux/useTheme'
import { colors } from '../../Custom/Colors'
import OrdersTab from './Orders/OrdersTab'
import HistoryTab from './Orders/HistoryTab'
import Loader from '../../Custom/loader'

const Tab = createMaterialTopTabNavigator()

const Orders = () => {
  const { backgroundColor, getTextColor } = useTheme()
  const AppText = useAppText()
  const [loading, setLoading] = useState(false)


  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.MY_ORDERS} showBellIcon={false} leftIconType='back'/>
      
      {/* <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarActiveTintColor: colors.Light_theme_maincolour,
          tabBarInactiveTintColor: getTextColor(),
          tabBarPressColor: 'transparent',
          tabBarShowIcon: false,
          tabBarLabelStyle: {
            textTransform: 'none',
            fontSize: 14,
            fontWeight: '500',
          },
        }}
      >
        <Tab.Screen 
          name="Orders" 
          component={OrdersTab}
          options={{
            tabBarLabel: AppText.ORDERS
          }}
          initialParams={{ loading, setLoading }}
        />
        <Tab.Screen 
          name="History" 
          component={HistoryTab}
          options={{
            tabBarLabel: AppText.HISTORY
          }}
          initialParams={{ loading, setLoading }}
            
        />
      </Tab.Navigator> */}
          {/* Render OrdersTab directly instead of using Tab Navigator */}
          <OrdersTab loading={loading} setLoading={setLoading} />
      {loading && <Loader />}
    </SafeAreaView>
  )
}

export default Orders

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    backgroundColor: 'transparent',
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey,
  },
  tabIndicator: {
    backgroundColor: colors.Light_theme_maincolour,
    height: 2,
  },
})