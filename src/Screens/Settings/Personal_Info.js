import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React from 'react'
import useTheme from '../../Redux/useTheme'
import PersonalInfoHeader from '../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoHeader'
import PersonalInfoProfile from '../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoProfile'
import PersonalInfoCard from '../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoCard'
import Admin_Personal_Header from '../../Components/Settings_Components/Admin_Personal_Header'
import AdminInfoProfile from '../../Components/Settings_Components/AdminInfoProfile'
import AdminfoCard from '../../Components/Settings_Components/AdminfoCard'
// import PersonalInfoCard from '../../Components/Settings_Components/PersonalInfoCard'

const Personal_Info = () => {
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <Admin_Personal_Header />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <AdminInfoProfile />
        <AdminfoCard />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Personal_Info;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});