import React from 'react';
import {StyleSheet, View, SafeAreaView} from 'react-native';
import {hp} from '../../../Custom/Responsiveness';
import {colors} from '../../../Custom/Colors';
import {globalpath} from '../../../Custom/globalpath';
import SalonDetailsCard from '../../../Components/Settings_Components/SalonDetailsCard';
import SalonContentTabs from '../../../Components/Settings_Components/SalonContentTabs';
import Icon from '../../../Custom/Icon';
import useTheme from '../../../Redux/useTheme';
const SalonRatings = ({navigation}) => {
  const {backgroundColor} = useTheme();
  const salonData = {
    name: '<PERSON>',
    rating: '4.5/5',
    duration: '25/30 min',
    description: 'Professional barber with 10 years of experience specializing in modern haircuts and beard grooming.',
  };

  const recentWorkImages = [
    globalpath.childcut,
    globalpath.childcut1,
    globalpath.childcut2,
    globalpath.childcut3,
  ];

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={[styles.container,{backgroundColor:backgroundColor}]  }>
      <SalonDetailsCard
        onBackPress={handleBackPress}
        name={salonData.name}
        rating={salonData.rating}
        duration={salonData.duration}
      />
      <SalonContentTabs
        description={salonData.description}
        recentWorkImages={recentWorkImages}
      />
    </SafeAreaView>
  );
};

export default SalonRatings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});