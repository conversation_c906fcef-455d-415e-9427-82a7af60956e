import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React, { useState } from 'react'
import { useNavigation } from '@react-navigation/native'
import useAppText from '../../../Custom/AppText';
import { globalpath } from '../../../Custom/globalpath';
import Personal_MenuItem from '../../../Components/CustomerComponents/Personal_Area_Components/Personal_MenuItems';
import Personal_ProfileSection from '../../../Components/CustomerComponents/Personal_Area_Components/Personal_ProfileSection';
import { hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import CustomHeader from '../../../Components/CustomHeader';
import AppearanceSettings from '../../../Components/Settings_Components/AppearanceSettings';
import Barber_Header from '../../../Components/Barber_Section_Components/Barber_Header';
import Barber_ProfileSection from '../../../Components/Barber_Section_Components/Barber_Settings_Components/Barber_ProfileSection';
import Barber_Setting_MenuItems from '../../../Components/Barber_Section_Components/Barber_Settings_Components/Barber_Setting_MenuItems';

const Barber_Settings = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();
  const [showAppearanceModal, setShowAppearanceModal] = useState(false);
  const navigation = useNavigation();

  const menuItems = [
    { icon: globalpath.theme, title: AppText.LIGHT_MODE, isThemeSwitch: true, isLanguageSwitch: false },
    { icon: globalpath.language, title: AppText.ENGLISH, isThemeSwitch: false, isLanguageSwitch: true },
    // { icon: globalpath.notifications, title: AppText.MANAGE_SHEDULE },
    { icon: globalpath.Vratings, title: AppText.ACCOUNT },
    { icon: globalpath.help, title: AppText.HELP },
    { icon: globalpath.about, title: AppText.ABOUT },
  ];

  const handleMenuItemPress = (title) => {
    if (title === AppText.MANAGE_SHEDULE) {
      navigation.navigate('Manage_Barber_Schedule');
    } else if (title === AppText.ACCOUNT) {
      navigation.navigate('View_Ratings_Brber_Details');
    } else if (title === AppText.HELP) {
      navigation.navigate('PaymentMethod');
    } else if (title === AppText.ABOUT) {
      navigation.navigate('PaymentMethod');
    } else {
      console.log(`Pressed: ${title}`);
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <Barber_Header title={AppText.SETTINGS} showBellIcon={false} /> 
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Barber_ProfileSection />
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <Barber_Setting_MenuItems
              key={index}
              icon={item.icon}
              title={item.title}
              onPress={() => handleMenuItemPress(item.title)}
              isThemeSwitch={item.isThemeSwitch}
              isLanguageSwitch={item.isLanguageSwitch}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Barber_Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  menuContainer: {
    marginTop: hp(2),
  },
});