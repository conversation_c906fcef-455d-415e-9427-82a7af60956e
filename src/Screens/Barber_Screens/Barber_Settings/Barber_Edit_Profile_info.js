import React, { useEffect, useState , useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, KeyboardAvoidingView, SafeAreaView, Platform, ScrollView, Alert } from 'react-native';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import CustomTextInput from '../../../Components/CustomTextInput';
import { hp, wp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import EditProfileImage from '../../../Components/Settings_Components/EditProfileImage';
import EditCustomerProfileImage from '../../../Components/CustomerComponents/Personal_Area_Components/EditCustomerProfileImage';
import PersonalInfoHeader from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoHeader';
import CustomeProfileInfoHeader from '../../../Components/CustomerComponents/Personal_Area_Components/CustomeProfileInfoHeader';
import EditBarberProfileImage from '../../../Components/Barber_Section_Components/Barber_Settings_Components/EditBarberProfileImage';
import EditableBarberWorkingHours from '../../../Components/Barber_Section_Components/Barber_Settings_Components/EditableBarberWorkingHours';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { update_barber, update_barber_working_hours } from '../../../Services/API/Endpoints/Admin/Barber';
import Loader from '../../../Custom/loader';
import { useNavigation, useFocusEffect } from '@react-navigation/native';




const Edit_Personal_Profile_Info = () => {



  const { getsky_Theme,backgroundColor } = useTheme();
  const AppText = useAppText();
  const [barber, setBarber] = useState(null);
  const [workingHours, setWorkingHours] = useState([]);

  const fetchBarberData = async () => {
    try {
      const data = await AsyncStorage.getItem('barberData');
      if (data) {
        const parsed = JSON.parse(data);
        const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
        setBarber(barberInfo);
        setFormData({
          fullName: barberInfo.full_name || '',
          email: barberInfo.email || '',
          phoneNumber: barberInfo.phone_number || '',
          bio: barberInfo.description || '',
        });
        if (barberInfo.working_hours?.length > 0) {
          setWorkingHours(barberInfo.working_hours);
        }
      }
    } catch (error) {
      console.log('❌ Error fetching barber details:', error);
    }
  };

   useFocusEffect(
      useCallback(() => {
        fetchBarberData();
      }, []),
    );
  


  useEffect(() => {
    if (barber) {
      setFormData({
        fullName: barber.full_name || '',
        email: barber.email || '',
        phoneNumber: barber.phone_number || '',
        bio: barber.description || '',
      });
  
      if (barber.working_hours?.length > 0) {
        setWorkingHours(barber.working_hours);
      }
    }
  }, [barber]);
  
  

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    bio: '',
  });
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const [loading, setLoading] = useState(false);

  const handleSave = async () => {

    try {
      setLoading(true);

      // Validate form data
      if (!formData.fullName || !formData.email || !formData.phoneNumber) {
        Alert.alert('Validation Error', 'Please fill in all required fields.');
        setLoading(false);
        return;
      }

      console.log('Saving profile:', formData);
    

      // Prepare barber update payload
      const barberPayload = {
        full_name: formData.fullName,
        email: formData.email,
        phone_number: formData.phoneNumber,
        description: formData.bio
      };

      // Update barber profile
      const barberResponse = await update_barber(barber.id, barberPayload);
      console.log('✅ Barber profile updated successfully:', barberResponse.data);

      // Prepare working hours payload
      // const workingHoursPayload = {
      //   barber: barber.id,
      //   working_hours: workingHours
      // };

      // console.log("workingHoursPayload--->", workingHoursPayload)

      // // Update working hours
      // const workingHoursResponse = await update_barber_working_hours(barber.id, workingHoursPayload);
      // console.log('✅ Working hours updated successfully:', workingHoursResponse.data);

      // Update AsyncStorage with new data
      const updatedBarber = {
        ...barber,
        full_name: formData.fullName,
        email: formData.email,
        phone_number: formData.phoneNumber,
        description: formData.bio,
        working_hours: workingHours
      };

      await AsyncStorage.setItem('barberData', JSON.stringify(updatedBarber));

      Alert.alert('Success', 'Profile updated successfully!');

    } catch (error) {
      console.log('❌ Error updating profile:', error);
    
      // Try to extract and format server validation errors
      let errorMessage = 'Failed to update profile. Please try again.';
    
      if (error?.response?.data) {
        const errorData = error.response.data;
        // Combine all error messages into a single string
        errorMessage = Object.keys(errorData)
          .map(key => {
            const value = errorData[key];
            if (Array.isArray(value)) {
              return `${key}: ${value.join(', ')}`;
            } else {
              return `${key}: ${value}`;
            }
          })
          .join('\n');
      }
    
      Alert.alert('Error', errorMessage);
    }
     finally {
      setLoading(false);
    }
  };

  const handleWorkingHoursChange = async (updatedHours) => {
    console.log("THE UPDATED WORKING HOURS IS -->", updatedHours);
    setWorkingHours(updatedHours);
  
    try {
      // Update local barber object
      const updatedBarber = {
        ...barber,
        working_hours: updatedHours
      };
  
      // Save in state
      setBarber(updatedBarber);
  
      // Save in AsyncStorage
      await AsyncStorage.setItem('barberData', JSON.stringify(updatedBarber));
  
      console.log('✅ Working hours updated in AsyncStorage');
    } catch (error) {
      console.error('❌ Failed to update Async barber data:', error);
    }
  };
  

  return (
    <SafeAreaView style={{flex:1,backgroundColor:backgroundColor}}>
      <KeyboardAvoidingView behavior={Platform.OS?"padding":""} style={{flex:1}}>
      <CustomeProfileInfoHeader/>
        <ScrollView style={{ flex:1}}>
          <View style={styles.container}>
      <EditBarberProfileImage/>

      <CustomTextInput
        label={AppText.FULL_NAME}
        placeholder={AppText.FULL_NAME}
        value={formData.fullName}
        onChangeText={(text) => handleChange('fullName', text)}
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.EMAIL}
        placeholder={AppText.EMAIL}
        value={formData.email}
        onChangeText={(text) => handleChange('email', text)}
        keyboardType="email-address"
        editable={false}
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.PHONE_NUMBER}
        placeholder={AppText.PHONE_NUMBER}
        value={formData.phoneNumber}
        onChangeText={(text) => handleChange('phoneNumber', text)}
        keyboardType="phone-pad"
        inputStyle={{ backgroundColor: getsky_Theme() }}
      />
      <CustomTextInput
        label={AppText.BIO}
        placeholder={AppText.BIO}
        value={formData.bio}
        onChangeText={(text) => handleChange('bio', text)}
        multiline={true}
        numberOfLines={4}
        inputStyle={{
          backgroundColor: getsky_Theme(),
          height: hp(15),
        }}
      />

     <EditableBarberWorkingHours
        workingHours={workingHours}
        onWorkingHoursChange={handleWorkingHoursChange}
      />



    </View>
        </ScrollView>
        <View style={{marginHorizontal:wp(4), paddingVertical:hp(1),  backgroundColor: backgroundColor}}>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={handleSave}
            disabled={loading}
          >
            <ResponsiveText
              color={colors.white}
              size={4.5}
              weight={'bold'}
            >
              {loading ? 'Saving...' : AppText.SAVE}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

      </KeyboardAvoidingView>
      {loading && <Loader />}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: wp(4),
  },
  saveButton: {
    height: hp(6),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: hp(4),
    // marginBottom: hp(2),
  },
});

export default Edit_Personal_Profile_Info;