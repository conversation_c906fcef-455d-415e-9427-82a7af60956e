import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '../../../Custom/Colors';
import { hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { get_barber_Recent_work, get_service, get_serviece_by_barber } from '../../../Services/API/Endpoints/Admin/Barber';

// Component imports
import BarberProfileHeader from '../../../Components/BarberProfile/BarberProfileHeader';
import BarberDescription from '../../../Components/BarberProfile/BarberDescription';
import TabSelector from '../../../Components/BarberProfile/TabSelector';
import ServicesList from '../../../Components/BarberProfile/ServicesList';
import RecentWorkGallery from '../../../Components/BarberProfile/RecentWorkGallery';
import LoadingIndicator from '../../../Components/Common/LoadingIndicator';
import { View } from 'react-native-animatable';
import useAppText from '../../../Custom/AppText';

const View_Ratings_Brber_Details = ({ navigation , route }) => {
  const {barber} = route.params;
  console.log("the barber details are in params is ----- --------",barber)
  const { backgroundColor } = useTheme();
    const AppText = useAppText();
  // State variables
  const [selectedTab, setSelectedTab] = useState(AppText.SERVICES); // Default Selected Tab
  const [fullname, setFullname] = useState('');
  const [email, setEmail] = useState('');
  const [description, setDescription] = useState('');
  const [profileImage, setProfileImage] = useState('');
  const [services, setServices] = useState([]);
  const [recentWork, setRecentWork] = useState([]);

  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [servicesLoading, setServicesLoading] = useState(true);
  const [recentWorkLoading, setRecentWorkLoading] = useState(true);

  const handleBackPress = () => {
    navigation.goBack();
  };

  // Fetch barber details from AsyncStorage
  const fetchBarberDetails = async () => {
    try {
      setIsLoading(true);
      const data = await AsyncStorage.getItem('barberData');
      if (data) {
        const parsed = JSON.parse(data);
        const barberInfo = Array.isArray(parsed) ? parsed[0] : parsed;
        console.log('ℹ️ Barber data fetched from AsyncStorage:', barberInfo);
        setFullname(barberInfo.full_name);
        setEmail(barberInfo.email);
        setDescription(barberInfo.description || '');
        setProfileImage(barberInfo.profile_image || '');

        // Fetch services and recent work
        await Promise.all([
          fetchBarberServices(barberInfo.id),
          fetchBarberRecentWork(barberInfo.id)
        ]);
      } else {
        console.log('ℹ️ No barber data found in AsyncStorage');
      }
    } catch (error) {
      console.log('❌ Error fetching barber details:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Fetch barber services
  const fetchBarberServices = async (barberId) => {
    try {
      setServicesLoading(true);
      const serviceRelationships = await get_serviece_by_barber(barberId);
      const serviceIds = serviceRelationships.map(item => item.service);

      const servicesData = [];
      for (const serviceId of serviceIds) {
        try {
          const serviceDetails = await get_service(serviceId);
          servicesData.push(serviceDetails);
        } catch (error) {
          console.error(`❌ Failed to fetch service ${serviceId}:`, error);
        }
      }

      setServices(servicesData);
      return servicesData;
    } catch (error) {
      console.error('❌ Failed to fetch barber services:', error);
    } finally {
      setServicesLoading(false);
    }
  };

  // Fetch barber recent work
  const fetchBarberRecentWork = async (barberId) => {
    try {
      setRecentWorkLoading(true);
      const recentWorkData = await get_barber_Recent_work(barberId);
      setRecentWork(recentWorkData);
      return recentWorkData;
    } catch (error) {
      console.error('❌ Failed to fetch barber recent work:', error);
    } finally {
      setRecentWorkLoading(false);
    }
  };

  // Handle refresh
  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    fetchBarberDetails();
  }, []);

  // Initial data fetch
  useEffect(() => {
    console.log('Fetching barber details...');
    fetchBarberDetails();
  }, []);

  // Render loading state
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
        <StatusBar backgroundColor={colors.Light_theme_maincolour} barStyle="light-content" />
        <BarberProfileHeader
          fullname=""
          email=""
          onBackPress={handleBackPress}
          isLoading={true}
          profile_image=""
        />
        <LoadingIndicator />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      <StatusBar backgroundColor={colors.Light_theme_maincolour} barStyle="light-content" />

      {/* Barber Profile Header */}
      <BarberProfileHeader
        fullname={fullname}
        email={email}
        onBackPress={handleBackPress}
        isLoading={isLoading}
        profile_image={profileImage}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
          stickyHeaderIndices={[1]} // Index of the sticky element in children array
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} colors={[colors.Light_theme_maincolour]} />
        }
      >
        {/* Barber Description */}
        <BarberDescription description={description} />

  {/* 1: Tab Selector - this will be sticky */}
  <View style={[styles.stickyTabContainer,{backgroundColor:backgroundColor}]}>
        <TabSelector
          tabs={[
            { key: AppText.SERVICES, label: AppText.SERVICES },
            { key: AppText.RECENT_WORK, label: AppText.RECENT_WORK }
          ]}
          selectedTab={selectedTab}
          onTabChange={setSelectedTab}
        />
        </View>
        {/* Content based on selected tab */}
        {selectedTab === AppText.SERVICES ? (
          <ServicesList
            services={services}
            isLoading={servicesLoading}
          />
        ) : (
          <RecentWorkGallery
            recentWork={recentWork}
            isLoading={recentWorkLoading}
          />
          // <View></View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default View_Ratings_Brber_Details;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: hp(5),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stickyTabContainer: {
  // backgroundColor: colors.white, // Ensure background doesn't overlap
  zIndex: 10, // Helps with stacking
},
});
