import { SafeAreaView, StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native'
import React, { useState } from 'react'
import Barber_Header from '../../../Components/Barber_Section_Components/Barber_Header'
import useAppText from '../../../Custom/AppText'
import ProductImageCarousel from '../../../Components/Products/ProductImageCarousel'
import { SheduleImage } from '../../../Mocks/CustomerMock_data'
import useTheme from '../../../Redux/useTheme'
import Icon from '../../../Custom/Icon'
import { globalpath } from '../../../Custom/globalpath'
import { hp, wp } from '../../../Custom/Responsiveness'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'

const WeekDayCards = ({ selectedDay, onDaySelect }) => {
  const { getDark_Theme, getTextColor } = useTheme();
  
  // Get current week's dates
  const getCurrentWeekDates = () => {
    const today = new Date();
    const currentDay = today.getDay();
    const dates = [];
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - currentDay + i);
      dates.push({
        day: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
        date: date.getDate()
      });
    }
    return dates;
  };

  const weekDates = getCurrentWeekDates();
  
  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.weekDaysContainer}
    >
      {weekDates.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.card,
            {
              borderColor: getDark_Theme(),
              backgroundColor: selectedDay === item.day ? colors.Light_theme_maincolour : 'transparent',
              width: wp(25),
            },
          ]}
          onPress={() => onDaySelect(item.day)}
        >
          {selectedDay === item.day && (
            <Icon
              source={globalpath.tick}
              size={wp(4)}
              style={styles.tickIcon}
            />
          )}
          <ResponsiveText
            color={selectedDay === item.day ? colors.white : getTextColor()}
            size={4.5}
            weight="500"
          >
            {item.day}
          </ResponsiveText>
          <ResponsiveText
            color={selectedDay === item.day ? colors.white : getTextColor()}
            size={3.5}
            weight="500"
            margin={[hp(0.5), 0, 0, 0]}
          >
            {item.date}
          </ResponsiveText>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const Manage_Barber_Schedule = () => {
  const [location, Setlocation] = useState("10 Downing Street, London SW1A 2AA, United Kingdom")
  const [selectedDay, setSelectedDay] = useState(null);
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor, getDarK_mode_LightGrayBackground, getBellBackground, getsky_Theme } = useTheme();
  
  const handleDaySelect = (day) => {
    setSelectedDay(day);
  };

  const handleSave = () => {
    // Add your save logic here
    console.log('Saving schedule for day:', selectedDay);
  };
    
  return (
    <SafeAreaView style={{flex:1,backgroundColor:backgroundColor}}>
      <Barber_Header leftIconType='back' title={AppText.EDIT_SHEDULE}/>
      <View style={styles.ImageView}>
        <Icon source={globalpath.back1} style={styles.ImageStyle} resizeMode={'cover'}></Icon>
        <View style={styles.Label_and_Location_Container}>
          <ResponsiveText size={5} weight={'500'} color={getTextColor()} >Turkish Hair Salon</ResponsiveText>
          <View style={styles.Icon_Location}>
            <Icon source={globalpath.loc} size={hp(1.8)} style={styles.IconStyleofLocation} margin={[hp(.3),0,0,0]}></Icon>
            <ResponsiveText size={3.6} weight={'500'} color={getTextColor()} margin={[0,0,0,wp(5)]}>{location}</ResponsiveText>
          </View>
          <View style={styles.Date_Details_AND_Current_week}>
            <ResponsiveText size={4.3} weight={'500'} color={getTextColor()}>{AppText.SELECT_DATE_TIME}</ResponsiveText>
            <View style={[styles.CurrentWeek,{backgroundColor:colors.lightGrey1}]}>
              <ResponsiveText size={3.5} weight={'500'} color={getTextColor()}>{AppText.CURRENT_WEEK}</ResponsiveText>
            </View>
          </View>
          <WeekDayCards selectedDay={selectedDay} onDaySelect={handleDaySelect} />
        </View>
      </View>
      <TouchableOpacity 
        style={[styles.saveButton, { backgroundColor: colors.Light_theme_maincolour }]}
        onPress={handleSave}
      >
        <ResponsiveText size={4.5} weight="500" color={colors.white}>
          {AppText.SAVE}
        </ResponsiveText>
      </TouchableOpacity>
    </SafeAreaView>
  )
}

export default Manage_Barber_Schedule

const styles = StyleSheet.create({
  ImageView:{
    flex:1,
  },
  ImageStyle:{
    height:hp(30),
    width:wp(100),
    bottom:hp(1)
  },
  Label_and_Location_Container:{
    marginHorizontal:wp(5),
    marginVertical:hp(2),
  },
  Icon_Location:{
    flexDirection:"row",
    marginVertical:hp(2),
    marginHorizontal:wp(1.5)
  },
  IconStyleofLocation:{
    position:'absolute',
  },
  Date_Details_AND_Current_week:{
    flexDirection:"row",
    alignItems:'center',
    justifyContent:"space-between",
  },
  CurrentWeek:{
    paddingHorizontal:wp(2),
    paddingVertical:hp(.5),
    borderRadius:wp(1.5)
  },
  weekDaysContainer: {
    flexDirection: 'row',
    paddingHorizontal: wp(1),
    paddingVertical: hp(1),
  },
  card: {
    height: hp(11),
    borderWidth: 1,
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: wp(1),
    position: 'relative',
  },
  tickIcon: {
    position: 'absolute',
    top: hp(0.5),
    right: wp(1),
  },
  saveButton: {
    // position: 'absolute',
    // bottom: hp(3),
    // left: wp(5),
    // right: wp(5),
    height: hp(6),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: wp(5),
    bottom:hp(10)
  },
})