import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React from 'react'
import useTheme from '../../../Redux/useTheme'
import PersonalInfoHeader from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoHeader';
import PersonalInfoProfile from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoProfile';
import PersonalInfoCard from '../../../Components/CustomerComponents/Personal_Area_Components/PersonalInfoCard';
import <PERSON>_Header from '../../../Components/Barber_Section_Components/Barber_Header';
import Barber_Profile_Header from '../../../Components/Barber_Section_Components/Barber_Settings_Components/Barber_Profile_Header';
import BarberInfoProfile from '../../../Components/Barber_Section_Components/Barber_Settings_Components/BarberInfoProfile';
import BarberInfoCard from '../../../Components/Barber_Section_Components/Barber_Settings_Components/BarberInfoCard';
import BarberWorkingHours from '../../../Components/Barber_Section_Components/Barber_Settings_Components/BarberWorkingHours';

const Barber_Profile_Info = () => {
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <Barber_Profile_Header/>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <BarberInfoProfile />
        <BarberInfoCard />
        <BarberWorkingHours />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Barber_Profile_Info;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});