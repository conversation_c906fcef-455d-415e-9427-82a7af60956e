import { StyleSheet, View, SafeAreaView, TouchableOpacity, TextInput, KeyboardAvoidingView, ScrollView, Alert } from 'react-native'
import React, { useState } from 'react'
import <PERSON>_Header from '../../../Components/Barber_Section_Components/Barber_Header'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import ResponsiveText from '../../../Custom/RnText'
import { colors } from '../../../Custom/Colors'
import { hp, wp } from '../../../Custom/Responsiveness'

const Barber_Contact_Screen = () => {
  const AppText = useAppText();
  const { backgroundColor, getTextColor , getsky_Theme } = useTheme();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');

  const [selectedOption, setSelectedOption] = useState(AppText.COMPLAINT);

  const validateEmail = email => {
    const regex = /^\S+@\S+\.\S+$/;
    return regex.test(email);
  };

  const handleSubmit = () => {
    if (!name || !email || !message) {
      Alert.alert(AppText.ATTENTION, AppText.ALL_FIELDS);
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert(AppText.INVALID_EMAIL, AppText.VALID_EMAIL);
      return;
    }

    Alert.alert('Submitted', `${AppText.THANKS} ${selectedOption}!`);
    setName('');
    setEmail('');
    setMessage('');
    console.log("---BARBER CONTACT SCREEN DETAILS---")
    console.log('Name:', name);
    console.log('Email:', email);
    console.log('Message:', message);
    console.log('Selected Option:', selectedOption);
  };


  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <KeyboardAvoidingView behavior={Platform.OS==="ios"?"height":"padding"}>
     <View style={styles.HeaderALignment}>
     <Barber_Header title={AppText.CONTACT} />
     </View>
      <ScrollView
        contentContainerStyle={[
          styles.container,
          {backgroundColor: backgroundColor},
        ]}>
       

        {/* Options */}
        <ResponsiveText color={getTextColor()} weight={'600'} size={4.5} textAlign={'center'} margin={[hp(4),0,0,0]}>{AppText.APPRECIATE}</ResponsiveText>
        {/* <Icon source={globalpath.logo} tintColor={getTextColor()} size={hp(8)} ></Icon> */}
        <View
          style={[styles.optionContainer, {backgroundColor: backgroundColor}]}>
         {/* {[AppText.COMPLAINT, AppText.INFORMATION, AppText.FEEDBACK].map( for three options  */}

          {[AppText.COMPLAINT,  AppText.FEEDBACK].map(
            option => (
              <TouchableOpacity
                key={option}
                onPress={() => setSelectedOption(option)}
                style={[
                  styles.optionButton,
                  selectedOption === option && styles.optionSelected,
                ]}>
                <ResponsiveText
                  color={selectedOption === option ? '#fff' : colors.lightGrey5}
                  style={styles.optionText} size={3.7}>
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </ResponsiveText>
              </TouchableOpacity>
            ),
          )}
        </View>

        {/* Dynamic Title */}
        <ResponsiveText style={styles.subHeading} color={colors.Light_theme_maincolour} weight={'600'}>
          {selectedOption === AppText.COMPLAINT
            ? AppText.REGISTER_COMPLAINT
            : selectedOption === AppText.INFORMATION
            ? AppText.REQUEST_INFORMATION
            : AppText.SEND_FEEDBACK}
        </ResponsiveText>

        {/* Input Fields */}
        <TextInput
          style={[styles.input, {backgroundColor: getsky_Theme(),color:getTextColor()}]}
          placeholder={AppText.ENTER_YOUR_NAME}
          value={name}
          onChangeText={setName}
          placeholderTextColor={colors.lightGrey5}
        />
        <TextInput
          style={[styles.input, {backgroundColor: getsky_Theme(),color:getTextColor()}]}
          placeholder={AppText.ENTER_YOUR_EMAIL}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          placeholderTextColor={colors.lightGrey5}
        />
        <TextInput
          style={[
            styles.input,
            styles.textArea,
            {backgroundColor: getsky_Theme(),color:getTextColor()},
          ]}
          placeholder={AppText.ENTER_YOUR_MESSAGE}
          value={message}
          onChangeText={setMessage}
          placeholderTextColor={colors.lightGrey5}
          multiline
          numberOfLines={9}
        />

{/* Button for sending attachments */}
        {/* <TouchableOpacity
          style={[styles.Add_Attcahmenst, {backgroundColor: getsky_Theme()}]}>
          <ResponsiveText size={4} color={getTextColor()}>
            {AppText.ATTACHMENTS}
          </ResponsiveText>
          <Icon
            source={globalpath.attachment}
            tintColor={getTextColor()}
            margin={[0, 0, 0, wp(2)]}></Icon>
        </TouchableOpacity> */}

        {/* Submit Button */}
        <TouchableOpacity style={styles.submitBtn} onPress={handleSubmit}>
          <ResponsiveText color={colors.white} size={3.9} weight={'600'}>{AppText.SEND}</ResponsiveText>
        </TouchableOpacity>
      </ScrollView>

      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default Barber_Contact_Screen

const styles = StyleSheet.create({
 container: {
    paddingHorizontal: wp(5),
    backgroundColor: '#fff',
    flexGrow: 1,
  },
  heading: {
    fontSize: 26,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 20,
    color: '#222',
  },
  subHeading: {
    fontSize: 18,
    fontWeight: '500',
    color: '#444',
    marginVertical: 15,
    textAlign: 'center',
  },
  optionContainer: {
    marginTop: hp(3),
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#eee',
    // padding: 5,
    // borderRadius: 10,
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
  },
  optionButton: {
    flex: 1,
    paddingVertical: hp(1.5),
    marginHorizontal: wp(2),
    borderRadius: wp(1.5),
    alignItems: 'center',
  },
  optionSelected: {
    backgroundColor: colors.Light_theme_maincolour,
  },
  optionText: {
    color: '#555',
    fontWeight: '500',
  },
  optionTextSelected: {
    color: '#fff',
  },
  input: {
    // backgroundColor: '#f2f2f2',
    borderRadius: wp(2),
    padding: wp(3),
    marginVertical: hp(1),
    fontSize: 15,
    borderWidth: 1,
    borderColor: colors.lightGrey1,
    height: hp(6),
  },
  textArea: {
    height: hp(20),
    textAlignVertical: 'top',
  },
  submitBtn: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: hp(2.2),
    borderRadius: wp(2),
    marginTop: hp(3),
    alignItems: 'center',
    justifyContent:"center"
  },
  submitText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  Add_Attcahmenst: {
    padding: wp(5),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.lightGrey3,
    borderRadius: wp(3),
    marginTop: hp(2),
    borderWidth: 1,
    borderColor: colors.lightGrey1,
  },
  HeaderALignment:{
    paddingTop:hp(1)
  }
})