import { KeyboardAvoidingView, SafeAreaView, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import Barber_Job_Progress_Stepper from '../../../Components/Barber_Section_Components/Barber_Home_Components/Weekly/Barber_Job_Progress_Stepper'
import Your_Book_Service from '../../../Components/Bookings/Your_Book_Service'
import CustomHeader from '../../../Components/CustomHeader'
import Book_Barber_Details from '../../../Components/Bookings/Book_Barber_Details'
import Barber_customer__Details from '../../../Components/Barber_Section_Components/Barber_Home_Components/Weekly/Barber_Customer_Details'
import { ScrollView } from 'react-native-gesture-handler'
import { hp ,wp} from '../../../Custom/Responsiveness'
import { colors } from '../../../Custom/Colors'
import ResponsiveText from '../../../Custom/RnText'
import useAppText from '../../../Custom/AppText'
import useTheme from '../../../Redux/useTheme'
import { useFocusEffect } from '@react-navigation/core'
import { get_customer ,get_service,get_barber} from '../../../Services/API/Endpoints/barber/appointment_details'
import Loader from '../../../Custom/loader'

const Barber_Pendings_Jobs = ({route}) => { 
  const appointment=route.params.appointment;
  console.log("details in barber_pending---",  appointment)
  const Apptext=useAppText();
  const {getTextColor,backgroundColor,getsky_Theme,getDark_Theme}=useTheme()
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true); 
    const [customer, setCustomer] = useState(null);
    const [phone, setphone] = useState(null);
    const [email, setemail] = useState(null);
    const [services, setServices] = useState([]);
    const [services_name, setServices_name] = useState([]);
    const [services_price, setServices_price] = useState([]);
    const [barberdetails, setbarberdetails] = useState([]);




  // api of get customer details
    useFocusEffect(
      useCallback(() => {
        fetch_customer_details();
        fetch_services_details();
        fetch_barber_details();
      }, []),
    );
  useEffect(() => {
    fetch_customer_details();
    fetch_services_details();
    fetch_barber_details();
  },[appointment])
    const fetch_customer_details = async () => {
      try {
        setLoading(true);
        setRefreshing(true);
  
        const response = await get_customer(appointment.customer);
        console.log('🟢 customer detsils fetch ', response);
        setCustomer(response.full_name);
        setphone(response.phone_number);
        setemail(response.email);
      } catch (error) {
        console.error('❌ Failed to fetch cutomer detsils', error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    };

    const fetch_services_details = async () => {
      try {
        setLoading(true);
        setRefreshing(true);
        
        if (!appointment?.services || !Array.isArray(appointment.services)) {
          console.log('No services found or services is not an array');
          setServices([]);
          return;
        }
    
        // Create an array of promises
        const servicePromises = appointment.services.map(async serviceId => {
          try {
            // If serviceId is an object with id property, use that id
            const id = typeof serviceId === 'object' ? serviceId.id : serviceId;
            const response = await get_service(id);
            console.log('🟢 Service details fetched', response);
            return response;
          } catch (error) {
            console.error(`❌ Failed to fetch details for service ID ${JSON.stringify(serviceId)}`, error);
            return null;
          }
        });
    
        // Wait for all promises to resolve
        const serviceDetails = await Promise.all(servicePromises);
        console.log('🟢 All service details fetched', serviceDetails);
        
        // Filter out null values from failed requests
        const validServices = serviceDetails.filter(service => service !== null);
        
        console.log('🟢 All service details fetched', validServices);
        setServices(validServices);
        
      } catch (error) {
        console.error('❌ General error in fetch_services_details', error);
        setServices([]);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    };
    const fetch_barber_details = async () => {
      console.log("fetch_barber_details called");
      try {
        setLoading(true);
        setRefreshing(true);
  
        const response = await get_barber(appointment.shop);
        setbarberdetails(response);
        console.log('🟢 barber details fetch is', response);
      } catch (error) {
        console.error('❌ Failed to fetch barber  detsils', error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    };
    
  // convert time to 12 hour format
  const formatTime = (timeString) => {
    if (!timeString) return '';
    
    const [hours, minutes] = timeString.split(':');
    const date = new Date(1970, 0, 1, hours, minutes);
    
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  

  return (
    <SafeAreaView style={{backgroundColor:backgroundColor}}>
          <KeyboardAvoidingView 
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
            keyboardVerticalOffset={Platform.OS === 'ios' ? hp(1) : 0}
          >
      <ScrollView 
      contentContainerStyle={{paddingBottom:hp(5)}}
       showsVerticalScrollIndicator={false} 
       style={{backgroundColor:backgroundColor}}
            keyboardShouldPersistTaps="handled"
       >
      <CustomHeader leftIconType='back' title={Apptext.PENDINGS} showBellIcon={false}/>
      
      <Barber_Job_Progress_Stepper
      appointment={appointment}
      />
      {/* <Barber_Appointment_Scheduled/> */}
      <View style={[styles.AppointmentDetails,{backgroundColor:backgroundColor,borderWidth:1,borderColor:getDark_Theme()}]}>
          <ResponsiveText size={4.7} weight={'500'} color={getTextColor()} margin={[hp(0.5),0,wp(2),0]}>{Apptext.APPOINTMENT_SHEDULE}</ResponsiveText>
          <View style={styles.AppointmentDate_tme}>
            <View style={styles.DateTimeContainer}>
              <ResponsiveText size={4.2}  color={colors.black} >{route?.params?.appointment?.date}</ResponsiveText>
            </View>
            <View style={styles.DateTimeContainer}>
              <ResponsiveText size={4.2}  color={colors.black} >{formatTime(route?.params?.appointment?.start_time)}</ResponsiveText>
            </View>
          </View>
        </View>
      
      {/* <Your_Book_Service/> */}
      <Barber_customer__Details 
      customer={customer}
      phone={phone}
      email={email}
      services={services}
      barberdetails={barberdetails}
      appointment={appointment}

       />
      
      </ScrollView>
      </KeyboardAvoidingView>
      {loading ? <Loader/> : undefined}
    </SafeAreaView>
  )
}
export default Barber_Pendings_Jobs

const styles = StyleSheet.create({
   AppointmentDetails:{
      marginHorizontal:wp(5),
      marginTop:hp(1),
      elevation:5,
      paddingHorizontal:wp(4),
      paddingVertical:hp(2),
      borderRadius:wp(1.5)
    },
    AppointmentDate_tme:{
      flexDirection:"row",
      alignItems:"center",
      // backgroundColor:"pink",
      marginHorizontal:wp(-1.5)
  
    },
    DateTimeContainer:{
      paddingHorizontal:wp(5),
      backgroundColor:colors.lightGrey4,
      paddingVertical:hp(.7),
      borderRadius:wp(5),
      // marginLeft:wp(3),
      marginTop:hp(.7),
      marginHorizontal:wp(1)
    },
})