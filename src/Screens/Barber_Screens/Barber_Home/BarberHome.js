import { StyleSheet, View, SafeAreaView, Text } from 'react-native';
import React, { useEffect, useState, useCallback } from 'react';
import { wp, hp } from '../../../Custom/Responsiveness';
import Barber_Header from '../../../Components/Barber_Section_Components/Barber_Header';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Calendar_Header from '../../../Components/Barber_Section_Components/Calendar_Header';
import Weekly_Calendar_Header from '../../../Components/Barber_Section_Components/Weekly_Calendar_Header';
import { useNavigation } from '@react-navigation/native';
import Barber_ViewToggle from '../../../Components/Barber_Section_Components/Barber_Home_Components/Barber_ViewToggle';
import Barber_DailyView from '../../../Components/Barber_Section_Components/Barber_Home_Components/Barber_DailyView';
import Barber_Weekly_View from '../../../Components/Barber_Section_Components/Barber_Home_Components/Weekly/Barber_Weekly_View';
import ShopSelector from '../../../Components/Barber_Section_Components/Barber_Home_Components/ShopSelector';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { get_barber_deatils } from '../../../Services/API/Endpoints/barber/dashboard';
import { get_all_shops } from '../../../Services/API/Endpoints/Admin/Salons';
import { get_shop_by_id } from '../../../Services/API/Endpoints/Admin/Salons';

const BarberHome = ({ route }) => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();
  const navigation = useNavigation();
  const [viewType, setViewType] = useState('DAILY');
  const [userRole, setUserRole] = useState(null);
  const [salonData, setSalonData] = useState(null);
  const [AllShops, setAllShops] = useState(null);
  const [selectedShop, setSelectedShop] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState(null);

  const handleViewToggle = (type) => {
    setViewType(type);
  };

  const handleShopSelect = (shop) => {
    setSelectedShop(shop);
    setSalonData(shop);
  };

  const handleAddPress = () => {
    if (viewType === 'WEEKLY') {
      navigation.navigate('Barber_Pendings_Jobs');
    }
    console.log('Add button pressed');
  };

  // Handle date selection from Calendar_Header
  const handleDateSelect = useCallback((date) => {
    setSelectedDate(date);
    console.log('Selected date:', date);
  }, []);

  // Handle month selection from Weekly_Calendar_Header
  const handleMonthSelect = useCallback((month) => {
    setSelectedMonth(month);
    console.log('Selected month:', month);
  }, []);

  useEffect(() => {
    const getUserRole = async () => {
      try {
        const userData = await AsyncStorage.getItem('userData');
        if (userData) {
          const { role } = JSON.parse(userData);
          console.log('User role:', role);
          setUserRole(role);

          // Handle admin user
          if (role === 'admin') {
            if (route?.params?.salonData) {
              setSalonData(route.params.salonData);
              setSelectedShop(route.params.salonData);
            }

            if (route?.params?.AllShopsData) {
              setAllShops(route.params.AllShopsData);

              // If no salon data is set yet but we have shops, set the first shop as selected
              if (!route?.params?.salonData && route.params.AllShopsData.length > 0) {
                setSalonData(route.params.AllShopsData[0]);
                setSelectedShop(route.params.AllShopsData[0]);
              }
            }
          }
          // Handle barber user
          else if (role === 'barber') {
            fetchBarberShopData();
          }

          console.log('Salon data inside barber home', salonData);
        }
      } catch (error) {
        console.error('Error getting user role:', error);
      }
    };

    const fetchBarberShopData = async () => {
      try {
        // Get barber details to find their shop ID
        const response = await get_barber_deatils();
        console.log('Barber details fetched:', response);

        if (response && response.length > 0 && response[0].shop) {
          const shopId = response[0].shop;

          // Get shop details using the shop ID
          const shopDetails = await get_shop_by_id(shopId);
          console.log('Shop details fetched:', shopDetails);

          if (shopDetails) {
            setSalonData(shopDetails);
            setSelectedShop(shopDetails);

            // Get all shops to display in the selector (even though it will be disabled)
            const allShopsResponse = await get_all_shops();
            if (allShopsResponse && allShopsResponse.length > 0) {
              setAllShops(allShopsResponse);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching barber shop data:', error);
      }
    };

    getUserRole();
  }, [route?.params?.salonData, route?.params?.AllShopsData]);


          console.log('AllShopsData data inside barber home', AllShops);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>


      {viewType === 'DAILY' && (
        <Calendar_Header
          onPress={() => {/* handle bell press */}}
          onDateSelect={handleDateSelect}
          selectedDate={selectedDate}
          leftIconType={userRole === 'admin' ? 'back' : 'bars'}
        />
      )}

      {viewType === 'WEEKLY' && (
        <Weekly_Calendar_Header
          onPress={() => {/* handle bell press */}}
          onMonthSelect={handleMonthSelect}
          selectedMonth={selectedMonth}
          leftIconType={userRole === 'admin' ? 'back' : 'bars'}
        />
      )}

      <View style={styles.toggleContainer}>
        {/* Show ShopSelector for both admin and barber users */}
        {((userRole === 'admin' && AllShops && AllShops.length > 0) ||
          (userRole === 'barber' && selectedShop)) && (
          <ShopSelector
            currentShop={selectedShop}
            allShops={AllShops || []}
            onShopSelect={handleShopSelect}
            isTouchable={userRole === 'admin'} // Only touchable for admin users
          />
        )}
        <Barber_ViewToggle onToggle={handleViewToggle} />
      </View>

      {viewType === 'DAILY' && (
        <Barber_DailyView
          onAddPress={handleAddPress}
          salonData={salonData}
          userRole={userRole}
          AllShopsData={AllShops}
          selectedDate={selectedDate}
        />
      )}

      {viewType === 'WEEKLY' && (
        <Barber_Weekly_View
          onAddPress={handleAddPress}
          salonData={salonData}
          userRole={userRole}
          selectedMonth={selectedMonth}
        />
      )}
    </SafeAreaView>
  );
};

export default BarberHome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(1),
    marginBottom: hp(1),

  },
  salonInfoContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  salonName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  salonAddress: {
    fontSize: 14,
    color: '#666',
  }
});
