import { StyleSheet, View, SafeAreaView, ScrollView } from 'react-native'
import React from 'react'
import useTheme from '../Redux/useTheme'
import CustomHeader from '../Components/CustomHeader'
import useAppText from '../Custom/AppText';
import ResponsiveText from '../Custom/RnText';
import { wp, hp } from '../Custom/Responsiveness';
import ManagementCard from '../Components/ManagementCard';

const Management = ({ navigation }) => {
  const AppText = useAppText();
  const { 
    themeColor,
    getDarK_mode_LightGrayBackground, 
    backgroundColor, 
    getTextColor,
    getSecondaryTextColor,
    getLightGrayBackground,
    getborderTextColor,
    getBellBackground,
    getDark_Theme
  } = useTheme();
  
  return (
    <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader title={AppText.MANAGEMENT} />
      <ScrollView style={styles.container}>
        <View style={[styles.header, {backgroundColor: getBellBackground()}]}>
          <ResponsiveText color={getTextColor()} weight={'700'} size={5}>
            {AppText.MANAGEMENT_TITLE}
          </ResponsiveText>
          <ResponsiveText color={getborderTextColor()} size={3.8} margin={[hp(1), 0, 0, 0]}>
            {AppText.MANAGEMENT_DESC}
          </ResponsiveText>
        </View>

        <ManagementCard 
          title={AppText.SALONS}
          onPress={() => navigation.navigate('Salons')}
          getSecondaryTextColor={getSecondaryTextColor}
          getTextColor={getTextColor}
          getBellBackground={getBellBackground}
          getDark_Theme={getDark_Theme}
        />

        <ManagementCard 
          title={AppText.SERVICES}
          onPress={() => navigation.navigate('Service')}
          getSecondaryTextColor={getSecondaryTextColor}
          getTextColor={getTextColor}
          getBellBackground={getBellBackground}
          getDark_Theme={getDark_Theme}
        />

        <ManagementCard 
          title={AppText.BARBER}
          onPress={() => navigation.navigate('Barber')}
          getSecondaryTextColor={getSecondaryTextColor}
          getTextColor={getTextColor}
          getBellBackground={getBellBackground}
          getDark_Theme={getDark_Theme}
        />

        <ManagementCard 
          title={AppText.PRODUCTS}
          onPress={() => navigation.navigate('Products')}
          getSecondaryTextColor={getSecondaryTextColor}
          getTextColor={getTextColor}
          getBellBackground={getBellBackground}
          getDark_Theme={getDark_Theme}
        />

        <ManagementCard 
          title={AppText.GIFT_CARDS}
          onPress={() => navigation.navigate('GiftCards')}
          getSecondaryTextColor={getSecondaryTextColor}
          getTextColor={getTextColor}
          getBellBackground={getBellBackground}
          getDark_Theme={getDark_Theme}
        />
      </ScrollView>
    </SafeAreaView>
  )
}

export default Management

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  header: {
    padding: wp(3),
    marginHorizontal: wp(4),
    borderRadius: wp(2),
    marginTop: hp(2),
  }
});