import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  Alert,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { create_cutomer_signUP } from '../Services/API/Endpoints/Auth/Auth';

const Email_Verification = ({ route, navigation }) => {
  const { data } = route.params;
  console.log("The Data is", data)
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef([]);
  const [loading, setLoading] = useState(false);


  const handleVerifyOTP = async (retry = false) => {
    setLoading(true);
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter a 6-digit OTP.');
      return;
    }

    try {
      const cookie = await AsyncStorage.getItem('sessionCookie');
      console.log('📦 Cookie at verify time:', cookie);

      if (!cookie) {
        if (!retry) {
          console.log('🔁 Retrying due to missing cookie...');
          setTimeout(() => handleVerifyOTP(true), 300);
        } else {
          Alert.alert('Error', 'Session cookie not found.');
        }
        return;
      }

      const myHeaders = new Headers();
      myHeaders.append('Cookie', cookie);
      myHeaders.append('Content-Type', 'application/json');

      const raw = JSON.stringify({ otp: otpString });
      console.log('🟡 Sending payload:', raw);

      const response = await fetch(
        'https://dev-barber.stampasolutions.support/verify_otp/',
        {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow',
        }
      );

      const text = await response.text();
      console.log('✅ Server response:', text);

      let result;
      try {
        result = JSON.parse(text);
      } catch (err) {
        result = { message: text };
      }

      if (result?.message === 'OTP verified.') {
        console.log('🔓 OTP Verified, navigating to Login...');
       
        await create_customer();
       
        return;
      }

      if (result?.error === 'Invalid OTP or email.') {
        if (!retry) {
          console.log('⚠️ Invalid OTP on first try. Retrying silently...');
          setTimeout(() => handleVerifyOTP(true), 300);
        } else {
          setLoading(true);
          console.log('❌ Final retry failed. Showing alert.');
          Alert.alert('Invalid OTP', result.error);
        }
        return;
      }

      if (retry) {
        console.log('❌ Final retry failed with unknown response. Showing fallback alert...');
        Alert.alert('Something went wrong', result.message || result.error || 'Unknown error.');
      } else {
        console.log('⚠️ Unknown error on first try. Retrying silently...');
        setTimeout(() => handleVerifyOTP(true), 300);
      }
    } catch (error) {
      console.error('❌ OTP verification error:', error);
      if (!retry) {
        setTimeout(() => handleVerifyOTP(true), 300);
      } else {
        Alert.alert('Error', 'OTP verification failed.');
        setLoading(true);
      }
    }
  };

  const handleChange = (value, index) => {
    if (!/^\d?$/.test(value)) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e, index) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };



  const create_customer = async () => {
    try {
      const cookie = await AsyncStorage.getItem('sessionCookie');
      if (!cookie) {
        Alert.alert('Error', 'Session cookie not found for sign-up.');
        return;
      }
  
      const myHeaders = new Headers();
      myHeaders.append("content-type", "application/json");
      myHeaders.append("Cookie", cookie);
  
      const payload = {
        email: data.email,
        password: data.password,
        role: 'customer',
        full_name: data.full_name,
        phone_number: data.phone_number,
        address_city: data.address_city,
        address_postal_code: data.address_postal_code,
        address_country: data.address_country,
        address_state: data.address_state,
        address_street: data.address_street
      };
  
      const raw = JSON.stringify(payload);
      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };
  
      console.log("📦 Registering customer with payload:", payload);
  
      const response = await fetch("https://dev-barber.stampasolutions.support/customers/", requestOptions);
      const text = await response.text();
      console.log("✅ Customer registration result:", text);
  
      await AsyncStorage.removeItem('sessionCookie');
      navigation.replace('Login');
    } catch (error) {
      console.error('❌ Customer sign-up failed:', error);
      Alert.alert("Error", "Customer sign-up failed");
    }
  };




  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.container}
    >
      
      <View style={styles.innerContainer}>
        <Text style={styles.title}>Verify Your Email</Text>
        <Text style={styles.subtitle}>Enter the 6-digit code sent to:</Text>
        <Text style={styles.email}>{data.email}</Text>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              value={digit}
              onChangeText={(text) => handleChange(text, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              keyboardType="number-pad"
              maxLength={1}
              style={[
                styles.otpInput,
                digit ? styles.activeInput : null,
              ]}
            />
          ))}
        </View>

        <TouchableOpacity style={styles.button} onPress={() => handleVerifyOTP()}>
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Verify OTP</Text>
        )}
              </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={styles.backButtonText}>← Back to Login</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

export default Email_Verification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    justifyContent: 'center',
  },
  innerContainer: {
    alignItems: 'center',
    padding: 24,
    marginTop: 80,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#555',
  },
  email: {
    fontSize: 16,
    color: '#BC9B5D',
    fontWeight: '500',
    marginBottom: 28,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
    marginBottom: 30,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 10,
    textAlign: 'center',
    fontSize: 22,
    backgroundColor: '#fff',
    color: '#333',
  },
  activeInput: {
    borderColor: '#BC9B5D',
  },
  button: {
    backgroundColor: '#BC9B5D',
    paddingVertical: 14,
    paddingHorizontal: 50,
    borderRadius: 10,
    marginTop: 10,
    elevation: 3,
  },
  buttonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: 'bold',
  },
  backButton: {
    marginTop: 16,
  },
  backButtonText: {
    color: '#BC9B5D',
    fontSize: 16,
    fontWeight: '600',
  },
});
