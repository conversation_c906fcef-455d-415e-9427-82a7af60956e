import React from 'react';
import { View, ScrollView } from 'react-native';
import ResponsiveText from '../Custom/RnText';
import Fonts from '../Custom/Fonts';

const FontTest = () => {
  return (
    <ScrollView style={{ flex: 1, padding: 20,marginTop:50 }}>
      {/* Semantic Font Styles */}
      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="Main_Heading" size={7}>
          Main Heading (Poppins-Bold)
        </ResponsiveText>
        <ResponsiveText fontFamily="Sub_Heading" size={6}>
          Sub Heading (Gilroy-Regular)
        </ResponsiveText>
        <ResponsiveText fontFamily="Button" size={5}>
          Button Text (Gilroy-Bold)
        </ResponsiveText>
        <ResponsiveText fontFamily="Accordion" size={5}>
          Accordion Text (Gilroy-Medium)
        </ResponsiveText>
        <ResponsiveText fontFamily="Muted_Text" size={4}>
          Muted Text (Sen-Regular)
        </ResponsiveText>
        <ResponsiveText fontFamily="Form_Label" size={4}>
          Form Label (PlusJakartaSans-Regular)
        </ResponsiveText>
        <ResponsiveText fontFamily="Body_Text" size={4}>
          Body Text (Poppins-Regular)
        </ResponsiveText>
      </View>

      {/* Individual Font Families */}
      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="PoppinsRegular" size={5}>
          Poppins Regular
        </ResponsiveText>
        <ResponsiveText fontFamily="PoppinsBold" size={5}>
          Poppins Bold
        </ResponsiveText>
        <ResponsiveText fontFamily="PoppinsMedium" size={5}>
          Poppins Medium
        </ResponsiveText>
      </View>

      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="SenRegular" size={5}>
          Sen Regular
        </ResponsiveText>
        <ResponsiveText fontFamily="SenBold" size={5}>
          Sen Bold
        </ResponsiveText>
        <ResponsiveText fontFamily="SenMedium" size={5}>
          Sen Medium
        </ResponsiveText>
      </View>

      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="GilroyRegular" size={5}>
          Gilroy Regular
        </ResponsiveText>
        <ResponsiveText fontFamily="GilroyBold" size={5}>
          Gilroy Bold
        </ResponsiveText>
        <ResponsiveText fontFamily="GilroyMedium" size={5}>
          Gilroy Medium
        </ResponsiveText>
      </View>

      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="PlusJakartaSansRegular" size={5}>
          Plus Jakarta Sans Regular
        </ResponsiveText>
        <ResponsiveText fontFamily="PlusJakartaSansBold" size={5}>
          Plus Jakarta Sans Bold
        </ResponsiveText>
      </View>

      <View style={{ marginBottom: 20 }}>
        <ResponsiveText fontFamily="PlayfairDisplay" size={5}>
          Playfair Display
        </ResponsiveText>
      </View>
    </ScrollView>
  );
};

export default FontTest; 