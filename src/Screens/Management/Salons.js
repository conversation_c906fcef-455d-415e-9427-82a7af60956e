import React, { useCallback, useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  SafeAreaView, 
  TextInput, 
  TouchableOpacity, 
  ImageBackground,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl
} from 'react-native';
import CustomHeader from '../../Components/CustomHeader';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import { salonData } from '../../Custom/mockData';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { get_all_shops } from '../../Services/API/Endpoints/Admin/Salons';
import Loader from '../../Custom/loader';

const Salons = () => {
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);
    const [shops, setShops] = useState([]);
    const [filteredShops, setFilteredShops] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const AppText = useAppText();
    const { 
      themeColor,
      getDarK_mode_LightGrayBackground, 
      backgroundColor, 
      getTextColor,
      getSecondaryTextColor,
      getLightGrayBackground,
      getborderTextColor,
      getBellBackground,
      getDark_Theme
    } = useTheme();
    const navigation = useNavigation();

    const handleSalonPress = (salon) => {   
      navigation.navigate('Add Salons Details', { salon });
      setSearchQuery('');  
    };
    const handleAddSalonPress = () => {
      navigation.navigate('Add Salons');
      setSearchQuery('');  
    };  

    const renderSalonItem = ({ item }) => (
      <TouchableOpacity activeOpacity={0.9} onPress={() => handleSalonPress(item)}>
        <ImageBackground 
          source={item.shop_image
                      ? { uri: item.shop_image } // Remote image (from API)
                      : globalpath.logo }
          style={styles.salonCard}
          imageStyle={styles.salonCardImage}
        >
          <View style={styles.salonCardOverlay} />
          <View style={styles.salonCardContent}>
            <ResponsiveText weight={'bold'} size={6} color={colors.white} numberOfLines={3} margin={[hp(2),0,0,0]}>
              {item.name}
            </ResponsiveText>

            <View style={{height:hp(7)}}/>
            
            <View style={styles.salonInfoRow}>
              <Icon 
                source={globalpath.location}
                size={wp(4)} 
                tintColor={colors.white}
                margin={[0, wp(2), 0, 0]}
              />
              <ResponsiveText color={colors.white} weight={'700'}  size={3.6} numberOfLines={1} maxWidth={"90%"}  >
                {item.address_city}
              </ResponsiveText>
            </View>
            
            <View style={styles.salonInfoRow}>
              <Icon 
                source={globalpath.watch}
                size={wp(4)} 
                tintColor={colors.white}
                margin={[0, wp(2), 0, 0]}
              />
             <ResponsiveText
                color={colors.white}
                weight={'700'}
                size={3.6}
                margin={[0, wp(8), 0, 0]}
                numberOfLines={1}
                maxWidth={"57%"}
              >
                {`${AppText.OPEN_TODAY} : ${
                  Array.isArray(item?.working_hours) && item.working_hours.length > 0
                    ? item.working_hours[0].open_time
                    : ''
                }`}
              </ResponsiveText>
            </View>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );

    const handleSearch = (text) => {
        setSearchQuery(text);
        if (text) {
            const filtered = shops.filter(shop =>
                shop.name.toLowerCase().includes(text.toLowerCase())
            );
            setFilteredShops(filtered);
        } else {
            setFilteredShops(shops);
        }
    };

    useEffect(() => {
        setFilteredShops(shops);
    }, [shops]);

    const getAllshops = async () => {
        try {
            setLoading(true);
            setRefreshing(true);
    
            const response = await get_all_shops();
            console.log('🟢 Shops fetched:', response);
    
            if (Array.isArray(response)) {
                const sortedServices = response.sort((a, b) => b.id - a.id);
                setShops(sortedServices);
            }
        } catch (error) {
            console.error('❌ Failed to fetch services:', error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };
  
    const onRefresh = useCallback(() => {
      getAllshops();
      setSearchQuery('');
    }, []);
  
    useFocusEffect(
      useCallback(() => {
        getAllshops();
      }, []),
    );

    return (
        <SafeAreaView style={[styles.container, {backgroundColor: backgroundColor}]}>
            <CustomHeader title={AppText.SALONS} leftIconType="back" />   
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : ''}
                style={{flex: 1}}>  
                <View style={styles.content}>
                    <View style={[styles.searchRow]}>
                        <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
                            <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
                            <TextInput
                                style={[styles.searchInput, {color: getTextColor()}]}
                                placeholder={AppText.SEARCH_SALONS}
                                placeholderTextColor={colors.grey}
                                value={searchQuery}
                                onChangeText={handleSearch}
                            />
                        </View>
                        <TouchableOpacity 
                            style={[styles.addButton, {backgroundColor: colors.Light_theme_maincolour}]}
                            onPress={handleAddSalonPress}
                        >
                            <ResponsiveText color={colors.white}>{AppText.ADD_SALONS}</ResponsiveText>
                        </TouchableOpacity>
                    </View>
                    
                    <FlatList
                        data={filteredShops}
                        renderItem={renderSalonItem}
                        keyExtractor={item => item.id.toString()}
                        contentContainerStyle={styles.salonList}
                        showsVerticalScrollIndicator={false}
                        ListEmptyComponent={() => (
                            <View style={styles.noResults}>
                                <ResponsiveText color={getTextColor()} size={4}>
                                    {AppText.NO_RESULTS_FOUND}
                                </ResponsiveText>
                            </View>
                        )}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={onRefresh}
                                colors={[colors.Light_theme_maincolour]}
                                tintColor={colors.Light_theme_maincolour}
                            />
                        }
                    />
                </View>
            </KeyboardAvoidingView>
            {loading && <Loader />}
        </SafeAreaView>
    );
};

export default Salons;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  salonList: {
    paddingHorizontal: wp(3),
    // paddingBottom: wp(5),
    marginTop:hp(2)
  },
  salonCard: {
    // height: hp(25),
    width: '100%',
    borderRadius: wp(2),
    overflow: 'hidden',
    marginBottom: hp(2),
  },
  salonCardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  salonCardOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  salonCardContent: {
    padding: wp(4),
    position: 'relative',
  },

  salonInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: wp(2),
  },
  bookNowButton: {
    backgroundColor: colors.Light_theme_maincolour,
    paddingVertical: wp(2),
    paddingHorizontal: wp(3),
    borderRadius: wp(1),
    alignSelf: 'flex-start',
    // marginTop: wp(3),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
    height: hp(55)
  },
});