import React, { useState , useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform , ActivityIndicator , Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import CustomHeader from '../../../Components/CustomHeader';
import CustomTextInput from '../../../Components/CustomTextInput';
import Product_Upload_Image from '../../../Components/Products/Product_Upload_Image';
import ProductVariants from '../../../Components/Products/ProductVariants';
import { add_product , update_product, delete_productVariant } from '../../../Services/API/Endpoints/Admin/products';
import { useNavigation } from '@react-navigation/native';
import DropDownPicker from 'react-native-dropdown-picker';

const Edit_Product = ({route}) => {
   
  const { product, isEdit , from } = route.params || {};

  console.log("The DATA IS ", product)

 
   const navigation = useNavigation();
 


   const [selectedImage, setSelectedImage] = useState(null);
   const [loading, setLoading] = useState(false);


  console.log("The Products is ", from)
  const { backgroundColor, getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const [formData, setFormData] = useState({
    productName: '',
    description: '',
    price: '',
    stock: '',
  });
  const [selectedVariants, setSelectedVariants] = useState([]);
  const [errors, setErrors] = useState({});
  const [variantDropdownOpen, setVariantDropdownOpen] = useState(false);
  const [variantDropdownItems, setVariantDropdownItems] = useState([]);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [variantImagesMap, setVariantImagesMap] = useState({});





  useEffect(() => {
    const dropdownItems = selectedVariants.map(variant => ({
      label: `Variant - ${variant.value} ${variant.name}  |  - Price ${variant.price_override}`,
      value: variant,
    }));
    setVariantDropdownItems(dropdownItems);
  }, [selectedVariants]);


  const handleVariantSelectDropdown = (variant) => {
    console.log
    setSelectedVariant(variant);
    setFormData(prev => ({
      ...prev,
      price: variant.price_override,
      // stock: variant.stock.toString(),
    }));
  };

  const handleVariantImagesChange = (imageDetails) => {
    console.log("EDIT imageDetails---", imageDetails);
    setVariantImagesMap(imageDetails)
    

  };



  useEffect(() => {
    if (isEdit && product) {
    
    const totalStock = product.variants?.reduce((sum, v) => {
        const val = parseInt(v.stock, 10);
        return sum + (isNaN(val) ? 0 : val);
        }, 0);



      setFormData({
        productName: product?.name || '',
        description: product?.description || '',
        price: product?.price?.toString() || '',
        stock: totalStock.toString(),
      });
  
      setSelectedVariants(product.variants || []);
  
      // ✅ Auto-select variant in dropdown based on price match
      const match = product.variants.find(
        v => v.price_override === product.price
      );
      if (match) {
        setSelectedVariant(match);
      }
  
      if (from === 'products') {
        
        const formattedImages = product.product_images?.map(img => ({
          uri: img.image,
          image: img.image,
          fileName: img.image.split('/').pop(),
          id:img.id
        })) ;
        setSelectedImage(formattedImages);
      } 
  
      // ✅ Initialize variant images
      const imageMap = {};
      product.variants.forEach((variant, index) => {
        if (Array.isArray(variant.variant_images) && variant.variant_images.length > 0) {
          const formattedImages = variant.variant_images.map(img => ({
            uri: img.image,
            image: undefined,
            fileName: img.image?.split('/').pop() || `variant_${index}.jpg`,
            id: img.id
          }));
          imageMap[`variant_${index}`] = formattedImages;
        }
      });
  
      setVariantImagesMap(imageMap);
      console.log("✅ Variant Image Map Created:", imageMap);
    }
  }, [isEdit, product]);
  




  const isFormValid = () => {
    return (
      formData.productName.trim() !== '' &&
      formData.description.trim() !== '' &&
      formData.price.trim() !== '' &&
      formData.stock.trim() !== '' &&
      selectedVariants.length > 0
    );
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };


  const handleImageSelect = (imageDetails) => {
    setSelectedImage(imageDetails);
  
    if (imageDetails) {
      if (from === 'products' && Array.isArray(imageDetails)) {
        console.log('🖼️ Selected Product Images:', imageDetails.map((img, index) => ({
          index: index + 1,
          name: img.fileName,
          size: img.size + ' MB',
          dimensions: img.dimensions,
          uri: img.uri,
        })));
      } else {
        console.log('🖼️ Selected Service Image Details:', {
          name: imageDetails.fileName,
          size: imageDetails.size + ' MB',
          dimensions: imageDetails.dimensions,
          uri: imageDetails.uri,
        });
      }
    } else {
      console.log(from === 'products' ? '🧹 Product images cleared' : '🧹 Service image removed');
    }
  };

  // const handleVariantSelect = (variantId) => {
  //   setSelectedVariants(prev => {
  //     if (prev.includes(variantId)) {
  //       return prev.filter(id => id !== variantId);
  //     } else {
  //       return [...prev, variantId];
  //     }
  //   });
  // };

  const handleVariantSelect = (updater) => {
    setSelectedVariants(prev => {
      const updated = typeof updater === 'function' ? updater(prev) : updater;
  
      const totalStock = updated.reduce((sum, variant) => {
        const stockVal = parseInt(variant.stock, 10);
        return sum + (isNaN(stockVal) ? 0 : stockVal);
      }, 0);
  
      setFormData(prevForm => ({
        ...prevForm,
        stock: totalStock.toString(),
      }));
  
      return updated;
    });
  };
  
  
  

  const validateForm = () => {
    const newErrors = {};
    if (!formData.productName.trim()) {
      newErrors.productName = 'Product name is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    }
    if (!formData.stock.trim()) {
      newErrors.stock = 'Stock is required';
    }
    if (selectedVariants.length === 0) {
      newErrors.variants = 'At least one variant is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
  
    console.log('📝 Submitting Product:', {
      ...formData,
      variants: selectedVariants,
    });
  
    setLoading(true);
  
    try {
      const form = new FormData();

      //  🔤 Slugify logic
      //  🔤 Slugify logic
    const slugify = (text) => {
      return text
        .normalize('NFD') // Normalize Unicode (important for accents like é, ñ, etc.)
        .replace(/[\u0300-\u036f]/g, '') // Remove accent marks
        .replace(/[^a-zA-Z0-9\s-]/g, '') // Remove special characters except letters, numbers, spaces, and hyphens
        .trim()
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen
    };
    // const randomCode = Math.floor(100000 + Math.random() * 900000); // 6-digit random number
    // const slug = `${slugify(formData.productName)}-${randomCode}`;

    const slug = slugify(formData.productName);
    console.log("Slug is:", slug);

   
  
      form.append('name', formData.productName);
      form.append('description', formData.description);
      form.append('category',product.category);
      form.append('price', formData.price);
      form.append('slug', slug);
      form.append('stock', formData.stock);
      form.append('is_active', 'true');
  
            // ✅ Add variants as JSON string
        if (Array.isArray(selectedVariants) && selectedVariants.length > 0) {
          form.append('variants', JSON.stringify(selectedVariants));
        } else {
          console.warn('⚠️ No variants selected to append.');
        }


            // ✅ Append variant images to FormData (only local ones)
            if (variantImagesMap && typeof variantImagesMap === 'object') {
              Object.entries(variantImagesMap).forEach(([key, images]) => {
                if (Array.isArray(images) && images.length > 0) {
                  images.forEach((img, index) => {
                    if (img?.uri) {
                      if (!img.uri.startsWith('http')) {
                        form.append(`${key}`, {
                          uri: img.uri,
                          name: img.fileName || img.name || `${key}-${index}.jpg`,
                          type: img.type || 'image/jpeg',
                        });
                        console.log(`✅ Appended Local Image for ${key}:`, img.uri);
                      } else {
                        console.log(`⛔ Skipped Remote Image for ${key}:`, img.uri);
                      }
                    } else {
                      console.warn(`⚠️ Skipped image in ${key} due to missing URI`, img);
                    }
                  });
                } else {
                  console.warn(`⚠️ No images for ${key} or not an array`);
                }
              });
            } else {
              console.warn('⚠️ variantImagesMap is empty or not an object');
            }



        // ✅ Add image(s)
        if (from === 'products') {
          if (Array.isArray(selectedImage) && selectedImage.length > 0) {
            selectedImage.forEach((img, index) => {
              if (img?.uri) {
                form.append('product_images', {
                  uri: img.uri,
                  name: img.fileName || `product-${index}.jpg`,
                  type: img.type || 'image/jpeg',
                });
              } else {
                console.warn('⚠️ Skipped product image due to missing URI', img);
              }
            });
          } else {
            console.warn('⚠️ No product images selected');
          }
        } else if (selectedImage?.uri) {
          form.append('product_images', {
            uri: selectedImage.uri,
            name: selectedImage.fileName || 'image.jpg',
            type: selectedImage.type || 'image/jpeg',
          });
        } else {
          console.warn('⚠️ No single service image selected');
        }
  
      console.log('📤 Sending FormData...', form);



      const response = await update_product(product.id , form);
  
      console.log('✅ Update API Response:', response);
  
      Alert.alert('Success', 'Product Updated successfully!');

      setSelectedImage(null);
      setFormData({
        productName: '',
        description: '',
        price: '',
        stock: '',
      });
      setSelectedVariants([]);
      setVariantImagesMap({});
      navigation.pop(2); // 👈 Instantly go back 2 screens
    } catch (error) {
      console.error('❌ Error adding product:', error);
    
      if (error?.response?.data) {
        console.error('Server Response:', error.response.data);
    
        const serverErrors = error.response.data;
    
        // 🔁 Convert backend errors to local state format
        const parsedErrors = {};
        Object.entries(serverErrors).forEach(([field, messages]) => {
          parsedErrors[field] = Array.isArray(messages) ? messages.join('\n') : messages;
        });
    
        setErrors(parsedErrors); // 👈 Show errors on UI if mapped to `errors.<field>`
    
        // Optionally alert all error messages
        const errorList = Object.values(parsedErrors).join('\n');
        Alert.alert('Validation Error', errorList);
      } else {
        Alert.alert('Error', 'Failed to add product. Please try again.');
      }
    }finally {
      setLoading(false);
    }
  };


  const Updateproduct = async () => {
    if (!validateForm()) return;
  
    console.log('📝 Submitting Product:', {
      ...formData,
      variants: selectedVariants,
    });
  
    setLoading(true);
  
    try {
      const form = new FormData();

       // 🔤 Slugify logic
    const slugify = (text) => {
      return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/[\s\W-]+/g, '-') // replace spaces & special chars with -
        .replace(/^-+|-+$/g, '');  // remove leading/trailing -
    };
    const randomCode = Math.floor(100000 + Math.random() * 900000); // 6-digit random number
    const slug = `${slugify(formData.productName)}-${randomCode}`;

    console.log("The slug is ", slug)
  
      form.append('name', formData.productName);
      form.append('description', formData.description);
      form.append('category', product.category);
      form.append('price', formData.price);
      form.append('slug', slug);
      form.append('stock', formData.stock);
      form.append('is_active', 'true');
  
      // ✅ Add variants as JSON string
      form.append('variants', JSON.stringify(selectedVariants));
  
      // ✅ Add image(s)
      if (from === 'products' && Array.isArray(selectedImage)) {
        selectedImage.forEach((img, index) => {
          form.append('product_images', {
            uri: img.uri,
            name: img.fileName || `product-${index}.jpg`,
            type: img.type || 'image/jpeg',
          });
        });
      } else if (selectedImage?.uri) {
        form.append('product_images', {
          uri: selectedImage.uri,
          name: selectedImage.fileName || 'image.jpg',
          type: selectedImage.type || 'image/jpeg',
        });
      }
  
      console.log('📤 Sending FormData...');
      const response = await update_product(product.id , form);
  
      console.log('✅ Update API Response:', response);
  
      Alert.alert('Success', 'Product Update successfully!', [
        {
          text: 'OK',
          onPress: () => {
            // Reset logic
            setSelectedImage(null);
            setFormData({
              productName: '',
              description: '',
              price: '',
              stock: '',
            });
            setSelectedVariants([]);
            navigation.goBack();
            // optionally navigate back or reset screen
          },
        },
      ]);
    } catch (error) {
      console.error('❌ Error adding product:', error);
      if (error?.response?.data) {
        console.error('Server Response:', error.response.data);
      }
      Alert.alert('Error', 'Failed to add product. Please try again.');
    } finally {
      setLoading(false);
    }
    
  }

  const handleMatchedVariant = async (matchedVariant) => {
    console.log("📦 Received matchedVariant in Edit_Product:", matchedVariant.id);
    try {
      console.log(`📤 Deleting image from API (ID: ${matchedVariant.id})...`);
      await delete_productVariant(matchedVariant.id);
      // console.log(`🗑️ Successfully deleted image ID: ${img.id}`);
    } catch (error) {
      console.error(`❌ Failed to delete image ID: ${img.id}`, error);
      Alert.alert('Error', `Failed to delete image from server. Image ID: ${img.id}`);
    }
  };
  

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.EDIT_PRODUCT} leftIconType="back" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={styles.container}
      >


        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          <CustomTextInput
            label={AppText.PRODUCT_NAME}
            placeholder={AppText.ENTER_PRODUCT_NAME}
            value={formData.productName}
            onChangeText={(value) => handleChange('productName', value)}
            error={errors.productName}
          />

          <View style={styles.descriptionContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, hp(1), 0]}
            >
              {AppText.PRODUCT_DESCRIPTION}
            </ResponsiveText>
            <TextInput
              style={[
                styles.descriptionInput,
                {
                  color: getTextColor(),
                  borderColor: errors.description ? colors.red : getDark_Theme(),
                  backgroundColor: backgroundColor,
                },
              ]}
              placeholder={AppText.ENTER_PRODUCT_DESCRIPTION}
              placeholderTextColor={colors.grey}
              value={formData.description}
              onChangeText={(value) => handleChange('description', value)}
              multiline
              numberOfLines={4}
            />
            {errors.description && (
              <ResponsiveText color={colors.red} size={3} margin={[hp(0.5), 0, 0, 0]}>
                {errors.description}
              </ResponsiveText>
            )}
          </View>

          <ProductVariants
                selectedVariants={selectedVariants}
                onVariantSelect={handleVariantSelect}
                onVariantImagesChange={handleVariantImagesChange}
                initialVariantImagesMap={variantImagesMap} // ✅ Add this prop
                isEdit={isEdit}
                onMatchedVariant={handleMatchedVariant} // ✅ new prop
                />
          <View style={{marginBottom:hp(2)}}> </View>


          <DropDownPicker
            open={variantDropdownOpen}
            value={selectedVariant}
            items={variantDropdownItems}
            setOpen={setVariantDropdownOpen}
            setValue={(cb) => handleVariantSelectDropdown(cb(null))}
            setItems={setVariantDropdownItems}
            placeholder={AppText.SELECT_VARIANT_PRIORITY}
            style={{ marginBottom: hp(2), zIndex: 9999 }}
          />

          <CustomTextInput
            label={AppText.PRODUCT_PRICE}
            placeholder={AppText.CHOOSE_PRODUCT_VARIANT_PRICE}
            value={formData.price}
            onChangeText={(value) => handleChange('price', value)}
            keyboardType="numeric"
            error={errors.price}
            editable={false}
          />

            <CustomTextInput
            label={AppText.TOTAL_STOCK}
            placeholder={AppText.CHOOSE_PRODUCT_VARIANT_STOCK}
            value={formData.stock}
            onChangeText={(value) => handleChange('stock', value)}
            keyboardType="numeric"
            error={errors.stock}
            // editable={false} // prevent manual edits
            />

         

          <Product_Upload_Image from={from} onImageSelect={handleImageSelect} initialImage={selectedImage}
 />

          <TouchableOpacity
              style={[
                styles.addButton,
                {
                  backgroundColor: isFormValid() ? colors.Light_theme_maincolour : colors.grey,
                  opacity: isFormValid() ? 1 : 0.5,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
              ]}
              disabled={!isFormValid() || loading}
              onPress={handleSubmit}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                   {isEdit ?  'Update' : AppText.ADD_PRODUCT}
                </ResponsiveText>
              )}
            </TouchableOpacity>

        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Edit_Product;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: hp(2),
  },
  descriptionContainer: {
    marginBottom: hp(2),
  },
  descriptionInput: {
    height: hp(12),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    paddingTop: wp(4),
    fontSize: wp(3.8),
    textAlignVertical: 'top',
  },
  addButton: {
    marginTop: hp(2),
    marginBottom: hp(4),
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
});