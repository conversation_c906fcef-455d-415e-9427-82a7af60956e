import React, { useState , useCallback , useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, FlatList , ActivityIndicator , RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomHeader from '../../../Components/CustomHeader';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import ProductCard from '../../../Components/Products/ProductCard';
import { Product_Details_mock } from '../../../Mocks/Product_mock';
import { useNavigation , useFocusEffect } from '@react-navigation/native';

import Add_Product_Modal from '../../../Components/Products/Add_Product_Modal';
import { list_products_by_category } from '../../../Services/API/Endpoints/Admin/products';


const Products_Details = ({ route }) => {
  console.log("route.params", route.params)
  const { product_category } = route.params;
  console.log("product category details is ", product_category)
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filteredServices, setFilteredServices] = useState([]);
  const [productdata, setProductData] = useState([]);


   const onRefresh = useCallback(() => {
      fetchProducts();
        handleSearch('');
      }, []);
  
  
     useFocusEffect(
        useCallback(() => {
          fetchProducts();
        }, []),
      );
  
       const fetchProducts = async () => {
          try {
            setLoading(true);
            setRefreshing(true);
      
            const response = await list_products_by_category(product_category.id);
            console.log('🟢 Products Categories fetched:', response);
      
            if (Array.isArray(response)) {
              const sortedServices = response.sort((a, b) => b.id - a.id);
              setProductData(sortedServices);
              setFilteredServices(sortedServices);
            }
          } catch (error) {
            console.error('❌ Failed to fetch services:', error);
          } finally {
            setLoading(false);
            setRefreshing(false);
          }
        };
  

        const handleSearch = (text) => {
          setSearchText(text);
          if (text.trim()) {
            const filtered = productdata.filter((product) =>
              product.name.toLowerCase().includes(text.toLowerCase())
            );
            setFilteredServices(filtered);
          } else {
            setFilteredServices(productdata);
          }
        };





  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor } = useTheme();

  const handleProductPress = (product) => {
    // Handle product press - navigate to product details or show modal
    console.log('Product pressed:', product);
  };

  const renderProductItem = ({ item }) => {
    console.log('🛍️ Rendering Product Item:', item);
  
    const productPrice = item.price;
    let imagesToShow = [];
  
    // 1️⃣ Check if any variant matches the price exactly
    const exactMatchVariant = item.variants.find(variant => {
      if (variant.price_override === productPrice) {
        console.log(`✅ Price Matched: productPrice = ${productPrice}, variant.price_override = ${variant.price_override}`);
        return true;
      } else {
        console.log(`❌ Price Not Matched: productPrice = ${productPrice}, variant.price_override = ${variant.price_override}`);
        return false;
      }
    });
  
    if (exactMatchVariant?.variant_images?.length > 0) {
      imagesToShow = exactMatchVariant.variant_images;
      console.log('✅ Exact price match found. Showing variant_images:', imagesToShow.map(img => img.image_url || img.uri));
    } 
    // 2️⃣ Check if any variant has images
    else if (
      item.variants.some(variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0)
    ) {
      const firstWithImages = item.variants.find(
        variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0
      );
      imagesToShow = firstWithImages.variant_images;
      console.log('📦 No exact price match, but found variant with images.');
      console.log('🖼️ Variant Images Used:', imagesToShow.map(img => img.image_url || img.uri));
    } 
    // 3️⃣ Fallback to product_images
    else {
      imagesToShow = item.product_images;
      console.log('⚠️ No variant images found. Using product_images.');
      console.log('🖼️ Product Images Used:', imagesToShow.map(img => img.image_url || img.uri));
    }
  
    return (
      <View style={styles.productCardContainer}>
        <ProductCard
          product={item}
          images={imagesToShow} // ✅ Passing the resolved image array
          onPress={() => handleProductPress(item)}
        />
      </View>
    );
  };
  
  
  
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={product_category.name} showBellIcon={false} leftIconType="back" />
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : ''} style={styles.container}>
        <View style={styles.content}>
          <View style={[styles.searchRow]}>
            <View style={[styles.searchContainer, { borderColor: getDark_Theme() }]}>
              <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0, wp(2), 0, 0]} />
              <TextInput
                style={[styles.searchInput, { color: getTextColor() }]}
                placeholder={AppText.SEARCH_PRODUCTS}
                placeholderTextColor={colors.grey}
                value={searchText}
                onChangeText={handleSearch}
              />
            </View>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
              onPress={() => navigation.navigate('Add_Product', { product_category: product_category , from:'products' })}
            >
              <ResponsiveText color={colors.white}>{AppText.ADD_PRODUCT}</ResponsiveText>
            </TouchableOpacity>
            {/* <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
              onPress={() => navigation.navigate('Product Filters')}
            >
              <Icon source={globalpath.filter} size={wp(4.2)} tintColor={colors.white} />
            </TouchableOpacity> */}
          </View>

          {loading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
            </View>
          ) : filteredServices.length === 0 ? (
            <View style={styles.noDataContainer}>
              <ResponsiveText size={4} color={getTextColor()}>
                {AppText.NO_RECORD_FOUND || 'No Records Found'}
              </ResponsiveText>
            </View>
          ) : (
            <FlatList
              data={filteredServices}
              renderItem={renderProductItem}
              keyExtractor={item => item.id.toString()}
              numColumns={2}
              contentContainerStyle={styles.productsGrid}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={fetchProducts} />
              }
            />
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Products_Details;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5),
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
  },
  filterButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
    width: wp(12),
    height: wp(12),
  },
  productsContainer: {
    flex: 1,
  },
  productsGrid: {
    padding: wp(2),
  },
  productCardContainer: {
    width: '50%',
    padding: wp(1),
  },
  loaderContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noDataContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: hp(10),
  },

});