import React, { useState , useCallback , useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity  , Alert} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomHeader from '../../../Components/CustomHeader';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import ProductImageCarousel from '../../../Components/Products/ProductImageCarousel';
import ProductSizeDropdown from '../../../Components/Products/ProductSizeDropdown';
import ProductDescription from '../../../Components/Products/ProductDescription';
import { list_product , delete_product } from '../../../Services/API/Endpoints/Admin/products';
import { useNavigation , useFocusEffect } from '@react-navigation/native';

const View_Product_Details = ({ route }) => {
   const navigation = useNavigation();
  const { product } = route.params;
  
   const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);
    const [searchText, setSearchText] = useState('');
    const [filteredServices, setFilteredServices] = useState([]);
    const [productdata, setProductData] = useState([]);

  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor,getDarK_mode_LightGrayBackground,getBellBackground } = useTheme();
  const [selectedSize, setSelectedSize] = useState(product.size);

  console.log("Product is ", product)
  const productPrice = product.price;
  let imagesToShow = [];

  // 1️⃣ Collect exact price matched variant images
  const exactPriceVariantImages = product.variants
    .filter(variant => variant.price_override === productPrice && Array.isArray(variant.variant_images))
    .flatMap(variant => variant.variant_images);

  // 2️⃣ Collect all other variant images
  const allVariantImages = product.variants
    .filter(variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0)
    .flatMap(variant => variant.variant_images);

  // 3️⃣ Always append product images at the end
  const productImages = Array.isArray(product.product_images) ? product.product_images : [];

  // 🔥 Combine ALL images into imagesToShow
  imagesToShow = [...exactPriceVariantImages, ...allVariantImages, ...productImages];

  console.log('✅ Final imagesToShow:', imagesToShow);

  
  // Picked based on the Variant Priority
    // let imagesToShow = [];
  
    // // 1️⃣ Check if any variant matches the price exactly
    // const exactMatchVariant = product.variants.find(
    //   variant => variant.price_override === productPrice
    // );
  
    // if (exactMatchVariant?.variant_images?.length > 0) {
    //   imagesToShow = exactMatchVariant.variant_images;
    //   console.log('✅ Exact price match found. Showing variant_images:', imagesToShow);
    // } 
    // // 2️⃣ Check if any variant has images
    // else if (
    //   product.variants.some(variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0)
    // ) {
    //   const firstWithImages = product.variants.find(
    //     variant => Array.isArray(variant.variant_images) && variant.variant_images.length > 0
    //   );
    //   imagesToShow = firstWithImages.variant_images;
    //   console.log('📦 No price match. Showing first available variant_images:', imagesToShow);
    // } 
    // // 3️⃣ Fallback to product_images
    // else {
    //   imagesToShow = product.product_images;
    //   console.log('🖼️ No variant images. Showing product_images:', imagesToShow);
    // }


  const handleEdit = () => {
    navigation.navigate('Edit_Product', { product : productdata  , isEdit : true , from:'products' });
  };

  const handleDelete = () => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this product?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log("Product id is ", productdata.id);
              await delete_product(productdata.id);
              console.log(`🧼 Deleted product ID: ${productdata.id} from server`);
              navigation.goBack();
            } catch (error) {
              console.error('❌ Failed to delete product from API:', error);
              Alert.alert('Error', 'Failed to delete product from server.');
            }
          },
        },
      ],
      { cancelable: true }
    );
  };


       useFocusEffect(
          useCallback(() => {
            fetchProducts();
          }, []),
        );
    
         const fetchProducts = async () => {
            try {
              setLoading(true);
              setRefreshing(true);
        
              const response = await list_product(product.id);
              console.log('🟢 Products  fetched:', response);
              setProductData(response);
            } catch (error) {
              console.error('❌ Failed to fetch services:', error);
            } finally {
              setLoading(false);
              setRefreshing(false);
            }
          };
    


  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.PRODUCTS_DETAILS} showBellIcon={false} leftIconType="back" />
      <ProductImageCarousel images={imagesToShow} />
      <ScrollView style={styles.content}>
        
        <View style={styles.detailsContainer}>
          <View style={styles.titleRow}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="bold"
              flex={1}
            >
              {productdata?.name || ''}
            </ResponsiveText>
            <TouchableOpacity style={[styles.editButton,{backgroundColor:getBellBackground()}]} onPress={handleDelete}>
              <Icon source={globalpath.delete_icon} size={wp(5)} tintColor={getTextColor()} />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.editButton,{backgroundColor:getBellBackground()}]} onPress={handleEdit}>
              <Icon source={globalpath.edit} size={wp(5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
                    <ResponsiveText
                      color={getTextColor()}
                      size={3.6}
                      weight="bold"
                    >
                     {AppText.VARINAT_SIZES}
                    </ResponsiveText>
                    <ResponsiveText
                      color={colors.Light_theme_maincolour}
                      size={3.6}
                      margin={hp(1)}
                      weight="bold"
                    >
                      {(() => {
                        const match = productdata?.variants?.find(
                          v =>
                            v.price_override === productdata?.price 
                        );
                        return match ? `${match.value} ${match.name}` : 'Size not matched';
                      })()}
                    </ResponsiveText>
                  </View>

          <View style={styles.stockPriceRow}>
            <ResponsiveText
              color={getTextColor()}
              size={3.4}
              weight="700"
            >
              {AppText.PRICE}
            </ResponsiveText>
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3.6}
              weight="bold"
            >
             ${productdata?.price}
            </ResponsiveText>
          </View>

          <View style={[styles.divider, { borderBottomColor: getDark_Theme() }]} />

          <ProductDescription description={productdata?.description || ''} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default View_Product_Details;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  detailsContainer: {
    padding: wp(4),
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  editButton: {
    padding: wp(2.5),
    marginRight:wp(1),
    borderRadius:wp(6)
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  stockPriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  divider: {
    borderBottomWidth: 1,
    marginBottom: hp(2),
  },
});