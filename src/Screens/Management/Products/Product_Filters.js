import { StyleSheet, View, TouchableOpacity, ScrollView } from 'react-native';
import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { wp, hp } from '../../../Custom/Responsiveness';
import useTheme from '../../../Redux/useTheme';
import CustomCheckbox from '../../../Custom/CustomCheckbox';
import useAppText from '../../../Custom/AppText';
const FILTER_OPTIONS = [
  { id: 1, name: 'Pre-Cleaning' },
  { id: 2, name: 'Hair Cleaning' },
  { id: 3, name: 'Hair Treatment' },
  { id: 4, name: 'Hair Style' },
  { id: 5, name: 'Lets take care of body' },
  { id: 6, name: 'Face Essentials' },
];

const Product_Filters = ({ navigation }) => {
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor,getDarK_mode_LightGrayBackground ,getBellBackground} = useTheme();
  const [selectedFilters, setSelectedFilters] = useState({});

  const handleFilterToggle = (id) => {
    setSelectedFilters(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const hasSelectedFilters = Object.values(selectedFilters).some(value => value);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: backgroundColor }]}>
        <TouchableOpacity 
          style={styles.closeButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon source={globalpath.cross} size={wp(5.5)} tintColor={getTextColor()} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
        <ResponsiveText weight="bold"  color={getTextColor()} size={4.5} textAlign="center">
          {AppText.FILTERS}
        </ResponsiveText>
        </View>
      </View>

      {/* Filter Options */}
      <ScrollView style={[styles.content, { backgroundColor: getBellBackground()}]} showsVerticalScrollIndicator={false}>
        <ResponsiveText color={getTextColor()} size={4.5} weight={'bold'} margin={[hp(2), 0, wp(3), wp(1)]} >{AppText.CATEGORIES}</ResponsiveText>
        {FILTER_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={styles.filterOption}
            onPress={() => handleFilterToggle(option.id)}
          >
            <CustomCheckbox
              value={selectedFilters[option.id] || false}
              onValueChange={() => handleFilterToggle(option.id)}
              onTintColor={colors.Light_theme_maincolour}
              onCheckColor={colors.white}
            />
            <ResponsiveText 
              style={styles.filterText} 
              color={getTextColor()}
              size={4}
              weight={'600'}
            >
              {option.name}
            </ResponsiveText>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Apply Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.applyButton,
            {
              backgroundColor: hasSelectedFilters 
                ? colors.Light_theme_maincolour 
                : colors.lightGrey5
            }
          ]}
          disabled={!hasSelectedFilters}
          onPress={() => {
            // Handle apply filters
            navigation.goBack();
          }}
        >
          <ResponsiveText color={colors.white} weight="bold">
            {AppText.APPLY_FILTERS}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default Product_Filters;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: hp(7),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
  },
  closeButton: {
    padding: wp(2),
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    marginRight: wp(10),
  },
  content: {
    flex: 1,
    padding: wp(4),
    borderTopLeftRadius: wp(5),
    borderTopRightRadius: wp(5),
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(2),
    marginHorizontal:wp(5),
  },
  filterText: {
    marginLeft: wp(3),
    fontSize: 14,
  },
  footer: {
    padding: wp(4),
  },
  applyButton: {
    height: hp(6),
    borderRadius: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
});