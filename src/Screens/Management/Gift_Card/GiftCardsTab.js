import React, { useCallback, useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  TouchableOpacity, 
  FlatList, 
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import GiftCard from '../../../Components/Gift_Cards/GiftCard';
import { Gift_Mock } from '../../../Mocks/Product_mock';
import { get_all_giftcard } from '../../../Services/API/Endpoints/Admin/Giftcard';
import Loader from '../../../Custom/loader';



const GiftCardsTab = () => {
  const navigation = useNavigation();
  const AppText = useAppText();

  const [allcardData, setAllCradData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filteredShops, setFilteredShops] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);


   const onRefresh = useCallback(() => {
    getAllgiftcard();
        setSearchQuery('');
      }, []);
    
      useFocusEffect(
        useCallback(() => {
          getAllgiftcard();
        }, []),
      );




   const getAllgiftcard = async () => {
          try {
              setLoading(true);
              setRefreshing(true);
      
              const response = await get_all_giftcard();
              console.log('🟢 Cards fetched:', response);
      
              if (Array.isArray(response)) {
                  const sortedServices = response.sort((a, b) => b.id - a.id);
                  setAllCradData(sortedServices);
              }
          } catch (error) {
              console.error('❌ Failed to fetch services:', error);
          } finally {
              setLoading(false);
              setRefreshing(false);
          }
      };



  const { 
    backgroundColor, 
    getTextColor,
    getDark_Theme
  } = useTheme();

  const handleGiftCardPress = (giftCard) => {
    console.log("the giftcard is -----",giftCard)
    navigation.navigate('View_Gift_Card', { giftCard });
  };

  const renderGiftCardItem = ({ item }) => (
    <GiftCard
      item={item}
      onPress={() => handleGiftCardPress(item)}
    />
  );
  
  if (loading) return <Loader />;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={[styles.container, { backgroundColor }]}
    >
      <View style={styles.content}>
        {/* Search and Add Button */}
        <View style={styles.searchRow}>
          <View style={[styles.searchContainer, { borderColor: getDark_Theme() }]}>
            <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0, wp(2), 0, 0]} />
            <TextInput
              style={[styles.searchInput, { color: getTextColor() }]}
              placeholder={AppText.SEARCH_CARD}
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
            onPress={() => navigation.navigate('Add_Gift_Card')}
          >
            <ResponsiveText color={colors.white}>{AppText.ADD_GIFT_CARD}</ResponsiveText>
          </TouchableOpacity>
        </View>
  
        {/* Gift Cards List */}
        {allcardData?.length > 0 ? (
          <FlatList
            data={allcardData}
            renderItem={renderGiftCardItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.giftCardsContainer}
            showsVerticalScrollIndicator={false}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        ) : (
          <View style={[styles.giftCardsContainer, { justifyContent: 'center', alignItems: 'center' }]}>
            <ResponsiveText color={colors.grey}>{AppText.NO_RECORD_FOUND}</ResponsiveText>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default GiftCardsTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  giftCardsContainer: {
    flexGrow: 1,
  }
}); 