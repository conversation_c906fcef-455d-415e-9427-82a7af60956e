import React, { useCallback , useState, useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  TouchableOpacity, 
  FlatList, 
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import AddVoucherModal from '../../../Components/Gift_Cards/AddVoucherModal';
import VoucherCard from '../../../Components/Gift_Cards/VoucherCard';
import { get_add_giftproduct_type , delete_giftproduct_type, update_gift_type } from '../../../Services/API/Endpoints/Admin/Giftcard';
import Loader from '../../../Custom/loader';

const VouchersTab = () => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { 
    backgroundColor, 
    getTextColor,
    getDark_Theme
  } = useTheme();

  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [vouchers, setVouchers] = useState([]);
  const [editingVoucher, setEditingVoucher] = useState(null);
    const [loading, setLoading] = useState(false);
   const [refreshing, setRefreshing] = useState(false);
    
  
   const onRefresh = useCallback(() => {
    setRefreshing(true); // Set refreshing to true when the user pulls to refresh
    fetchProductstype(); // Refresh the vouchers list
  }, []);
  // Filter vouchers based on search query
  const filteredVouchers = useMemo(() => {
    if (!searchQuery.trim()) return vouchers;
    
    return vouchers.filter(voucher => 
      voucher.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [vouchers, searchQuery]);

  const handleSaveVoucher = async (voucherName) => {
    if (editingVoucher) {
      try {
        const payload = { name: voucherName.trim() };
        const response = await update_gift_type(editingVoucher.id, payload);
        setVouchers(vouchers.map(voucher =>
          voucher.id === editingVoucher.id
            ? { ...voucher, name: voucherName }
            : voucher
        ));
        setEditingVoucher(null);
        Alert.alert('Success', 'Voucher updated successfully!');
      } catch (error) {
        console.error('❌ Failed to update voucher:', error);
        Alert.alert('Error', 'Failed to update voucher.');
      }
    } else {
      // We now expect the modal to call the `add_gift_type` API and return the response
      const newVoucher = {
        id: Date.now(), // fallback in case API doesn't return an ID
        name: voucherName,
      };
      setVouchers([...vouchers, newVoucher]);
    }
  
    setModalVisible(false);
  };

  const handleEditVoucher = (voucher) => {
   console.log("Voucher is ", voucher)
    setEditingVoucher(voucher);
    setModalVisible(true);
  };

  // Function to handle voucher deletion with confirmation
const handleDeleteVoucher = (voucherId) => {
  // Display the confirmation alert
  Alert.alert(
    'Confirm Deletion', // Alert title
    'Are you sure you want to delete this voucher?', // Alert message
    [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: () => console.log('Deletion cancelled'),
      },
      {
        text: 'OK',
        onPress: async () => {
          try {
            // Call the delete API function
            await delete_giftproduct_type(voucherId);
            // Update the vouchers state to remove the deleted voucher
            setVouchers((prevVouchers) =>
              prevVouchers.filter((voucher) => voucher.id !== voucherId)
            );
            // Optionally, refresh the vouchers list
            fetchProductstype();
            console.log(`✅ Voucher ${voucherId} deleted.`);
          } catch (error) {
            console.error(`❌ Failed to delete voucher ${voucherId}:`, error);
          }
        },
      },
    ],
    { cancelable: false } // Prevent dismissing the alert by tapping outside
  );
};


  useFocusEffect(
          useCallback(() => {
            fetchProductstype();
          }, []),
        );

   const fetchProductstype = async () => {
    setLoading(true);
      try {
        const response = await get_add_giftproduct_type();
        console.log('🟢 Types fetched:', response);
        setVouchers(Array.isArray(response) ? response : []);
      } catch (error) {
        console.error('❌ Error fetching products:', error);
      }
      finally{
        setLoading(false); // Stop loading
    setRefreshing(false); // Stop refreshing
      }
    };

  if (loading) return <Loader />;

    

  const renderVoucherItem = ({ item }) => (
    <VoucherCard
      title={item.name}
      getTextColor={getTextColor}
      getDark_Theme={getDark_Theme}
      onEdit={() => handleEditVoucher(item)}
      onDelete={() => handleDeleteVoucher(item.id)}
    />
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : ''}
      style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        <View style={[styles.searchRow]}>
          <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
            <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_VOUCHERS}
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          <TouchableOpacity 
            style={[styles.addButton, {backgroundColor: colors.Light_theme_maincolour}]}
            onPress={() => {
              setEditingVoucher(null);
              setModalVisible(true);
            }}
          >
            <ResponsiveText color={colors.white}>{AppText.ADD_VOUCHER}</ResponsiveText>
          </TouchableOpacity>
        </View>
        
        {loading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
            </View>
          ) : (
            <FlatList
              data={filteredVouchers}
              renderItem={renderVoucherItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.productsContainer}
              showsVerticalScrollIndicator={false}
              refreshing={refreshing}
              onRefresh={onRefresh}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <ResponsiveText color={getTextColor()} size={4}>
                    {searchQuery.trim() ? AppText.NO_RESULTS_FOUND : AppText.NO_PRODUCTS}
                  </ResponsiveText>
                </View>
              }
            />
          )}

<AddVoucherModal
  visible={modalVisible}
  onClose={() => setModalVisible(false)}
  onSave={handleSaveVoucher}
  initialValue={editingVoucher?.name}
  editingVoucher={editingVoucher} // ✅ pass this prop
/>
      </View>
    </KeyboardAvoidingView>
  );
};

export default VouchersTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  vouchersContainer: {
    flexGrow: 1,
    paddingTop: hp(2),
    paddingBottom: hp(29)

  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  
}); 