import React from 'react';
import { StyleSheet, SafeAreaView, Platform } from 'react-native';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import { colors } from '../../../Custom/Colors';
import ProductsTab from './ProductsTab';
import VouchersTab from './VouchersTab';
import { hp, wp } from '../../../Custom/Responsiveness';

const Tab = createMaterialTopTabNavigator();

const OthersTab = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: colors.grey,
          tabBarPressColor: 'transparent',
          tabBarShowIcon: false,
          tabBarItemStyle: styles.tabItem,
          tabBarIndicatorContainerStyle: [styles.indicatorContainer, { backgroundColor: colors.lightGrey1 }],
        }}
      >
        <Tab.Screen 
          name="Products" 
          component={ProductsTab}
          options={{
            tabBarLabel: AppText.PRODUCTS
          }}
        />
        <Tab.Screen 
          name="Vouchers" 
          component={VouchersTab}
          options={{
            tabBarLabel: AppText.VOUCHERS
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  );
};

export default OthersTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    // backgroundColor: colors.lightGrey,
    // elevation: 0,
    // shadowOpacity: 0,
    // borderRadius: 8,
    marginHorizontal: wp(2),
    marginTop: hp(1),
    height: hp(6),
  },
  tabItem: {
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: Platform.OS==='ios'? 4:null,
  },
  indicatorContainer: {
    // backgroundColor: colors.white,
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: 4,
  },
  tabIndicator: {
    backgroundColor: colors.Light_theme_maincolour,
    height: '100%',
    borderRadius: 8,
    // width:"30%"
  },
  tabLabel: {
    textTransform: 'none',
    fontSize: 14,
    fontWeight: '500',
  },
}); 