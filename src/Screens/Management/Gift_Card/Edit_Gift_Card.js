import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import CustomHeader from '../../../Components/CustomHeader';
import CustomTextInput from '../../../Components/CustomTextInput';
import Gift_card_upload_image from '../../../Components/Gift_Cards/Gift_card_upload_image';
import DynamicDropdown from '../../../Components/Services_Components/DynamicDropdown';
import { get_gift_product_name } from '../../../Services/API/Endpoints/Admin/Giftcard';
import { get_add_giftproduct_type } from '../../../Services/API/Endpoints/Admin/Giftcard';
import { update_giftcard_Form } from '../../../Services/API/Endpoints/Admin/Giftcard';

const Edit_Gift_Card = ({ route, navigation }) => {
  const { giftCard } = route.params;
  const { backgroundColor, getTextColor, getDark_Theme } = useTheme();
  const AppText = useAppText();
  const [formData, setFormData] = useState({
    product: giftCard.product,
    product_type: giftCard.product_type,
    description: giftCard.description,
    price: giftCard.price,
  });
  const [errors, setErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(giftCard.giftcard_image ? {
    uri: giftCard.giftcard_image,
    fileName: giftCard.giftcard_image.split('/').pop(),
    type: 'image/jpeg'
  } : null);
  const [productOptions, setProductOptions] = useState([]);
  const [typeOptions, setTypeOptions] = useState([]);
  const[loading,setLoading]=useState(false)

  useEffect(() => {
    // Load product and type options
    const loadOptions = async () => {
      try {
        const products = await get_gift_product_name();
        const types = await get_add_giftproduct_type();
        
        setProductOptions(products);
        setTypeOptions(types);
      } catch (error) {
        console.error('Error loading options:', error);
      }
    };
    
    loadOptions();
  }, []);

  const getProductName = (productId) => {
    const product = productOptions.find(p => p.id === productId);
    return product ? product.name : giftCard.product_name;
  };

  const getProductTypeName = (typeId) => {
    const type = typeOptions.find(t => t.id === typeId);
    return type ? type.name : giftCard.product_type_name;
  };

  const isFormValid = () => {
    return (
      formData.product &&
      formData.product_type &&
      formData.description.trim() !== '' &&
      formData.price.trim() !== ''
    );
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleImageSelect = (imageDetails) => {
    setSelectedImage(imageDetails);
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.product) {
      newErrors.product = 'Name is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    }
    if (!formData.product_type) {
      newErrors.product_type = 'Stock is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setLoading(true);

    const formDataPayload = new FormData();
    formDataPayload.append('product', String(formData.product).trim());
    formDataPayload.append('product_type', String(formData.product_type).trim());
    formDataPayload.append('description', formData.description.trim());
    formDataPayload.append('price', formData.price.trim());
  
    if (selectedImage?.uri && selectedImage.uri !== giftCard.giftcard_image) {
      formDataPayload.append('giftcard_image', {
        uri: selectedImage.uri,
        name: selectedImage.fileName || 'giftcard.jpg',
        type: selectedImage.type || 'image/jpeg',
      });
    } else if (!selectedImage && giftCard.giftcard_image) {
      // This ensures the backend removes the image if the user deleted it
      formDataPayload.append('giftcard_image', '');
    }
  
    try {
      await update_giftcard_Form(giftCard.id, formDataPayload);
  
      Alert.alert('Success', 'Gift card updated successfully!');
      navigation.pop(2);
      // navigation.reset({
      //   index: 0, // This makes the first screen of the stack the active one
      //   routes: [{ name: 'GiftCards' }],  // This specifies which screen to go to
      // });
    } catch (error) {
      console.error('Error updating gift card:', error);
      Alert.alert('Error', 'Failed to update gift card. Please try again.');
    }
    
    finally {
      setLoading(false); // Set loading to false after submission
    }
    
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.EDIT_GIFT_CARD} leftIconType="back" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={styles.container}
      >
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          <DynamicDropdown
            title={AppText.NAME}
            placeholder={AppText.ENTER_YOUR_NAME}
            value={formData.product}
            onChange={value => handleChange('product', value)}
            fetchData={get_gift_product_name}
            error={errors.product}
          />

          <DynamicDropdown
            title={AppText.STOCK}
            placeholder={AppText.ENTER_YOUR_STOCK}
            value={formData.product_type}
            onChange={value => handleChange('product_type', value)}
            fetchData={get_add_giftproduct_type}
            error={errors.product_type}
          />

          <View style={styles.descriptionContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, hp(1), 0]}
            >
              {AppText.DESCRIPTION}
            </ResponsiveText>
            <TextInput
              style={[
                styles.descriptionInput,
                {
                  color: getTextColor(),
                  borderColor: errors.description ? colors.red : getDark_Theme(),
                  backgroundColor: backgroundColor,
                },
              ]}
              placeholder={AppText.ENTER_DESCRIPTION}
              placeholderTextColor={colors.grey}
              value={formData.description}
              onChangeText={(value) => handleChange('description', value)}
              multiline
              numberOfLines={4}
            />
            {errors.description && (
              <ResponsiveText color={colors.red} size={3} margin={[hp(0.5), 0, 0, 0]}>
                {errors.description}
              </ResponsiveText>
            )}
          </View>

          <CustomTextInput
            label={AppText.PRICE}
            placeholder={AppText.ENTER_YOUR_PRICE}
            value={formData.price}
            onChangeText={(value) => handleChange('price', value)}
            keyboardType="numeric"
            error={errors.price}
          />

          <Gift_card_upload_image 
            onImageSelect={handleImageSelect}
            initialImage={selectedImage}
          />

<TouchableOpacity
            style={[
              styles.editButton,
              {
                backgroundColor: isFormValid() ? colors.Light_theme_maincolour : colors.grey,
                opacity: isFormValid() ? 1 : 0.5,
              },
            ]}
            disabled={!isFormValid() || loading} // Disable button while loading
            onPress={handleSubmit}
          >
            {loading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <ResponsiveText color={colors.white} size={4} weight="bold">
                {AppText.EDIT_GIFT_CARD}
              </ResponsiveText>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Edit_Gift_Card;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: hp(2),
  },
  descriptionContainer: {
    marginBottom: hp(2),
  },
  descriptionInput: {
    height: hp(12),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    paddingTop: wp(4),
    fontSize: wp(3.8),
    textAlignVertical: 'top',
  },
  editButton: {
    marginTop: hp(2),
    marginBottom: hp(4),
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
});