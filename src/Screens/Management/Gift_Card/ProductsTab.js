import React, { useCallback , useState, useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  TouchableOpacity, 
  FlatList, 
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ResponsiveText from '../../../Custom/RnText';
import { colors } from '../../../Custom/Colors';
import { hp, wp } from '../../../Custom/Responsiveness';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import AddProductModal from '../../../Components/Gift_Cards/AddProductModal';
import ProductCard from '../../../Components/Gift_Cards/ProductCard';
import { get_gift_product_name , delete_giftproduct_name } from '../../../Services/API/Endpoints/Admin/Giftcard';
import Loader from '../../../Custom/loader';

const ProductsTab = () => {
  const navigation = useNavigation();
  const AppText = useAppText();
  const { 
    backgroundColor, 
    getTextColor,
    getDark_Theme
  } = useTheme();

  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState([]);
  const [editingProduct, setEditingProduct] = useState(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  

  const onRefresh = useCallback(() => {
    setRefreshing(true); // Set refreshing to true when the user pulls to refresh
    fetchProducts(); // Fetch products
  }, []);
      
       
  

  // Filter products based on search query
  const filteredProducts = useMemo(() => {
    if (!searchQuery.trim()) return products;
    
    return products.filter(product => 
      product.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [products, searchQuery]);

  useFocusEffect(
        useCallback(() => {
          setRefreshing(true)
          fetchProducts();
        }, []),
      );



      const fetchProducts = async () => {
        setLoading(true);
        try {
          const response = await get_gift_product_name();
          console.log('🟢 Products fetched:', response);
          setProducts(Array.isArray(response) ? response : []);
        } catch (error) {
          console.error('❌ Error fetching products:', error);
        } finally {
          setLoading(false);
          setRefreshing(false); // Stop refreshing after the fetch operation

        }
      };
      



  

  const handleSaveProduct = (productName) => {
    if (editingProduct) {
      // Update existing product
      setProducts(products.map(product => 
        product.id === editingProduct.id 
          ? { ...product, name: productName }
          : product
      ));
      setEditingProduct(null);
    } else {
      // Add new product
      const newProduct = {
        id: Date.now(),
        name: productName
      };
      setProducts([...products, newProduct]);
    }
    setModalVisible(false);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setModalVisible(true);
  };

  const handleDeleteProduct = (productId) => {
   


    Alert.alert(
        'Confirm Deletion', // Alert title
        'Are you sure you want to delete this voucher?', // Alert message
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => console.log('Deletion cancelled'),
          },
          {
            text: 'OK',
            onPress: async () => {
              try {
                // Call the delete API function
                await delete_giftproduct_name(productId);
                // Update the vouchers state to remove the deleted voucher
                setProducts(products.filter(product => product.id !== productId));
                // Optionally, refresh the vouchers list
                fetchProducts();
                console.log(`✅ Voucher ${productId} deleted.`);
              } catch (error) {
                console.error(`❌ Failed to delete voucher ${productId}:`, error);
              }
            },
          },
        ],
        { cancelable: false } // Prevent dismissing the alert by tapping outside
      );
  };

  const renderProductItem = ({ item }) => (
    <ProductCard
      title={item.name}
      getTextColor={getTextColor}
      getDark_Theme={getDark_Theme}
      onEdit={() => handleEditProduct(item)}
      onDelete={() => handleDeleteProduct(item.id)}
    />
  );
  if (loading) return <Loader />;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : ''}
      style={[styles.container, { backgroundColor, }]}>
      <View style={styles.content}>
        <View style={[styles.searchRow]}>
          <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
            <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_PRODUCTS}
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>


          <TouchableOpacity 
            style={[styles.addButton, {backgroundColor: colors.Light_theme_maincolour}]}
            onPress={() => {
              setEditingProduct(null);
              setModalVisible(true);
            }}
          >
            <ResponsiveText color={colors.white}>{AppText.ADD_PRODUCT}</ResponsiveText>
          </TouchableOpacity>
        </View>
        
        {loading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
            </View>
          ) : (
            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.productsContainer}
              showsVerticalScrollIndicator={false}
              refreshing={refreshing}
              onRefresh={onRefresh}
            
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <ResponsiveText color={getTextColor()} size={4}>
                    {searchQuery.trim() ? AppText.NO_RESULTS_FOUND : AppText.NO_PRODUCTS}
                  </ResponsiveText>
                </View>
              }
            />
          )}

<AddProductModal
  visible={modalVisible}
  onClose={() => setModalVisible(false)}
  onSave={handleSaveProduct}
  initialValue={editingProduct?.name}
  productToEdit={editingProduct}
/>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ProductsTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  productsContainer: {
    flexGrow: 1,
    paddingTop: hp(2),
    paddingBottom: hp(29)
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // paddingTop: hp(20),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 