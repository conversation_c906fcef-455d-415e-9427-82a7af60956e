import React, { useEffect, useState , useCallback } from 'react';
import { StyleSheet, View, SafeAreaView, TouchableOpacity, TextInput, FlatList, ActivityIndicator, KeyboardAvoidingView, Platform, RefreshControl } from 'react-native';
import CustomHeader from '../../Components/CustomHeader';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { wp, hp } from '../../Custom/Responsiveness';
import { barberMockData } from '../../Mocks/service_mock';
import BarberCard from '../../Components/Salons/BarberCard';
import { get_all_barber } from '../../Services/API/Endpoints/Admin/Barber';
import { useFocusEffect } from '@react-navigation/native';
import Loader from '../../Custom/loader';

const Barber = ({navigation}) => {
  const AppText = useAppText();
  const [chooseBarber, setChooseBarber] = useState(null);
  const [barbers, setBarbers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedBarber, setSelectedBarber] = useState(null);
  const [filteredBarbers, setFilteredBarbers] = useState([]);
  const { 
    themeColor,
    getDarK_mode_LightGrayBackground, 
    backgroundColor, 
    getTextColor,
    getSecondaryTextColor,
    getLightGrayBackground,
    getborderTextColor,
    getBellBackground,
    getDark_Theme,
  } = useTheme();

  const handleBarberSelect = barber => {
    setSelectedBarber(barber);
    navigation.navigate('View_Ratings_Brber_Details', { barber });
    setSelectedBarber(null)
    handleSearch('');
  };

  const fetchBarbers = async () => {
    try {
      setLoading(true);
      const response = await get_all_barber();
      console.log('🟢 Barbers fetched:', response);
      // Sort barbers in reverse order by ID (newest first)
      const sortedBarbers = response.sort((a, b) => b.id - a.id);
      setBarbers(sortedBarbers);
    } catch (error) {
      console.error('❌ Failed to fetch barbers:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchBarbers();
    handleSearch('');
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchBarbers();
    }, [])
  );

  const handleSearch = (text) => {
    setChooseBarber(text);
    if (text) {
      const filtered = barbers.filter(barber =>
        barber.full_name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredBarbers(filtered);
    } else {
      setFilteredBarbers(barbers);
    }
  };

  useEffect(() => {
    setFilteredBarbers(barbers);
  }, [barbers]);
  const handleAddBarber = () => {
    navigation.navigate('AddnewBarber');
    handleSearch('');
  };

  const renderBarberItem = ({ item }) => (
    <BarberCard
      item={item}
      isSelected={selectedBarber?.id === item.id}
      onPress={() => handleBarberSelect(item)}
    />
  );

  return (
    <SafeAreaView style={[styles.container,{backgroundColor: backgroundColor}]}>
      <CustomHeader title={AppText.BARBER} leftIconType="back"  />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex: 1}}>  
      <View style={styles.content}>
        <View style={[styles.searchRow]}>
          <View
            style={[
              styles.searchContainer,
              {borderColor: getDark_Theme()},
            ]}>
            <Icon
              source={globalpath.search}
              size={20}
              tintColor={getTextColor()}
              margin={[0, wp(2), 0, 0]}
            />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_BARBER}
              placeholderTextColor={colors.grey}
              value={chooseBarber}
              onChangeText={handleSearch}
            />
          </View>
          <TouchableOpacity
            style={[
              styles.addButton,
              {backgroundColor: colors.Light_theme_maincolour},
            ]}
            onPress={handleAddBarber}>
            <ResponsiveText color={colors.white}>
              {AppText.ADD_BARBER} 
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <ResponsiveText color={getTextColor()} size={5} weight="bold">
              {AppText.OUR_BARBER}
            </ResponsiveText>
          </View>
        </View>
          <FlatList
            data={filteredBarbers}
            renderItem={renderBarberItem}
            keyExtractor={item => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.barberList}
            ListEmptyComponent={() => (
              <View style={styles.noResults}>
                <ResponsiveText color={getTextColor()} size={4}>
                  {AppText.NO_RESULTS_FOUND}
                </ResponsiveText>
              </View>
            )}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.Light_theme_maincolour]}
                tintColor={colors.Light_theme_maincolour}
              />
            }
          />
      </View>
      </KeyboardAvoidingView>
      {loading? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default Barber;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  searchContainer: {
    height: hp(5),
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    backgroundColor: null
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(1.5),
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  barberList: {
    flexGrow: 1,
    // marginBottom: hp(2),
    // paddingBottom: hp(20),
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  }
});