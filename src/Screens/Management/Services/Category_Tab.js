import React, { useCallback, useState, useEffect } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, FlatList, RefreshControl } from 'react-native';
import { hp, wp } from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import { colors } from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';
import ServiceTab_Service_Card from '../../../Components/Services_Components/ServiceTab_Service_Card';
import ServiceDetailModal from '../../../Components/Salons/ServiceDetailModal';
import { serviceMockData } from '../../../Mocks/service_mock';
import CategoryCard from './CategoryCard';
import Add_Category_Modal from '../../../Components/Services_Components/Add_Category_Modal';
import { categoryMockData } from '../../../Mocks/category_mock';
import { get_all_category , get_sub_category } from '../../../Services/API/Endpoints/Admin/listCategoary';
import { useFocusEffect } from '@react-navigation/core';
import Loader from '../../../Custom/loader';

const Category_Tab = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [expandedCategoryId, setExpandedCategoryId] = useState(null);
  const [category, setCategory] = useState([]);
  const [filteredCategory, setFilteredCategory] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const { 
    themeColor,
    getDarK_mode_LightGrayBackground, 
    backgroundColor, 
    getTextColor,
    getSecondaryTextColor,
    getLightGrayBackground,
    getborderTextColor,
    getBellBackground,
    getDark_Theme
  } = useTheme();
  const AppText = useAppText();
   
  const handleAddCategory = async () => {
    setShowAddModal(false);
    await listCategoary();
  };

  const handleCategoryPress = (categoryId) => {
    setExpandedCategoryId(expandedCategoryId === categoryId ? null : categoryId);
  };

  const handleSearch = (text) => {
    setSearchText(text);
    if (text) {
      const filtered = category.filter(item => {
        const categoryMatch = item.title.toLowerCase().includes(text.toLowerCase());
        const subCategoryMatch = item.subCategories.some(sub => 
          sub.title.toLowerCase().includes(text.toLowerCase())
        );
        return categoryMatch || subCategoryMatch;
      });
      setFilteredCategory(filtered);
    } else {
      setFilteredCategory(category);
    }
  };

  useEffect(() => {
    setFilteredCategory(category);
  }, [category]);

  const renderCategoryItem = ({ item }) => {
    return (
      <CategoryCard 
        key={item.id}
        title={item.title}
        subCategories={item.subCategories}
        getSecondaryTextColor={getSecondaryTextColor}
        getTextColor={getTextColor}
        getBellBackground={getBellBackground}
        getDark_Theme={getDark_Theme}
        categoryId={item.id}
        onClose={() => setShowAddModal(false)}
        onSave={handleAddCategory}
        isExpanded={expandedCategoryId === item.id}
        onPress={() => handleCategoryPress(item.id)}
        onAddSubCategory={() => {
          setExpandedCategoryId(item.id);
          setShowAddModal(true);
        }}
      />
    );
  };

  const listCategoary = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
  
      const response = await get_all_category();
      console.log('🟢 Categories fetched:', response);
  
      if (Array.isArray(response)) {
        const sortedCategories = response.sort((a, b) => b.id - a.id);
  
        const categoriesWithSub = await Promise.all(
          sortedCategories.map(async (category) => {
            try {
              const subRes = await get_sub_category(category.id);
              return {
                id: category.id,
                title: category.name,
                subCategories: subRes.map((sub) => ({
                  id: sub.id,
                  title: sub.name,
                })),
              };
            } catch (subError) {
              console.error(`❌ Failed to fetch subcategories for category ${category.id}:`, subError);
              return {
                id: category.id,
                title: category.name,
                subCategories: [],
              };
            }
          })
        );
  
        setCategory(categoriesWithSub);
        setFilteredCategory(categoriesWithSub);
      }
    } catch (error) {
      console.error('❌ Failed to fetch categories:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    listCategoary();
    handleSearch('');
  }, []);

  useFocusEffect(
    useCallback(() => {
      listCategoary();
    }, []),
  );



  return (
    <View style={[styles.container,{backgroundColor:backgroundColor}]}>
      <View style={styles.searchRow}>
        <View style={[styles.searchContainer, { borderColor: getDark_Theme() }]}>
          <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0, wp(2), 0, 0]} />
          <TextInput
            style={[styles.searchInput, { color: getTextColor() }]}
            placeholder={AppText.SEARCH_YOUR_CATEGORY}
            placeholderTextColor={colors.grey}
            value={searchText}
            onChangeText={handleSearch}
          />
        </View>
        <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: colors.Light_theme_maincolour }]}
          onPress={() => setShowAddModal(true)}
        >
          <ResponsiveText color={colors.white}>{AppText.ADD_CATEGORY}</ResponsiveText>
        </TouchableOpacity>
      </View>
      <FlatList
        data={filteredCategory}
        renderItem={renderCategoryItem}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.Light_theme_maincolour]}
            tintColor={colors.Light_theme_maincolour}
          />
        }
        ListEmptyComponent={() => (
          <View style={styles.noResults}>
            <ResponsiveText color={getTextColor()} size={4}>
              {AppText.NO_RESULTS_FOUND}
            </ResponsiveText>
          </View>
        )}
      />
      
      <Add_Category_Modal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSave={handleAddCategory}
        categoryId={expandedCategoryId}
      />
      {loading ? <Loader /> : undefined}
    </View>
  );
};

export default Category_Tab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: wp(3),
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  noResults: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(5),
    height: hp(50),
  },
});