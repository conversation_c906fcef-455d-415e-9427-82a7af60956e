import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { wp, hp } from '../../../Custom/Responsiveness';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import Icon from '../../../Custom/Icon';
import { globalpath } from '../../../Custom/globalpath';    
import SubCategoryList from '../../../Components/Services_Components/SubCategoryList';
import * as Animatable from 'react-native-animatable';

const CategoryCard = ({ 
  title, 
  subCategories, 
  getSecondaryTextColor, 
  getTextColor, 
  getBellBackground, 
  getDark_Theme,
  categoryId,
  onClose, onSave,
  isExpanded,
  onPress
}) => {

  return (
    <View style={styles.wrapper}>
      <TouchableOpacity 
        style={[styles.cardContainer, {
          borderColor: getDark_Theme(),
          borderWidth: 1.5
        }]}
        activeOpacity={0.7}
        onPress={onPress}
      >
        <View style={[styles.colorStrip, {backgroundColor: colors.Light_theme_maincolour}]} />
        <View style={styles.cardContent}>
          <ResponsiveText color={getTextColor()} size={5} weight={'600'}>
            {title}
          </ResponsiveText>
          <TouchableOpacity style={[styles.backicon,{backgroundColor:getBellBackground()}]}>
            <Icon 
              source={globalpath.down} 
              tintColor={getTextColor()}  
              size={wp(4)} 
              resizeMode="contain"
              style={[
                styles.arrowIcon,
                isExpanded && styles.arrowIconRotated
              ]}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
      <Animatable.View 
        duration={300}
        animation={isExpanded ? "slideInDown" : "slideOutUp"}
        style={{ overflow: 'hidden' }}
      >
        {isExpanded && (
          <SubCategoryList 
            subCategories={subCategories}
            getTextColor={getTextColor}
            getDark_Theme={getDark_Theme}
            categoryId={categoryId}
            onSave={onSave}
            onClose={onClose}
          />
        )}
      </Animatable.View>
    </View>
  );
};

export default CategoryCard;

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: hp(2),
  },
  cardContainer: {
    // marginHorizontal: wp(4),
    // marginTop: hp(2),
    borderRadius: wp(3),
    overflow: 'hidden',
    flexDirection: 'row',
    borderTopLeftRadius: wp(3),
    borderBottomLeftRadius: wp(3),
    // backgroundColor:"#292929"
  },
  colorStrip: {
    width: wp(4),
    borderTopLeftRadius: wp(2.5),
    borderBottomLeftRadius: wp(2.5),
  },
  cardContent: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(1.5),
    marginHorizontal: wp(4),
    borderRadius: wp(2),
    alignItems: "center",
    justifyContent: "space-between",
    flexDirection: "row",
    flex: 1
  },
  backicon: {
    padding: wp(2),
    borderRadius: wp(8),
  },
  arrowIcon: {
    transform: [{ rotate: '0deg' }]
  },
  arrowIconRotated: {
    transform: [{ rotate: '180deg' }]
  }
}); 