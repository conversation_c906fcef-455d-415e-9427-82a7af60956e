import React, {useCallback, useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
} from 'react-native';
import {hp, wp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import {colors} from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import ServiceTab_Service_Card from '../../../Components/Services_Components/ServiceTab_Service_Card';
import ServiceDetailModal from '../../../Components/Salons/ServiceDetailModal';
import {serviceMockData} from '../../../Mocks/service_mock';
import {get_all_service} from '../../../Services/API/Endpoints/Admin/Services';
import {useFocusEffect} from '@react-navigation/core';
import Loader from '../../../Custom/loader';

const Services_tab = ({navigation}) => {
  const {getDark_Theme, getTextColor, backgroundColor} = useTheme();
  const AppText = useAppText();
  const [selectedService, setSelectedService] = useState(null);
  const [showServiceDetail, setShowServiceDetail] = useState(false);
  const [Services, setServices] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filteredServices, setFilteredServices] = useState([]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setRefreshing(true);

      const response = await get_all_service();
      console.log('🟢 Services fetched:', response);

      if (Array.isArray(response)) {
        const sortedServices = response.sort((a, b) => b.id - a.id);
        setServices(sortedServices);
        setFilteredServices(sortedServices);
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    fetchServices();
    handleSearch('');
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchServices();
    }, []),
  );

  const handleSearch = (text) => {
    setSearchText(text);
    if (text) {
      const filtered = Services.filter(service =>
        service.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredServices(filtered);
    } else {
      setFilteredServices(Services);
    }
  };

  useEffect(() => {
    setFilteredServices(Services);
  }, [Services]);

  const handleServicePress = service => {
    setSelectedService(service);
    setShowServiceDetail(true);
    // handleSearch('');
  };

  const handleAddService = () => {
    navigation.navigate('Add Service');
    handleSearch('');
  };

  const renderServiceItem = ({item}) => (
    <ServiceTab_Service_Card
      key={item.id}
      item={{
        id: item.id,
        service_image: item.service_image,
        title: item.name,
        description: item.description,
        duration: `${item.duration} min`,
        amount: item.price,
        category: item.category,
        sub_category: item.sub_category,
        note: item.note,
        created_at: item.created_at
      }}
      onPress={() => handleServicePress(item)}
      onSeeDetails={() => handleServicePress(item)}
      openscreen={true}
      Services={item}
    />
  );

  return (
    <View style={[styles.container, {backgroundColor: backgroundColor}]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={{flex: 1}}>
          <View style={{flex:1,}}>
        <View style={styles.searchRow}>
          <View
            style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
            <Icon
              source={globalpath.search}
              size={20}
              tintColor={getTextColor()}
              margin={[0, wp(2), 0, 0]}
            />
            <TextInput
              style={[styles.searchInput, {color: getTextColor()}]}
              placeholder={AppText.SEARCH_SERVICES}
              placeholderTextColor={colors.grey}
              value={searchText}
              onChangeText={handleSearch}
            />
          </View>
          <TouchableOpacity
            style={[
              styles.addButton,
              {backgroundColor: colors.Light_theme_maincolour},
            ]}
            onPress={handleAddService}>
            <ResponsiveText color={colors.white}>
              {AppText.ADD_SERVICE}
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        <ResponsiveText
          color={getTextColor()}
          size={5}
          weight="bold"
          margin={[hp(2), 0, hp(2), 0]}>
          {AppText.OUR_SERVICES}
        </ResponsiveText>

        <FlatList
          data={filteredServices}
          renderItem={renderServiceItem}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.Light_theme_maincolour]}
              tintColor={colors.Light_theme_maincolour}
            />
          }
          ListEmptyComponent={() => (
            <View style={styles.noResults}>
              <ResponsiveText color={getTextColor()} size={4}>
                {AppText.NO_RESULTS_FOUND}
              </ResponsiveText>
            </View>
          )}
        />
        <ServiceDetailModal
          visible={showServiceDetail}
          onClose={() => setShowServiceDetail(false)}
          service={selectedService}
        />
        </View>
      </KeyboardAvoidingView>
      {loading ? <Loader /> : undefined}
    </View>
  );
};

export default Services_tab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: wp(3),
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5),
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3),
  },
  noResults: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp(55),
    // paddingVertical: hp(5),
  },
});
