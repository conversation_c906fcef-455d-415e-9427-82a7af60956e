import React, {useState} from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import {hp, wp} from '../../../Custom/Responsiveness';
import CustomHeader from '../../../Components/CustomHeader';
import CustomTextInput from '../../../Components/CustomTextInput';
import CategoryDropdown from '../../../Components/Services_Components/CategoryDropdown';
import DurationSelector from '../../../Components/Services_Components/DurationSelector';
import ServiceImageUploadCard from '../../../Components/Services_Components/ServiceImageUploadCard';
import {globalpath} from '../../../Custom/globalpath';
import Icon from '../../../Custom/Icon';
import {delete_service, update_Service} from '../../../Services/API/Endpoints/Admin/Services';
import DynamicDropdown from '../../../Components/Services_Components/DynamicDropdown';
import ColorPickerModal from '../../../Components/Services_Components/ColorPickerModal';
import {
  get_all_category,
  get_sub_category,
} from '../../../Services/API/Endpoints/Admin/listCategoary';
import { useNavigation } from '@react-navigation/native';

// Function to generate a random color
const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const Add_Services_Details = ({route}) => {
  const navigation=useNavigation()
  const {service} = route.params;
  console.log('the service details are -----', service);
  console.log('the service id is ---', service?.id);
  const {
    backgroundColor,
    getTextColor,
    getDark_Theme,
    getDarK_mode_LightGrayBackground,
  } = useTheme();
  const AppText = useAppText();
  const [isEditMode, setIsEditMode] = useState(false);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [colorPickerVisible, setColorPickerVisible] = useState(false);

  const [formData, setFormData] = useState({
    serviceName: service?.name || '',
    category: service?.category || null,
    category_name: service?.category_name || '',
    subCategory: service?.sub_category || null,
    sub_category_name: service?.sub_category_name || '',
    description: service?.description || '',
    duration: service?.duration?.toString() || '30',
    price: service?.price || '',
    service_image: service?.service_image || null,
    color: service?.color || '',
  });
  const [errors, setErrors] = useState({});

  const isFormValid = () => {
    return (
      formData.serviceName.trim() !== '' &&
      formData.category !== null &&
      formData.subCategory !== null &&
      formData.description.trim() !== '' &&
      formData.price.trim() !== ''
      // Not requiring color as it can be random
    );
  };

  const handleChange = (field, value, additionalData = {}) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      ...additionalData, // Update category_name and sub_category_name
    }));

    // Log the IDs if needed
    if (field === 'category') {
      console.log('Selected Category ID:', value);
    }
    if (field === 'subCategory') {
      console.log('Selected Sub-category ID:', value);
    }

    // Remove errors for the updated field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.serviceName.trim()) {
      newErrors.serviceName = 'Service name is required';
    }
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }
    if (!formData.subCategory) {
      newErrors.subCategory = 'Sub-category is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle color selection
  const handleColorSelect = (color) => {
    handleChange('color', color);
    setColorPickerVisible(false);
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      try {
        setLoading(true);

        // Construct the payload
        const formDataPayload = new FormData();

        formDataPayload.append('name', formData.serviceName);
        formDataPayload.append('category', formData.category); // Category ID
        // formDataPayload.append('category_name', formData.category_name);
        formDataPayload.append('sub_category', formData.subCategory); // Sub-category ID
        // formDataPayload.append('sub_category_name', formData.sub_category_name);
        formDataPayload.append('description', formData.description);
        formDataPayload.append('duration', parseInt(formData.duration));
        formDataPayload.append('price', formData.price);

        // Add color to form data - use random color if none provided
        const colorToUse = formData.color || generateRandomColor();
        formDataPayload.append('color', colorToUse);

        if (
          formData.service_image &&
          !formData.service_image?.includes('http')
        ) {
          // Image selected from gallery (new image)
          formDataPayload.append('service_image', {
            uri: formData.service_image,
            name: 'service.jpg', // or use something from uri
            type: 'image/jpeg',
          });
        } else if (!formData.service_image && service?.service_image) {
          // Existing image was removed
          formDataPayload.append('service_image', '');
        }

        await update_Service(service.id, formDataPayload);
        console.log('The formData payload is -----', formDataPayload);
        setLoading(false);
        setIsEditMode(false);
        navigation.goBack();

      } catch (error) {
        console.error('Update failed:', error);
        setLoading(false);
      }
    }
  };

  const handleImageSelect = image => {
    handleChange('service_image', image?.uri || null);
    };

    // Function to handle service deletion with confirmation
  const handleDeleteService = () => {
    // Display the confirmation alert
    Alert.alert(
      'Confirm Deletion', // Alert title
      'Are you sure you want to delete this Service?', // Alert message
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => console.log('Deletion cancelled'),
        },
        {
          text: 'OK',
          onPress: async () => {
            try {
              // Call the delete API function
              await delete_service(service?.id);
              console.log(`✅ Deleted Service ${service?.id}`);
              navigation.goBack();
            } catch (error) {
              console.error(`❌ Failed to delete Service ${service?.id}:`, error);
            }
          },
        },
      ],
      { cancelable: false } // Prevent dismissing the alert by tapping outside
    );
  };


  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <CustomHeader title={AppText.ADD_SERVICE_DETAILS} leftIconType="back" />
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}>
        <View style={{flexDirection: 'row', justifyContent: 'flex-end'}}>
          {isEditMode && (
          <TouchableOpacity
            onPress={handleDeleteService}
            style={{
              backgroundColor: getDarK_mode_LightGrayBackground(),
              padding: wp(2.5),
              borderRadius: wp(5),
              marginRight: wp(3),
            }}>
            <Icon
              source={globalpath.delete_icon}
              size={wp(5)}
              tintColor={colors.red}
            />
          </TouchableOpacity>
          )}
          <TouchableOpacity
            onPress={() => setIsEditMode(true)}
            style={{
              backgroundColor: getDarK_mode_LightGrayBackground(),
              padding: wp(2.5),
              borderRadius: wp(5),
            }}>
            <Icon source={globalpath.edit} size={wp(5)} />
          </TouchableOpacity>
        </View>

        <CustomTextInput
          label={AppText.SERVICE_NAME}
          placeholder={AppText.ENTER_YOUR_NAME}
          value={formData.serviceName}
          onChangeText={value => handleChange('serviceName', value)}
          error={errors.serviceName}
          editable={isEditMode}
        />
        {isEditMode ? (
          <>
            <DynamicDropdown
              title={AppText.CHOOSE_CATEGORY}
              placeholder={AppText.ENTER_YOUR_CATEGORY}
              value={formData.category}
              onChange={(value, label) => {
                handleChange('category', value, {category_name: label});
                handleChange('subCategory', null); // Reset subcategory
                handleChange('sub_category_name', ''); // Reset subcategory name
              }}
              fetchData={get_all_category}
              error={errors.category}
            />

            <DynamicDropdown
              title={AppText.CHOOSE_SUB_CATEGORY}
              placeholder={AppText.ENTER_YOUR_SUB_CATEGORY}
              value={formData.subCategory}
              onChange={(value, label) =>
                handleChange('subCategory', value, {sub_category_name: label})
              }
              fetchData={get_sub_category}
              dependentOn={formData.category}
              dependencyAlertMessage="Please select a category first"
              error={errors.subCategory}
            />
          </>
        ) : (
          <>
            <CategoryDropdown
              value={formData.category}
              onChange={(value, label) =>
                handleChange('category', value, {category_name: label})
              }
              error={errors.category}
              isViewMode={!isEditMode}
              selectedLabel={formData.category_name}
              title={AppText.CATEGORY}
              fetchData={async () => categories}
            />

            <CategoryDropdown
              value={formData.subCategory}
              onChange={(value, label) =>
                handleChange('subCategory', value, {sub_category_name: label})
              }
              error={errors.subCategory}
              isViewMode={!isEditMode}
              selectedLabel={formData.sub_category_name}
              title={AppText.SUB_CATEGORY}
              fetchData={async () => subCategories}
              dependentOn={formData.category}
              dependencyAlertMessage="Please select a category first"
            />
          </>
        )}

        <View style={styles.descriptionContainer}>
          <ResponsiveText
            color={getTextColor()}
            size={4}
            weight="500"
            margin={[0, 0, hp(1), 0]}>
            {AppText.DESCRIPTION}
          </ResponsiveText>
          <TextInput
            style={[
              styles.descriptionInput,
              {
                color: getTextColor(),
                borderColor: errors.description ? colors.red : getDark_Theme(),
                backgroundColor: backgroundColor,
              },
            ]}
            placeholder={AppText.ENTER_DESCRIPTION}
            placeholderTextColor={colors.grey}
            value={formData.description}
            onChangeText={value => handleChange('description', value)}
            multiline
            numberOfLines={4}
            editable={isEditMode}
          />
          {errors.description && (
            <ResponsiveText
              color={colors.red}
              size={3}
              margin={[hp(0.5), 0, 0, 0]}>
              {errors.description}
            </ResponsiveText>
          )}
        </View>

        <DurationSelector
          value={formData.duration}
          onChange={value => handleChange('duration', value)}
          error={errors.duration}
          isViewMode={!isEditMode}
        />

        <CustomTextInput
          label={AppText.SET_PRICE}
          placeholder={AppText.ENTER_YOUR_PRICE}
          value={formData.price}
          onChangeText={value => handleChange('price', value)}
          keyboardType="numeric"
          error={errors.price}
          editable={isEditMode}
        />

        {/* Color Picker Section */}
        <View style={styles.colorPickerSection}>
          <ResponsiveText
            color={getTextColor()}
            size={4}
            weight="500"
            margin={[0, 0, hp(1), 0]}>
            {AppText.CHOOSE_COLOR || 'Service Color'}
          </ResponsiveText>
          <View style={styles.colorPickerRow}>
            <View
              style={[
                styles.colorPreview,
                {
                  backgroundColor: formData.color || generateRandomColor(),
                  borderStyle: formData.color ? 'solid' : 'dashed'
                }
              ]}
            />
            <ResponsiveText
              color={getTextColor()}
              size={3.5}
              margin={[0, 0, 0, wp(2)]}>
              {formData.color || 'Random color will be used'}
            </ResponsiveText>
            {isEditMode && (
              <TouchableOpacity
                style={styles.editColorButton}
                onPress={() => setColorPickerVisible(true)}>
                <Icon source={globalpath.edit} size={wp(4)} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {isEditMode ? (
          <ServiceImageUploadCard
            onImageSelect={handleImageSelect}
            initialImage={formData.service_image}
          />
        ) : (
          <View style={styles.imageContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, hp(1), 0]}>
              {AppText.SERVICE_IMAGE}
            </ResponsiveText>
            <Image
              source={
                formData.service_image
                  ? {uri: formData.service_image}
                  : globalpath.logo
              }
              style={styles.serviceImage}
              resizeMode="cover"
            />
          </View>
        )}

        {isEditMode && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.addButton,
                {
                  backgroundColor: isFormValid()
                    ? colors.Light_theme_maincolour
                    : colors.grey,
                  opacity: isFormValid() ? 1 : 0.5,
                },
              ]}
              disabled={!isFormValid() || loading}
              onPress={handleSubmit}>
              {loading ? (
                <ActivityIndicator color={colors.white} size="small" />
              ) : (
                <ResponsiveText color={colors.white} size={4} weight="bold">
                  {AppText.UPDATE_SERVICE}
                </ResponsiveText>
              )}
            </TouchableOpacity>

            {/* <TouchableOpacity
              style={[
                styles.deleteButton,
              ]}
              onPress={handleDeleteService}>
              <ResponsiveText color={colors.white} size={4} weight="bold">
                {AppText.DELETE || 'Delete Service'}
              </ResponsiveText>
            </TouchableOpacity> */}
          </View>
        )}

        {/* Color Picker Modal */}
        <ColorPickerModal
          visible={colorPickerVisible}
          onClose={() => setColorPickerVisible(false)}
          onSelectColor={handleColorSelect}
          initialColor={formData.color}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Add_Services_Details;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: hp(2),
  },
  descriptionContainer: {
    marginBottom: hp(2),
  },
  descriptionInput: {
    height: hp(12),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    paddingTop: wp(4),
    fontSize: wp(3.8),
    textAlignVertical: 'top',
  },
  colorPickerSection: {
    marginVertical: hp(2),
  },
  colorPickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorPreview: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  editColorButton: {
    marginLeft: wp(2),
    padding: wp(1),
  },
  buttonContainer: {
    marginTop: hp(2),
    marginBottom: hp(4),
  },
  addButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: hp(2),
  },
  deleteButton: {
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.red,
  },
  imageContainer: {
    marginBottom: hp(2),
  },
  serviceImage: {
    width: '100%',
    height: hp(20),
    borderRadius: wp(2),
  },
});
