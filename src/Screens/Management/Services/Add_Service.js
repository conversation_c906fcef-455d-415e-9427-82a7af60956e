import React, {useState} from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import useTheme from '../../../Redux/useTheme';
import useAppText from '../../../Custom/AppText';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import {hp, wp} from '../../../Custom/Responsiveness';
import CustomHeader from '../../../Components/CustomHeader';
import CustomTextInput from '../../../Components/CustomTextInput';
import DynamicDropdown from '../../../Components/Services_Components/DynamicDropdown';
import DurationSelector from '../../../Components/Services_Components/DurationSelector';
import ServiceImageUploadCard from '../../../Components/Services_Components/ServiceImageUploadCard';
import ColorPickerModal from '../../../Components/Services_Components/ColorPickerModal';
import {Create_Service} from '../../../Services/API/Endpoints/Admin/Services';
import {
  get_all_category,
  get_sub_category,
} from '../../../Services/API/Endpoints/Admin/listCategoary'; // Make sure this import is correct
import {useNavigation} from '@react-navigation/native';
import Loader from '../../../Custom/loader';

const Add_Service = () => {
  const {backgroundColor, getTextColor, getDark_Theme} = useTheme();
  const AppText = useAppText();
  const [formData, setFormData] = useState({
    serviceName: '',
    category: null,
    subCategory: null,
    description: '',
    duration: '30',
    price: '',
    color: '', // Empty by default
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [colorPickerVisible, setColorPickerVisible] = useState(false);
  const navigation = useNavigation();

  const isFormValid = () => {
    return (
      formData.serviceName.trim() !== '' &&
      formData.category !== null &&
      formData.subCategory !== null &&
      formData.description.trim() !== '' &&
      formData.price.trim() !== '' &&
      formData.color !== ''
    );
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  const handleImageSelect = imageDetails => {
    setSelectedImage(imageDetails);
    if (imageDetails) {
      console.log('Selected Service Image Details:', {
        name: imageDetails.fileName,
        size: imageDetails.size + ' MB',
        dimensions: imageDetails.dimensions,
        uri: imageDetails.uri,
      });
    } else {
      console.log('Service Image removed');
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.serviceName.trim()) {
      newErrors.serviceName = 'Service name is required';
    }
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }
    if (!formData.subCategory) {
      newErrors.subCategory = 'Sub-category is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    }
    if (!formData.color) {
      newErrors.color = 'Service color is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  // Handle color selection
  const handleColorSelect = (color) => {
    setFormData(prev => ({
      ...prev,
      color: color,
    }));
    setColorPickerVisible(false);
  };

  // Api Request for creating service
  const handleAddService = async () => {
    if (validateForm()) {
      console.log('Form submitted:', formData);
      // Log the selected color
      console.log('Selected service color:', formData.color);
      setLoading(true); // 🔥 Start loader

      try {
        const form = new FormData();

        form.append('name', formData.serviceName);
        form.append('description', formData.description);
        form.append('duration', formData.duration);
        form.append('price', formData.price);
        form.append('category', formData.category);
        form.append('sub_category', formData.subCategory);
        form.append('color', formData.color); // Add color to form data

        // ✅ Append image if available
        if (selectedImage?.uri) {
          form.append('service_image', {
            uri: selectedImage.uri,
            name: selectedImage.fileName || 'service.jpg',
            type: 'image/jpeg',
          });
        }

        console.log('📤 Sending FormData:', form);
        const response = await Create_Service(form);
        console.log('✅ Create_Service RESPONSE:', response);

        Alert.alert('Success', 'Service added successfully!', [
          {
            text: 'OK',
            onPress: () => {
              // Reset form and navigate back
              setUploadedImage(null);
              setFormData({
                serviceName: '',
                category: null,
                subCategory: null,
                description: '',
                duration: '30',
                price: '',
                color: '', // Reset to empty
              });
              navigation.goBack();
            },
          },
        ]);
      } catch (error) {
        console.error('❌ Error in Creating Service:', error);
        if (error.response) {
          console.error('Response Data:', error.response.data);
        }
        Alert.alert('Error', 'Failed to add service. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <CustomHeader title={AppText.ADD_SERVICE} leftIconType="back" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        style={styles.container}>
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
          nestedScrollEnabled={true} // 🔥 THIS FIXES THE NESTED SCROLL ISSUE
        >
          <CustomTextInput
            label={AppText.SERVICE_NAME}
            placeholder={AppText.ENTER_YOUR_NAME}
            value={formData.serviceName}
            onChangeText={value => handleChange('serviceName', value)}
            error={errors.serviceName}
          />

          <DynamicDropdown
            title={AppText.CHOOSE_CATEGORY}
            placeholder={AppText.ENTER_YOUR_CATEGORY}
            value={formData.category}
            onChange={value => {
              handleChange('category', value);
              handleChange('subCategory', null); // Reset subcategory on category change
            }}
            fetchData={get_all_category}
            error={errors.category}
          />

          <DynamicDropdown
            title={AppText.CHOOSE_SUB_CATEGORY}
            placeholder={AppText.ENTER_YOUR_SUB_CATEGORY}
            value={formData.subCategory}
            onChange={value => handleChange('subCategory', value)}
            fetchData={get_sub_category}
            dependentOn={formData.category}
            dependencyAlertMessage={'Please select a category first'}
            error={errors.subCategory}
          />

          <View style={styles.descriptionContainer}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, hp(1), 0]}>
              {AppText.DESCRIPTION}
            </ResponsiveText>
            <TextInput
              style={[
                styles.descriptionInput,
                {
                  color: getTextColor(),
                  borderColor: errors.description
                    ? colors.red
                    : getDark_Theme(),
                  backgroundColor: backgroundColor,
                },
              ]}
              placeholder={AppText.ENTER_DESCRIPTION}
              placeholderTextColor={colors.grey}
              value={formData.description}
              onChangeText={value => handleChange('description', value)}
              multiline
              numberOfLines={4}
            />
            {errors.description && (
              <ResponsiveText
                color={colors.red}
                size={3}
                margin={[hp(0.5), 0, 0, 0]}>
                {errors.description}
              </ResponsiveText>
            )}
          </View>

          <DurationSelector
            value={formData.duration}
            onChange={value => handleChange('duration', value)}
            error={errors.duration}
          />

          <CustomTextInput
            label={AppText.SET_PRICE}
            placeholder={AppText.ENTER_YOUR_PRICE}
            value={formData.price}
            onChangeText={value => handleChange('price', value)}
            keyboardType="numeric"
            error={errors.price}
          />

             {/* Color Picker Section */}
          <View style={styles.colorPickerSection}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="500"
              margin={[0, 0, hp(1), 0]}>
              {AppText.CHOOSE_COLOR || 'Choose Color'}
            </ResponsiveText>
            <View style={styles.colorPickerRow}>
              <TouchableOpacity
                style={[
                  styles.colorPreview,
                  {
                    backgroundColor: formData.color || 'transparent',
                    borderStyle: formData.color ? 'solid' : 'dashed'
                  }
                ]}
                onPress={() => setColorPickerVisible(true)}
              />
              <ResponsiveText
                color={getTextColor()}
                size={3.5}
                margin={[0, 0, 0, wp(2)]}>
                {formData.color || AppText.NO_COLOR_SELECTED || 'No color selected'}
              </ResponsiveText>
            </View>
            {errors.color && (
              <ResponsiveText
                color={colors.red}
                size={3}
                margin={[hp(0.5), 0, 0, 0]}>
                {errors.color}
              </ResponsiveText>
            )}
          </View>


          <ServiceImageUploadCard onImageSelect={handleImageSelect} />

       
          <TouchableOpacity
            style={[
              styles.addButton,
              {
                backgroundColor: isFormValid()
                  ? colors.Light_theme_maincolour
                  : colors.grey,
                opacity: isFormValid() ? 1 : 0.5,
              },
            ]}
            disabled={!isFormValid() || loading} // 🔥 Disable button when loading
            onPress={handleAddService}>
            {/* {loading ? (
              <ActivityIndicator size="small" color={colors.white} /> // 🔥 Show loader when loading
            ) : ( */}
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.ADD_SERVICE}
            </ResponsiveText>
            {/* )} */}
          </TouchableOpacity>

          {/* Color Picker Modal */}
          <ColorPickerModal
            visible={colorPickerVisible}
            onClose={() => setColorPickerVisible(false)}
            onSelectColor={handleColorSelect}
            initialColor={formData.color}
          />
        </ScrollView>
      </KeyboardAvoidingView>
      {loading ? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default Add_Service;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: hp(2),
  },
  descriptionContainer: {
    marginBottom: hp(2),
  },
  descriptionInput: {
    height: hp(12),
    borderWidth: 1,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    paddingTop: wp(4),
    fontSize: wp(3.8),
    textAlignVertical: 'top',
  },
  colorPickerSection: {
    marginVertical: hp(2),
  },
  colorPickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorPreview: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    borderWidth: 1,
    borderColor: colors.grey,
  },
  addButton: {
    marginTop: hp(2),
    marginBottom: hp(4),
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
