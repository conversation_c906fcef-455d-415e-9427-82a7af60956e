import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Alert,
} from 'react-native';
import React, {useState, useEffect, useCallback} from 'react';
import useTheme from '../../../Redux/useTheme';
import CustomHeader from '../../../Components/CustomHeader';
import useAppText from '../../../Custom/AppText';
import CustomTextInput from '../../../Components/CustomTextInput';
import WeekDayCard from '../../../Components/Salons/WeekDayCard';
import TimePickerModal from '../../../Components/Salons/TimePickerModal';
import AddServiceModal from '../../../Components/Salons/AddServiceModal';
import SelectedServiceCard from '../../../Components/Salons/SelectedServiceCard';
import ImageUploadCard from '../../../Components/Salons/ImageUploadCard';
import {hp, wp} from '../../../Custom/Responsiveness';
import ResponsiveText from '../../../Custom/RnText';
import {colors} from '../../../Custom/Colors';
import Icon from '../../../Custom/Icon';
import {globalpath} from '../../../Custom/globalpath';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import AssignBarberModal from '../../../Components/Salons/AssignBarberModal';
import MainCard from '../../../Components/MainCard';
import {
  Add_shop,
  shop_to_service,
  service_to_barber,
} from '../../../Services/API/Endpoints/Admin/Salons';
import {
  get_all_barber,
  update_barber,
} from '../../../Services/API/Endpoints/Admin/Barber';
import Loader from '../../../Custom/loader';

const Add_Salons = ({}) => {
  const navigation = useNavigation();
  const {backgroundColor, getDark_Theme, getTextColor} = useTheme();
  const AppText = useAppText();
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [selectedDay, setSelectedDay] = useState(null);
  const [weekDays, setWeekDays] = useState([]);
  const [selectedServices, setSelectedServices] = useState([]);
  const [allbaber, setAllbabers] = useState([]);
  const [showBarberModal, setShowBarberModal] = useState(false);
  const [selectedServiceForBarber, setSelectedServiceForBarber] =
    useState(null);
  const [loading, setLoading] = useState(false);
  const [ShopID, setShopID] = useState(null);

  const [formData, setFormData] = useState({
    salonName: '',
    streetAddress: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    working_hours: [
      {day_of_week: 0, open_time: '00:00:00', close_time: '00:00:00'}, // Sun
      {day_of_week: 1, open_time: '00:00:00', close_time: '00:00:00'}, // Mon
      {day_of_week: 2, open_time: '00:00:00', close_time: '00:00:00'}, // Tue
      {day_of_week: 3, open_time: '00:00:00', close_time: '00:00:00'}, // Wed
      {day_of_week: 4, open_time: '00:00:00', close_time: '00:00:00'}, // Thu
      {day_of_week: 5, open_time: '00:00:00', close_time: '00:00:00'}, // Fri
      {day_of_week: 6, open_time: '00:00:00', close_time: '00:00:00'}, // Sat
    ],
  });
  console.log('formData', formData);
  console.log('working_hours', formData.working_hours);
  const [errors, setErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    fetch_all_barbers();
  }, []);

  const fetch_all_barbers = async () => {
    try {
      setLoading(true);

      const response = await get_all_barber();
      console.log('🟢 All barbers fetched:', response);

      if (Array.isArray(response)) {
        const sortedServices = response.sort((a, b) => b.id - a.id);
        setAllbabers(sortedServices);
      }
    } catch (error) {
      console.error('❌ Failed to fetch services:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = imageDetails => {
    setSelectedImage(imageDetails);
    if (imageDetails) {
      console.log('Selected Salon Image Details:', {
        name: imageDetails.fileName,
        size: imageDetails.size + ' MB',
        dimensions: imageDetails.dimensions,
        uri: imageDetails.uri,
      });
    } else {
      console.log('Salon Image removed');
    }
  };

  const isFormValid = () => {
    return (
      formData.salonName.trim() !== '' ||
      formData.streetAddress.trim() !== '' ||
      formData.city.trim() !== '' ||
      formData.postalCode.trim() !== '' ||
      formData.country.trim() !== '' ||
      formData.working_hours.length > 0 ||
      selectedServices.length > 0
    );
  };

  useEffect(() => {
    generateWeekDays();
  }, []);

  const generateWeekDays = () => {
    const days = [
      AppText.MON,
      AppText.TUE,
      AppText.WED,
      AppText.THU,
      AppText.FRI,
      AppText.SAT,
      AppText.SUN,
    ];

    const weekDaysData = days.map(day => ({
      day,
      isSelected: false,
      selectedTime: null,
    }));
    setWeekDays(weekDaysData);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDayPress = index => {
    setSelectedDay(index);
    setShowTimePicker(true);
  };

  const formatTo24Hour = date => {
    if (!(date instanceof Date)) return '00:00:00';
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}:00`;
  };

  const handleTimeSave = (startTime, endTime) => {
    // ✅ 1. For UI display
    const displayStart = startTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const displayEnd = endTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const displayString = `${displayStart} - ${displayEnd}`;

    // ✅ 2. For backend data (24-hour format)
    const formattedStart = formatTo24Hour(startTime);
    const formattedEnd = formatTo24Hour(endTime);

    // ✅ Update UI for selected day
    setWeekDays(prev => {
      const updated = [...prev];
      updated[selectedDay] = {
        ...updated[selectedDay],
        isSelected: true,
        selectedTime: displayString,
      };
      return updated;
    });

    // Update working_hours in formData
    const dayName = weekDays[selectedDay].day;
    const dayOfWeek = dayMap[dayName];

    setFormData(prev => ({
      ...prev,
      working_hours: prev.working_hours.map(hour =>
        hour.day_of_week === dayOfWeek
          ? {
              ...hour,
              open_time: formatTime(formattedStart),
              close_time: formatTime(formattedEnd),
            }
          : hour,
      ),
    }));
  };

  const handleServiceSelect = services => {
    console.log('Selected Services:', services);
    setSelectedServices(prev => [...prev, ...services]);
  };

  const handleDeleteService = serviceId => {
    setSelectedServices(prev =>
      prev.filter(service => service.id !== serviceId),
    );
  };

  const handleDeleteBarber = (serviceId, barber) => {
    setSelectedServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? {
              ...service,
              assignedBarbers: service.assignedBarbers.filter(
                b => b.id !== barber.id,
              ),
            }
          : service,
      ),
    );
  };

  const handleAssignBarber = serviceId => {
    const service = selectedServices.find(s => s.id === serviceId);
    setSelectedServiceForBarber(serviceId);
    setShowBarberModal(true);
  };

  const handleBarberSelect = barbers => {
    if (selectedServiceForBarber) {
      setSelectedServices(prev =>
        prev.map(service =>
          service.id === selectedServiceForBarber
            ? {
                ...service,
                assignedBarbers: [
                  ...(service.assignedBarbers || []),
                  ...barbers,
                ],
              }
            : service,
        ),
      );
    }
    setShowBarberModal(false);
  };

  const dayMap = {
    [AppText.MON]: 0,
    [AppText.TUE]: 1,
    [AppText.WED]: 2,
    [AppText.THU]: 3,
    [AppText.FRI]: 4,
    [AppText.SAT]: 5,
    [AppText.SUN]: 6,
  };

  const formatTime = timeStr => {
    try {
      // Split the time string into time and AM/PM
      const [time, modifier] = timeStr.trim().split(' ');
      let [hours, minutes] = time.split(':').map(num => parseInt(num, 10));

      // Handle AM/PM conversion
      if (modifier === 'PM' && hours !== 12) {
        hours += 12;
      } else if (modifier === 'AM' && hours === 12) {
        hours = 0;
      }

      // Format hours and minutes with leading zeros
      const formattedHours = hours.toString().padStart(2, '0');
      const formattedMinutes = minutes.toString().padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}:00`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return '';
    }
  };

  const linkshop_to_barber = async (barberId, shopId) => {
    console.log(
      'Linking Barber to Shop: Barber ID:',
      barberId,
      'Shop ID:',
      shopId,
    );

    try {
      const payload = {
        shop: shopId,
        barber: barberId, // Assigning the specific barber ID
      };

      console.log('🚀 Submitting barber-shop link payload:', payload);

      // Call the API to update barber details for the shop
      const response = await update_barber(barberId, payload); // Assuming update_barber is the API function
      console.log('✅ Barber linked to shop successfully:', response);
    } catch (error) {
      console.error(
        '❌ Error in linkshop_to_barber:',
        error?.response?.data || error,
      );
      // Optional: Alert or handle individual barber error
      Alert.alert(
        'Error',
        `Failed to link Barber ID ${barberId} to shop: ${error.message}`,
      );
    }
  };

  const executeService = async shopId => {
    console.log('wwwww', shopId);
    try {
      if (!shopId) {
        throw new Error('Shop ID is required.');
      }

      console.log('selectedServices payload:', selectedServices);

      // Ensure selectedServices are not empty
      if (!selectedServices || selectedServices.length === 0) {
        throw new Error('No services selected.');
      }

      const serviceIds = selectedServices.map(service => service.id); // ✅ No .toString()

      const payload = {
        shop: shopId,
        service: serviceIds,
      };

      console.log('🚀 Submitting service assignment payload:', payload);

      // Make the API call to assign services
      const response = await shop_to_service(payload); // ✅ Call API
      console.log('✅ shop_to_service response:', response);

      // Handle response if needed
      if (response && response.data) {
        console.log('Success:', response.data);
      } else {
        throw new Error('Invalid response from API.');
      }
    } catch (error) {
      console.error(
        '❌ Error in executeService:',
        error?.response?.data || error.message || error,
      );
      // You can also display an alert or show a notification to the user if needed
      // Alert.alert('Error', error.message || 'An unexpected error occurred.');
    }
  };

  const generateServiceBarberPayload = async (shopid) => {
    try {
      // Step 1: Generate Payload (add `shop: shopid`)
      const payload = selectedServices
        .filter(
          service =>
            service.assignedBarbers && service.assignedBarbers.length > 0,
        )
        .map(service => ({
          service: service.id,
          barber: service.assignedBarbers.map(barber => barber.id),
          shop: shopid, // <-- Add this line
        }));
  
      console.log('🧾 Final Service-Barber Payload:', payload);
  
      // Step 2: Extract all unique barber IDs
      const allBarbers = new Set();
  
      payload.forEach(item => {
        item.barber.forEach(barberId => {
          allBarbers.add(barberId);
        });
      });
  
      const uniqueBarbers = Array.from(allBarbers);
      console.log('Unique Barber IDs:', uniqueBarbers);
  
      // Step 3: Send API requests for each payload item
      for (const item of payload) {
        try {
          console.log('📦 Sending payload:', item);
          const response = await service_to_barber(item);
          console.log(`✅ Service ${item.service} assigned:`, response);
        } catch (error) {
          console.error(`❌ Failed for service ${item.service}:`, error?.response?.data || error);
        }
      }
  
      return uniqueBarbers;
    } catch (error) {
      console.error('❌ Error generating Service-Barber Payload:', error);
      throw new Error('Error generating Service-Barber Payload');
    }
  };
  

  const handleAddSalon = async () => {
    if (isFormValid()) {
      setLoading(true);

      try {
        const formDataPayload = new FormData();

        formDataPayload.append('name', formData.salonName.trim());
        formDataPayload.append('address_street', formData.streetAddress.trim());
        formDataPayload.append('address_city', formData.city.trim());
        formDataPayload.append('address_state', formData.state.trim());
        formDataPayload.append(
          'address_postal_code',
          formData.postalCode.trim(),
        );
        formDataPayload.append('address_country', formData.country.trim());

        // Append working_hours as JSON string
        formDataPayload.append(
          'working_hours',
          JSON.stringify(formData.working_hours),
        );

        // Append selected services as JSON string
        formDataPayload.append(
          'services',
          JSON.stringify(
            selectedServices.map(service => ({
              id: service.id,
            })),
          ),
        );

        // Append image if selected
        if (selectedImage?.uri) {
          const image = {
            uri: selectedImage.uri,
            name: selectedImage.fileName || 'salon_image.jpg',
            type: 'image/jpeg', // adjust type if needed
          };
          formDataPayload.append('shop_image', image);
        }

        console.log('🧾 Submitting FormData payload:', formDataPayload);

        const response = await Add_shop(formDataPayload);
        // // console.log("Add Saloon Response is ", response)
        console.log('Add Saloon Response is ', response.id);
        //  Step 2: Execute service assignment

        await executeService(response.id);

        // Step 3: Generate service-barber payload

        const all_barber_id = await generateServiceBarberPayload(response.id);
        console.log(
          'Successfully generated Service-Barber Payload with unique barbers:',
          all_barber_id,
        );

        // Now, call linkshop_to_barber for each barber
        for (const barberId of all_barber_id) {
          try {
            await linkshop_to_barber(barberId, response.id); // Assuming response.id is your shopId
          } catch (error) {
            console.error(
              `❌ Error linking barber ${barberId} to shop:`,
              error,
            );
          }
        }

        // If all steps succeed, navigate back
        navigation.goBack();
        Alert.alert('Success', 'Salon added successfully!');
      } catch (error) {
        console.error('❌ Error while submitting salon:', error);
      
        const errorData = error?.response?.data;
        let message = 'Failed to add salon. Please try again.';
      
        if (errorData && typeof errorData === 'object') {
          const messages = Object.entries(errorData).flatMap(([key, value]) =>
            Array.isArray(value) ? value : [value]
          );
          message = messages.join('\n');
        }
      
        Alert.alert('Error', message);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <CustomHeader title={AppText.ADD_SALONS} leftIconType="back" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}>
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}>
          <CustomTextInput
            label={AppText.SALON_NAME}
            placeholder={AppText.ENTER_SALON_NAME}
            value={formData.salonName}
            onChangeText={value => handleChange('salonName', value)}
          />

          <CustomTextInput
            label={AppText.STREET_ADDRESS}
            placeholder={AppText.ENTER_STREET}
            value={formData.streetAddress}
            onChangeText={value => handleChange('streetAddress', value)}
          />

          <CustomTextInput
            label={AppText.CITY}
            placeholder={AppText.ENTER_CITY}
            value={formData.city}
            onChangeText={value => handleChange('city', value)}
          />

          <CustomTextInput
            label={AppText.STATE}
            placeholder={AppText.UNITED_STATES}
            value={formData.state}
            onChangeText={value => handleChange('state', value)}

            // editable={false}
          />

          <CustomTextInput
            label={AppText.POSTAL_CODE}
            placeholder={AppText.ENTER_POSTAL_CODE}
            value={formData.postalCode}
            onChangeText={value => handleChange('postalCode', value)}
            keyboardType="number-pad"
          />

          <CustomTextInput
            label={AppText.COUNTRY}
            placeholder={AppText.ENTER_COUNTRY}
            value={formData.country}
            onChangeText={value => handleChange('country', value)}
          />

          <View>
            <ResponsiveText
              color={getTextColor()}
              size={4.6}
              weight="bold"
              margin={[hp(0), 0, hp(1), 0]}>
              {AppText.SET_DATE_TIME}
            </ResponsiveText>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.weekDaysContainer}>
              {weekDays.map((day, index) => (
                <View key={index} style={styles.dayContainer}>
                  <WeekDayCard
                    day={day.day}
                    isSelected={day.isSelected}
                    selectedTime={day.selectedTime}
                    onPress={() => handleDayPress(index)}
                  />
                  <View style={styles.timeItem}>
                    {day.selectedTime ? (
                      <ResponsiveText
                        color={colors.Light_theme_maincolour}
                        size={3}
                        weight="500">
                        {day.selectedTime}
                      </ResponsiveText>
                    ) : (
                      <ResponsiveText
                        color={getTextColor()}
                        size={3}
                        weight="500">
                        -
                      </ResponsiveText>
                    )}
                  </View>
                </View>
              ))}
            </ScrollView>
            {/* <View style={[styles.bottomLine,{borderColor:getDark_Theme()}]} /> */}
          </View>

          <View style={styles.servicesSection}>
            <View style={styles.servicesHeader}>
              <ResponsiveText color={getTextColor()} size={4.6} weight="bold">
                {AppText.ADD_SERVICE}
              </ResponsiveText>
              <TouchableOpacity onPress={() => setShowServiceModal(true)}>
                <Icon source={globalpath.add_service} size={wp(8)} />
              </TouchableOpacity>
            </View>
          </View>
          {selectedServices.map(service => (
            <SelectedServiceCard
              key={service.id}
              service={service}
              onAssignBarber={() => handleAssignBarber(service.id)}
              onDelete={() => handleDeleteService(service.id)}
              from={'Add'}
              // onDeleteBarber={handleDeleteBarber}
            />
          ))}

          <ImageUploadCard onImageSelect={handleImageSelect} />

          <TouchableOpacity
            style={[
              styles.addButton,
              {
                backgroundColor: isFormValid()
                  ? colors.Light_theme_maincolour
                  : colors.grey,
                opacity: isFormValid() ? 1 : 0.5,
              },
            ]}
            disabled={!isFormValid()}
            onPress={
              handleAddSalon
              // Handle add salon logic here
              // console.log('Add salon pressed');
            }>
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.ADD_SALONS}
            </ResponsiveText>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      <TimePickerModal
        visible={showTimePicker}
        onClose={() => setShowTimePicker(false)}
        onSave={handleTimeSave}
      />

      <AddServiceModal
        visible={showServiceModal}
        onClose={() => setShowServiceModal(false)}
        onServiceSelect={handleServiceSelect}
        previouslySelectedServices={selectedServices}
        from={'Add'}
      />

      <AssignBarberModal
        visible={showBarberModal}
        onClose={() => setShowBarberModal(false)}
        onBarberSelect={handleBarberSelect}
        barberdetails={allbaber}
        from={'Add'}
        previouslySelectedBarbers={
          selectedServices.find(s => s.id === selectedServiceForBarber)
            ?.assignedBarbers || []
        }
      />
      {loading ? <Loader /> : undefined}
    </SafeAreaView>
  );
};

export default Add_Salons;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: hp(2),
  },
  weekDaysContainer: {
    paddingVertical: hp(1),
  },
  dayContainer: {
    alignItems: 'center',
    marginHorizontal: wp(1),
  },
  timeItem: {
    marginTop: hp(1),
    alignItems: 'center',
  },
  bottomLine: {
    borderBottomWidth: 1,
    marginHorizontal: wp(-4),
  },
  servicesSection: {
    marginTop: hp(1),
    marginHorizontal: wp(1),
    // backgroundColor:"pink",
    marginBottom: hp(2),
  },
  servicesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: hp(2),
  },
  addButton: {
    marginTop: hp(2),
    marginBottom: hp(4),
    padding: hp(2),
    borderRadius: wp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
