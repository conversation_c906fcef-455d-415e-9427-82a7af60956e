import React from 'react';
import {StyleSheet, SafeAreaView} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import CustomHeader from '../../Components/CustomHeader';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import {colors} from '../../Custom/Colors';
import Services_tab from './Services/Services_tab';
import Category_Tab from './Services/Category_Tab';

const Tab = createMaterialTopTabNavigator();

const Service = () => {
  const AppText = useAppText();
  const {backgroundColor} = useTheme();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor}]}>
      <CustomHeader title={AppText.SERVICE} leftIconType="back" />

      <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: colors.Light_theme_maincolour,
          tabBarInactiveTintColor: colors.grey,
          tabBarPressColor: 'transparent',
          tabBarShowIcon: false,
          tabBarLabelStyle: {
            textTransform: 'none',
            fontSize: 14,
            fontWeight: '500',
          },
        }}>
        <Tab.Screen
          name="Services"
          component={Services_tab}
          options={{
            tabBarLabel: AppText.SERVICES,
          }}
        />
        <Tab.Screen
          name="Category"
          component={Category_Tab}
          options={{
            tabBarLabel: AppText.CATEGORY,
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  );
};

export default Service;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    backgroundColor: 'transparent',
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey,
  },
  tabIndicator: {
    backgroundColor: colors.Light_theme_maincolour,
    height: 2,
  },
  tabLabel: {
    textTransform: 'none',
    fontSize: 14,
    fontWeight: '500',
  },
});
