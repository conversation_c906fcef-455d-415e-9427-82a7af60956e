import React, { useState , useCallback , useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  SafeAreaView, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,

} from 'react-native';
import CustomHeader from '../../Components/CustomHeader';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import Icon from '../../Custom/Icon';
import { globalpath } from '../../Custom/globalpath';
import ResponsiveText from '../../Custom/RnText';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import { useNavigation , useFocusEffect } from '@react-navigation/native';
import ProductGrid from '../../Components/Products/ProductGrid';
import { Product_mock } from '../../Mocks/Product_mock';
import Add_Product_Modal from '../../Components/Products/Add_Product_Modal';
import { list_product_category } from '../../Services/API/Endpoints/Admin/products';


const Products = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filteredServices, setFilteredServices] = useState([]);
  const [product_categories, setProduct_categories] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);


  const AppText = useAppText();
  
  


  const { 
    themeColor,
    getDarK_mode_LightGrayBackground, 
    backgroundColor, 
    getTextColor,
    getSecondaryTextColor,
    getLightGrayBackground,
    getborderTextColor,
    getBellBackground,
    getDark_Theme
  } = useTheme();
  const navigation = useNavigation();

  const onRefresh = useCallback(() => {
    fetchProductsCategories();
      handleSearch('');
    }, []);


   useFocusEffect(
      useCallback(() => {
        fetchProductsCategories();
      }, []),
    );

     const fetchProductsCategories = async () => {
        try {
          setLoading(true);
          setRefreshing(true);
    
          const response = await list_product_category();
          console.log('🟢 Products Categories fetched:', response);
    
          if (Array.isArray(response)) {
            const sortedServices = response.sort((a, b) => b.id - a.id);
            setProduct_categories(sortedServices);
            setFilteredServices(sortedServices);
          }
        } catch (error) {
          console.error('❌ Failed to fetch services:', error);
        } finally {
          setLoading(false);
          setRefreshing(false);
        }
      };



     
      
      const handleSearch = (text) => {
        setSearchText(text);
        if (text) {
          const filtered = product_categories.filter((service) =>
            service.name.toLowerCase().includes(text.toLowerCase())
          );
          setFilteredServices(filtered);
        } else {
          setFilteredServices(product_categories);
        }
      };
      

  
  const handleProductPress = (product) => {
    // Handle product press
    console.log('Product pressed:', product);
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setSelectedProduct(null);
  };

  const handleModalSave = () => {
    fetchProductsCategories();
    handleModalClose();
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsModalVisible(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setIsModalVisible(true);
  };

  
  return (
    <SafeAreaView style={[styles.container,{backgroundColor: backgroundColor}]}>
      <CustomHeader title={AppText.FIND_CATEGORIES} showBellIcon={false} leftIconType="back" />
         {/* <View style={styles.content}> */}
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : ''}
            style={{flex: 1}}>  
            <View style={styles.content}>
              <View style={[styles.searchRow]}>
                <View style={[styles.searchContainer, {borderColor: getDark_Theme()}]}>
                  <Icon source={globalpath.search} size={20} tintColor={getTextColor()} margin={[0,wp(2),0,0]} />
                  <TextInput
                  style={[styles.searchInput, { color: getTextColor() }]}
                  placeholder={AppText.SEARCH_PRODUCTS}
                  placeholderTextColor={colors.grey}
                  value={searchText}
                  onChangeText={handleSearch}
                />
                
                </View>
                <TouchableOpacity 
                  style={[styles.addButton, {backgroundColor: colors.Light_theme_maincolour}]}
                onPress={handleAddProduct}


                >
                  <ResponsiveText color={colors.white}>{AppText.ADD_CATEGORY}</ResponsiveText>
                </TouchableOpacity>
              </View>
              
            
              {loading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color={colors.Light_theme_maincolour} />
              </View>
            ): filteredServices.length === 0 ? (
              <View style={styles.noDataContainer}>
                <ResponsiveText color={colors.black}>{AppText.NO_CATEGORIES_RECORD}</ResponsiveText>
              
              </View>
            ) : (
       
              <ProductGrid data={filteredServices} onProductPress={handleProductPress}   refreshing={refreshing}
              onRefresh={onRefresh} onEditPress={handleEditProduct} />
  
            )}
              
            
            </View>
          </KeyboardAvoidingView>
        <Add_Product_Modal
          visible={isModalVisible}
          onClose={handleModalClose}
          onSave={handleModalSave}
          productToEdit={selectedProduct}
        />
      {/* </View> */}
    </SafeAreaView>
  );
};

export default Products;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(2.5)
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  addButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(1.5),
    marginLeft: wp(3)
  },
  gridContainer: {
    flex: 1,
  },
  loaderContainer:{
    flex:1,
    justifyContent:'center',
    alignItems:'center'
  },
  noDataContainer:{
    flex:1,
    justifyContent:'center',
    alignItems:'center'
  }
}); 