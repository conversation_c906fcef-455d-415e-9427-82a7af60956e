import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import React, { useCallback, useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  SafeAreaView, 
  TextInput, 
  TouchableOpacity, 
  FlatList, 
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import CustomHeader from '../../Components/CustomHeader';
import useAppText from '../../Custom/AppText';
import useTheme from '../../Redux/useTheme';
import { colors } from '../../Custom/Colors';
import GiftCardsTab from './Gift_Card/GiftCardsTab';
import OthersTab from './Gift_Card/OthersTab';


const Tab = createMaterialTopTabNavigator();

const GiftCards = () => {
  const AppText = useAppText();
  const { backgroundColor } = useTheme();

  const [allcardData, setAllCradData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filteredShops, setFilteredShops] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);


  


  const handleGiftCardPress = (giftCard) => {
    navigation.navigate('View_Gift_Card', { giftCard });
  };

  const renderGiftCardItem = ({ item }) => (
    <GiftCard
      item={item}
      onPress={() => handleGiftCardPress(item)}
    />
  );
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.GIFT_CARDS} leftIconType="back" />
      
      <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: colors.Light_theme_maincolour,
          tabBarInactiveTintColor: colors.grey,
          tabBarPressColor: 'transparent',
          tabBarShowIcon: false,
          tabBarLabelStyle: {
            textTransform: 'none',
            fontSize: 14,
            fontWeight: '500',
          },
        }}
      >
        <Tab.Screen 
          name="GiftCards" 
          component={GiftCardsTab}
          options={{
            tabBarLabel: AppText.GIFT_CARDS
          }}
        />
        <Tab.Screen 
          name="Others" 
          component={OthersTab}
          options={{
            tabBarLabel: AppText.OTHERS
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  );
};

export default GiftCards;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    backgroundColor: 'transparent',
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey,
  },
  tabIndicator: {
    backgroundColor: colors.Light_theme_maincolour,
    height: 2,
  },
  tabLabel: {
    textTransform: 'none',
    fontSize: 14,
    fontWeight: '500',
  },
}); 