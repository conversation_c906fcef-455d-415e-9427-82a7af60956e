import React, { useEffect } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { colors } from '../Custom/Colors';
import { wp, hp } from '../Custom/Responsiveness';
import { globalpath } from '../Custom/globalpath';
import { useNavigation } from '@react-navigation/native';
import ResponsiveText from '../Custom/RnText';
import { isTablet } from '../Custom/TabResoponsive/TabResponsive';
import useTheme from '../Redux/useTheme';
import useAppText from '../Custom/AppText';

const { width, height } = Dimensions.get('window');

const Splash = () => {
  const navigation = useNavigation();
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor,getDark_Theme ,get_Font_text_color} = useTheme();
  const AppText = useAppText();

  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.replace('Welcome');
    }, 4000);

    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <Animatable.View 
        animation="zoomIn"
        duration={1000}
        style={styles.logoContainer}
      >
        <Animatable.Image
          source={globalpath.logo}
          style={styles.logo}
          animation="pulse"
          // iterationCount="infinite"
          duration={2000}
          resizeMode="contain"
        />
      </Animatable.View>

      <Animatable.View 
        style={styles.bottomContainer}
        animation="fadeInUp"
        delay={1000}
        duration={1500}
      >
        {/* <Animatable.View 
          style={styles.welcomeText}
          animation="fadeIn"
          delay={2000}
          
        >
              <ResponsiveText 
        weight={'500'} 
        size={ isTablet?5:7} 
        margin={isTablet? [hp(-0),0,hp(2),0]:[hp(-1),0,wp(5),0]} 
        color={colors.white}
        fontFamily='PlayfairDisplay'
        >
          {AppText.WELCOME_TITLE}
        </ResponsiveText>

        </Animatable.View> */}
      </Animatable.View>
    </View>
  );
};

export default Splash;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.Light_theme_maincolour,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  logo: {
    width: wp(60),
    height: wp(60),
    tintColor: colors.white,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: hp(10),
    alignItems: 'center',
  },
  welcomeText: {
    color: colors.white,
    fontSize: wp(6),
    fontWeight: '600',
    textAlign: 'center',
  },
});