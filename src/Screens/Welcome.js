import { SafeAreaView, StyleSheet, View, ImageBackground, TouchableOpacity, Image, StatusBar, Switch, Text } from 'react-native';
import React, { useEffect } from 'react';
import { colors } from '../Custom/Colors';
import { globalpath } from '../Custom/globalpath';
import { hp, wp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import useAppText from '../Custom/AppText';
import useTheme from '../Redux/useTheme';
import { useDispatch } from 'react-redux';
import { setSelectedTheme, setThemeColor, setBackgroundColor } from '../Redux/Slices/themeSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';


import Fonts from '../Custom/Fonts';
import { isTablet } from '../Custom/TabResoponsive/TabResponsive';
const Welcome = ({ navigation }) => {
  const dispatch = useDispatch();
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor, selectedTheme, getborderTextColor,getDark_Theme ,get_Font_text_color} = useTheme();
  const AppText = useAppText();

  useEffect(()=>{
    getHeaders();
    
  },[])

  // Function to handle Login button press
  const handleLogin = () => {
    navigation.navigate('Login');
  };

  // Function to handle Proceed as Guest button press
  const handleGuest = () => {
    // navigation.navigate('Guest');
  };

  // Function to handle Register button press
  const handleRegister = () => {
    navigation.navigate('Register');
  };

  const toggleSwitch = () => {
    const newTheme = selectedTheme === 'light' ? 'dark' : 'light';
    dispatch(setSelectedTheme(newTheme));
    dispatch(setThemeColor(newTheme === 'dark' ? colors.Light_theme_maincolour : colors.black));
    dispatch(setBackgroundColor(newTheme === 'dark' ? colors.black : colors.white));
  };

  // Get background image based on theme
  const getBackgroundImage = () => {
    return selectedTheme === 'dark' ? globalpath.dark_image : globalpath.BackImage;
  };



  const getHeaders = async () => {
     // ✅ Remove session cookie
     await AsyncStorage.removeItem('sessionCookie');
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsed = JSON.parse(userData);
        if (parsed.token) {
          console.log('🔐 User is logged in, redirecting to AppStack...');
          navigation.replace('AppStack');
          return;
        }
      }
      console.log('🚫 No user data found.');
    } catch (error) {
      console.error('❌ Error checking user session:', error);
    }
  
  };

  return (
    <View style={[styles.container, { backgroundColor: backgroundColor }]}>
      <StatusBar translucent backgroundColor="transparent" />
      <View style={styles.topHalf}>
        <ImageBackground
          source={getBackgroundImage()}
          style={styles.backgroundImage}
          resizeMode={isTablet?'contain':null}
        >
          <View style={styles.logoContainer}>
            <Image
              source={globalpath.logo}
              style={[styles.logo, {tintColor: themeColor}]}
              resizeMode='contain'
            />
          </View>
        </ImageBackground>
      </View>

      <View style={styles.bottomHalf}>
      <ResponsiveText 
        weight={'500'} 
        size={ isTablet?5:7} 
        margin={isTablet? [hp(-0),0,hp(2),0]:[hp(-1),0,wp(5),0]} 
        color={get_Font_text_color()}
        fontFamily='PlayfairDisplay'
        >
          {AppText.WELCOME_TITLE}
        </ResponsiveText>





        <TouchableOpacity 
          style={[styles.button, { backgroundColor: colors.Light_theme_maincolour }]} 
          onPress={handleLogin}
        >
          <ResponsiveText color={colors.white} weight={'600'} size={ isTablet?3.2:4}>
            {AppText.LOGIN_BUTTON}
          </ResponsiveText>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, { backgroundColor: colors.c_green }]} 
          onPress={handleGuest}
        >
          <ResponsiveText color={colors.white} weight={'600'} size={ isTablet?3.2:4}>
            {AppText.GUEST_BUTTON}
          </ResponsiveText>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.registerButton, { borderColor: getDark_Theme() }]} 
          onPress={handleRegister}
        >
          <ResponsiveText color={getborderTextColor()} weight={'600'} size={ isTablet?3.2:4}>
            {AppText.REGISTER_BUTTON}
          </ResponsiveText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topHalf: {
    height:  isTablet?hp(70): "57%",
    width:wp(100),
    marginTop: isTablet?hp(-1):null
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  logoContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginBottom: isTablet?hp(10):null,
    // backgroundColor:"red"
  },
  logo: {
    width: isTablet?wp(25): wp(33),
    height: isTablet?wp(25): wp(33),
  },
  bottomHalf: {
    flex: 1,
    // backgroundColor:"red",
    padding: isTablet?wp(2.5): wp(4.5),
    justifyContent:  isTablet? null:'center',
    alignItems: 'center',
    marginTop: isTablet? hp(-5):null
  },
  button: {
    width: '100%',
    padding: isTablet?wp(2.5):wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: isTablet?wp(2.5):wp(4)
  },
  registerButton: {
    width: '100%',
    padding: isTablet?wp(2.5):wp(4.5),
    borderRadius: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  themeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1),
    borderRadius: wp(2),
    marginBottom: wp(4),
    width: '100%',
  }
});