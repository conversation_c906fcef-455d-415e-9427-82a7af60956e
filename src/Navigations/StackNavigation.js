import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

// Screens
import Login from '../Screens/Login';
import Welcome from '../Screens/Welcome';
import Register from '../Screens/Register';
import Email_Verfication from '../Screens/Email_Verfication';
import Set_Password from '../Screens/Set_Password';
import ForgotPassword from '../Screens/ForgotPassword';
import OTPVerification from '../Screens/OTPVerification';
import ResetPassword from '../Screens/ResetPassword';
import ForgotPasswordOTP from '../Screens/ForgotPasswordOTP';
import FontTest from '../Screens/FontTest';
import Splash from '../Screens/Splash';

// Create Stack Navigator
const Stack = createNativeStackNavigator();

const AuthStack = () => {
  return (
    <Stack.Navigator 
    initialRouteName='Splash'
      screenOptions={{
        headerShown: false,
      }}
    >
        <Stack.Screen
        name="Welcome"
        component={Welcome}
      />
      <Stack.Screen
        name="Login"
        component={Login}
      />
      <Stack.Screen
        name="ForgotPassword"
        component={ForgotPassword}
      />
      <Stack.Screen
        name="ForgotPasswordOTP"
        component={ForgotPasswordOTP}
      />
      <Stack.Screen
        name="ResetPassword"
        component={ResetPassword}
      />
       <Stack.Screen
        name="Register"
        component={Register}
      />
       <Stack.Screen
        name="Email Verification"
        component={Email_Verfication}
      />
       <Stack.Screen
        name="Set Password"
        component={Set_Password}
      />
      <Stack.Screen
        name="FontTest"
        component={FontTest}
      />
       <Stack.Screen
        name="Splash"
        component={Splash}
      />
      
      {/* <Stack.Screen
        name="DrawerNavigator"
        component={DrawerNavigator}
      /> */}
    </Stack.Navigator>
  );
};

export default AuthStack;