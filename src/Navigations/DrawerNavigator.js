import React, {useState, useEffect} from 'react';
import {createDrawerNavigator} from '@react-navigation/drawer';
import HomeStack from '../NavigationStack/HomeStack';
import Log_out from '../Screens/Log_out';
import CustomDrawer from '../Components/CustomDrawer';
import CustomDrawer_Customer from '../Components/CustomerComponents/CustomDrawer_Customer';
import Barber_Drawer_Components from '../Components/Barber_Section_Components/Barber_Drawer_Components';
import SettingsStack from '../NavigationStack/SettingsStack';
import ManagementStack from '../NavigationStack/ManagementStack';
import Book_NowStack from '../NavigationStack/CustomerNavigationStack/Book_NowStack';
import Treatment_Stack from '../NavigationStack/CustomerNavigationStack/Treatment_Stack';
import Customer_Product_Stack from '../NavigationStack/CustomerNavigationStack/Customer_Product_Stack';
import Gift_Card_Stack from '../NavigationStack/CustomerNavigationStack/Gift_Card_stack';
import Bookings_Stack from '../NavigationStack/CustomerNavigationStack/Bookings_Stack';
import Track_Orders_Stack from '../NavigationStack/CustomerNavigationStack/Track_Orders_Stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Contact_Stack from '../NavigationStack/CustomerNavigationStack/Contact_Stack';
import Settings_Stack from '../NavigationStack/CustomerNavigationStack/Settings_Stack';
import Personal_Area_Stack from '../NavigationStack/CustomerNavigationStack/Personal_Area_Stack';
import Barber_Home_Stack from '../NavigationStack/Barber_Navigation_Stack/Barber_Home_Stack';
import Barber_Settings_Stack from '../NavigationStack/Barber_Navigation_Stack/Barber_Settings_Stack';
import Barber_Contact_Us from '../NavigationStack/Barber_Navigation_Stack/Barber_Contact_Us';
import Appointment_Stack from '../NavigationStack/Appointment_Stack';
const Drawer = createDrawerNavigator();

const DrawerNavigator = () => {
  const [userRole, setUserRole] = useState(null);

  useEffect(() => {
    const getUserRole = async () => {
      try {
        const userData = await AsyncStorage.getItem('userData');
        if (userData) {
          const {role} = JSON.parse(userData);
          setUserRole(role);
        }
      } catch (error) {
        console.error('Error getting user role:', error);
      }
    };

    getUserRole();
  }, []);

  if (userRole === 'admin') {
    return (
      <Drawer.Navigator
        initialRouteName="Home"
        drawerContent={props => <CustomDrawer {...props} />}
        screenOptions={{
          headerShown: false,
          drawerStyle: {
            width: '70%',
          },
          drawerType: 'slide',
        }}>
        <Drawer.Screen name="Home" component={HomeStack} />
        <Drawer.Screen name="Appointment" component={Appointment_Stack} />
        <Drawer.Screen name="Settings" component={SettingsStack} />
        <Drawer.Screen name="Management" component={ManagementStack} />
        <Drawer.Screen name="Log out" component={Log_out} />
      </Drawer.Navigator>
    );
  } else if (userRole === 'customer') {
    return (
      <Drawer.Navigator
        initialRouteName="Book_now"
        drawerContent={props => <CustomDrawer_Customer {...props} />}
        screenOptions={{
          headerShown: false,
          drawerStyle: {
            width: '70%',
          },
          drawerType: 'slide',
        }}>
        <Drawer.Screen name="Book_now" component={Book_NowStack} />
        <Drawer.Screen name="Treatments" component={Treatment_Stack} />
        <Drawer.Screen name="Products" component={Customer_Product_Stack} />
        <Drawer.Screen name="Gift_Card" component={Gift_Card_Stack} />
        <Drawer.Screen name="Personal_Area" component={Personal_Area_Stack} />
        <Drawer.Screen name="Bookings" component={Bookings_Stack} />
        <Drawer.Screen name="Track_Orders" component={Track_Orders_Stack} />
        <Drawer.Screen name="Contact" component={Contact_Stack} />
        <Drawer.Screen name="Settings" component={Settings_Stack} />
      </Drawer.Navigator> 
    );
  } else if (userRole === 'barber') {
    return (
      <Drawer.Navigator
        initialRouteName="BarberHome"
        drawerContent={props => <Barber_Drawer_Components {...props} />}
        screenOptions={{
          headerShown: false,
          drawerStyle: {
            width: '70%',
          },
          drawerType: 'slide',
        }}>
        <Drawer.Screen name="BarberHome" component={Barber_Home_Stack} />
        <Drawer.Screen name="BarberSettingsStack" component={Barber_Settings_Stack } />
        <Drawer.Screen name="BarberContactUs" component={Barber_Contact_Us} />
        <Drawer.Screen name="Log out" component={Log_out} />
      </Drawer.Navigator>
    );
  }

  return null; // or a loading component
};

export default DrawerNavigator;
