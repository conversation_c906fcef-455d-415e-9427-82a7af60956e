import { globalpath } from "../Custom/globalpath";

export const Product_mock = [
    {
      id: 1,
      title: 'Pre Cleaning',
      image: globalpath.back1,
    },
    {
      id: 2,
      title: 'Hair Cut',
      image: globalpath.back2,
    },
    {
      id: 3,
      title: 'Hair Color',
      image: globalpath.back3,
    },
    {
      id: 4,
      title: 'Hair Style',
      image: globalpath.back4,
    },
    {
      id: 5,
      title: 'Facial',
      image: globalpath.back5,
    },
    {
      id: 6,
      title: 'Manicure',
      image: globalpath.back6,
    },
    {
      id: 7,
      title: 'Pedicure',
      image: globalpath.back7,
    },
    {
      id: 8,
      title: 'Hair Cut',
      image: globalpath.back8,
    },
    {
      id: 9,
      title: 'Hair Cut',
      image: globalpath.back9,
    }
  ]; 

  export const Product_Details_mock = [
    {
      id: 1,
      image: globalpath.product1,
      subimage: globalpath.product1,
      subimage2: globalpath.product2,
      title: 'No.201 - Hydrating & Repairing Conditioner',
      price: '18.50',
      size: "250ml",
      stock: "100",
      prod_description: "A deeply hydrating conditioner that repairs dry and damaged hair, leaving it smooth and silky."
    },
    {
      id: 2,
      image: globalpath.product2,
      subimage: globalpath.product1,
      subimage2: globalpath.product2,
      title: 'No.202 - Volumizing & Strengthening Shampoo',
      price: '20.00',
      size: "500ml",
      stock: "100",
      prod_description: "A lightweight shampoo that adds volume and strengthens hair from the roots while keeping it fresh and clean."
    },
    {
      id: 3,
      image: globalpath.product3,
      subimage: globalpath.product1,
      subimage2: globalpath.product2,
      title: 'No.203 - Soothing & Nourishing Hair Mask',
      price: '22.90',
      size: "250ml",
      stock: "60",
      prod_description: "A rich hair mask infused with natural oils to soothe the scalp and deeply nourish dry, brittle hair."
    },
    {
      id: 4,
      image: globalpath.product4,
      subimage: globalpath.product1,
      subimage2: globalpath.product2,
      title: 'No.204 - Revitalizing & Shine Serum',
      price: '24.99',
      size: "1l",
      stock: "10",
      prod_description: "A lightweight serum that revitalizes dull hair, adds shine, and tames frizz without weighing it down."
    }
  ];

  export const Gift_Mock = [
    {
      id: 1,
      image: globalpath.card,
      title: 'Digital Voucher or Physical Card',
      description: "Choose between a convenient digital voucher or a beautifully designed physical gift card, perfect for any occasion.",
      amount: "40"
    },
    {
      id: 2,
      image: globalpath.card,
      title: 'Exclusive Holiday Gift Card',
      description: "Celebrate the holiday season with this special edition gift card, ideal for spreading joy and appreciation.",
      amount: "50"
    },
    {
      id: 3,
      image: globalpath.card,
      title: 'Luxury Shopping Gift Card',
      description: "A premium gift card for indulging in luxurious beauty and wellness products, making every purchase special.",
      amount: "75"
    },
    {
      id: 4,
      image: globalpath.card,
      title: 'Personalized Gift Voucher',
      description: "Customize your gift by adding a personal message, making it a truly special surprise for your loved ones.",
      amount: "100"
    },
    {
      id: 5,
      image: globalpath.card,
      title: 'Wellness & Beauty Gift Card',
      description: "The perfect gift for self-care enthusiasts, redeemable for a wide range of wellness and beauty products.",
      amount: "150"
    }
  ];
  
  