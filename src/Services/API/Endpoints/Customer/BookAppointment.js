import api from '../../api_congig';
export const getAvailableBarbers = params => {
  const queryString = new URLSearchParams(params).toString();
  return api.get(`/barbers/available_barber/?${queryString}`);
};
export const getbarbarsAvailability = params => {
  const queryString = new URLSearchParams(params).toString();
  return api.get(`/barber_availability/?${queryString}`);
};
export const checkBarbarAvailability = (queryParams, payload) => {
  const queryString = new URLSearchParams(queryParams).toString();
  const endpoint = `/barber_availability/service_duration_fitting/?${queryString}`;
  return api.post(endpoint, payload);
};

export const customerAppointment = payload => {
  return api.post('/customer_appointment/', payload);
};

export const list_customer_bookings = (id, start_date, end_date) => {
  return api.get(
    `/get_customer_appointments/?customer_id=${id}&start_date=${start_date}&end_date=${end_date}`,
  );
};

export const customer_bookings_History = id => {
  return api.get(`/appointment_history/?customer=${id}`);
};

export const get_barber_deatils_from_id = id => {
  return api.get(`/barbers/?barber=${id}`);
};

export const payment_session_checkout = payload => {
  return api.post('/payments/create-checkout-session/', payload);
};