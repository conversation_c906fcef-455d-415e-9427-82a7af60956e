import api from "../../api_congig";


// ADD customer address -------
export const Add_Address = (payload) => {
    return api.post('/customer_address/', payload);
  };

//Update the customer adress
  export const Update_Address = (payload, id) => {
    return api.put(`/customer_address/${id}/`, payload);
  };


//Delete the Customer Addedd address
export const delete_Address = (id) => {
  return api.delete(`/customer_address/${id}/`,);
};

  //list customers all address 
  export const list_customer_Address = (customerId) => {
    return api.get(`/customer_address/?customer=${customerId}`);
  };