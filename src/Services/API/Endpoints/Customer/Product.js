

// ---------------  PRODUCTS CATEGORY
// List the product

import api from "../../api_congig";

export const list_product_category = () => {
    return api.get('/ecommerce/product_category/');
};

// Add  Product category
export const add_product_category = (payload) => {
    return api.post('/ecommerce/product_category/', payload);
};

// List the Individual Product category
// update and deletee product category:

export const update_list_product_category = (id, payload) => {
  return api.put(`/ecommerce/product_category/${id}/`, payload);
};


export const delete_list_product_category = (id) => {
  return api.delete(`/ecommerce/product_category/${id}/`);
};





// ---------------  PRODUCTS 

// Update the Gift card
export const list_products_by_category = (id,) => {
    return api.get(`/ecommerce/product/?category=${id}`);
};

// Add the product 
export const add_product = (payload) => {
  return api.post('/ecommerce/product/', payload);
};


// Update the Gift card
export const update_product = (id, payload) => {
  return api.put(`/ecommerce/product/${id}/`, payload);
};

// List the Individual Product

export const list_product = (id) => {
  return api.get(`/ecommerce/product/${id}/`);
};





export const delete_giftproduct_name = (id) => {
    return api.delete(`/giftproduct/${id}/`);
  };






// Get all get_add_giftproduct_type
export const get_add_giftproduct_type = () => {
  return api.get('/giftproduct_type/');
};


export const delete_giftproduct_type = (id) => {
    return api.delete(`/giftproduct_type/${id}/`);
  };

// Add the Gift card 
export const add_giftcard_Form = (payload) => {
    return api.post('/giftproduct_options/', payload);
};

// Update the Gift card
export const update_giftcard_Form = (id, payload) => {
    return api.put(`/giftproduct_options/${id}/`, payload);
};

// Get all get_all_giftproduct_name
export const get_all_giftcard = () => {
  return api.get('/giftproduct_options/');
};

// Add  to cart api's 
export const add_to_cart = (payload) => {
  return api.post('/ecommerce/cart/', payload);
};

export const get_add_to_cart = () => {
  return api.get('/ecommerce/cart/');
};

export const add_item_in_cart = ( payload) => {
  return api.put(`/ecommerce/cartitem/add/`, payload);
};

export const remove_item_in_cart = ( payload) => {
  return api.put(`/ecommerce/cartitem/subtract/`, payload);
};

export const order_plac_to_cart = (payload) => {
  return api.post('/ecommerce/order/', payload);
};

export const get_the_order_list = () => {
  return api.get('/ecommerce/order/');
};

export const delete_cart_item= (id) => {
  return api.delete(`/ecommerce/cartitem/${id}/`);
};

export const update_order_status = (id, payload) => {
  return api.put(`/ecommerce/order/${id}/`, payload);
};





