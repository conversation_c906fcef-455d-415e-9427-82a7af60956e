
import api from "../../api_congig";

// Add Comments
export const add_appointment_comments = (payload) => {
    return api.post('/ecommerce/comments/', payload);
};

export const get_customer_comments = (id) => {
    return api.get(`/ecommerce/comments/?customer_appointment=${id}`);
};

export const get_barber_appointment = (id) => {
    return api.get(`/ecommerce/comments/?barber_appointment=${id}`);
};


export const delete_comments = (id) => {
    return api.delete(`/ecommerce/comments/${id}/`);
  };


export const update_comments = (id, payload) => {
    return api.put(`/ecommerce/comments/${id}/`, payload);
};




