import api from '../../api_congig';



// login Endpoints
export const Create_Category = (payload) => {
    return api.post('/category/', payload);
};


// Get all Endpoints
export const get_all_category = () => {
  return api.get('/category/');
};

// Get all Endpoints
export const get_sub_category = (id) => {
  return api.get(`/sub_category/?category_id=${id}`);
};


export const getAppointment = (appointmentId) => {
  return api.get(`/get-appointment/${appointmentId}`);
};

export const Create_SubCategory = (payload) => {
    return api.post('/sub_category/', payload);
};