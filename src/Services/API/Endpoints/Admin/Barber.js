import api from '../../api_congig';

// login Endpoints
export const Add_barber = payload => {
  return api.post('/barbers/', payload);
};



// login Endpoints
export const update_barber = (id, payload) => {
  return api.put(`/barbers/${id}/`, payload);
};

// login Endpoints
export const barber_working_hours = payload => {
  return api.post('/barber_working_hours/set-weekly-hours/', payload);
};

// login Endpoints
export const update_barber_working_hours = (id, payload) => {
  return api.put(`/barber_working_hours/${id}/`, payload);
};

// Get all Endpoints
export const get_all_barber = () => {
  return api.get('/barbers/');
};
// login Endpoints
export const get_serviece_by_barber = (id) => {
  return api.get(`/ecommerce/service_to_barber/?barber=${id}`);
};

// Get all Endpoints
export const get_service = (id) => {
  return api.get(`/services/${id}`);
};

// login Endpoints

export const get_barber_Recent_work = (id) => {
  return api.get(`/combined_appointments/?barber=${id}&status=completed`);
};

