import api from '../../api_congig';

// login Endpoints
export const Create_Service = payload => {
  return api.post('/services/', payload);
};

// login Endpoints
export const Update_Service = (id, payload) => {
  return api.put(`/services/${id}/`, payload);
};

// Get all Endpoints
export const get_all_service = () => {
  return api.get('/services/');
};

// put methode update the services ----
export const update_Service = (id, payload) => {
  return api.put(`/services/${id}/`, payload);
};

export const delete_service = (id,) => {
  return api.delete(`/services/${id}/`);
};

