import api from '../../api_congig';

// ---------------  PRODUCTS CATEGORY
// List the product

export const list_barber_appointments = (start_date, end_date, shopid) => {
  return api.get(
    `/combined_appointments/?start_date=${start_date}&end_date=${end_date}&shop=${shopid}`,
  );
};

export const get_barber_deatils = () => {
  return api.get('/barbers/');
};

export const get_barber_breaks = shopid => {
  return api.get(`/breaks/?shop=${shopid}`);
};

export const get_shops_barbers = id => {
  return api.get(`/barbers/?shop=${id}`);
};

export const get_appointment_history = id => {
  return api.get(`/appointment_history/?customer=${id}`);
};

export const Manage_breaks = payload => {
  return api.post('/breaks/', payload);
};
