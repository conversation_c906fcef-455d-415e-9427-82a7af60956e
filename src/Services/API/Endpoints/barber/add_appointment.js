import api from '../../api_congig';


// Get all Endpoints
export const get_all_service = () => {
    return api.get('/services/');
  };


// Get all Endpoints
export const get_all_customers = () => {
    return api.get('/customers/');
  };

// Get all Endpoints
export const barber_appointment = (payload) => {
  return api.post('/barber_appointment/', payload);
};


export const update_barber_appointment = (id, payload) => {
  return api.put(`/barber_appointment/${id}/`, payload);
};

export const update_customer_appointment = (id, payload) => {
  return api.put(`/customer_appointment/${id}/`, payload);
};






