import api from '../../api_congig';


export const get_service = (id) => {
    return api.get(`/services/${id}/`);
  };

  export const get_customer = (id) => {
    return api.get(`/customers/${id}/`);
  };

  export const get_barber = (id) => {
    return api.get(`/barbers/?shop=${id}`);
  };


  //Update the customer adress
  export const Update_Appointment = (payload, id) => {
    return api.patch(`/customer_appointment/${id}/`, payload);
  };