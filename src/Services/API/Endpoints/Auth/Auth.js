import api from '../../api_congig';



// login Endpoints
export const login = (payload) => {
    return api.post('/login/', payload);
  };


// send_OTP Endpoints
export const send_OTP = (payload) => {
  return api.post('/register_otp/', payload);
};

// verify_OTP Endpoints
export const verify_OTP = (payload) => {
  return api.post('/verify_otp/', payload);
};


// verify_OTP Endpoints
export const create_cutomer_signUP = (payload) => {
  return api.post('/customers/', payload);
};









export const getPatientSummary = (payload) => {
    return api.post('/get_patient_summary', payload);
  };

export const getAppointment = (appointmentId) => {
    return api.get(`/get-appointment/${appointmentId}`);
  };

export const updateAppointment = (payload) => {
    return api.post('/update-appointment', { data: payload });
  };

export const getPDF = (appointmentId) => {
    return api.get(`/get-pdf/${appointmentId}`);
  };



// Add Appoitnment Enpoints
export const deleteConsent = (consentId) => {
    return api.delete(`/delete-consent/${consentId}`);
  };


  export const addAppointment = (payload) => {
    return api.post('/add-appointment', payload);
  };


// Transcribe the Audio
export const transcribeAudio = (payload) => {
    return api.post('/transcribe', payload);
  };


// Filter Appointments
export const filterAppointments = (doctorId, startDate, endDate) => {
    const query = `doctor_id=${doctorId}&start_date=${startDate}&end_date=${endDate}`;
    return api.get(`/filter-appointments?${query}`);
  };


// Create Consent

export const createConsent = (payload) => {
  return api.post('/create-consent', payload);
};

// Payments


export const getCurrentPlan = (doctorId) => {
  return api.get(`/get_current_plan/${doctorId}`);
};

export const getUserPlanLimits = (doctorId) => {
  return api.get(`/user_plan_limits/${doctorId}`);
};

export const getStpUrl = () => {
  return api.get('/stp_url');
};

export const createCustomerPortalSession = (payload) => {
  return api.post('/create-customer-portal-session', payload);
};


// Chats Section
export const fetchDoctorChats = (doctorId) => {
  return api.get(`/list-doctor-chat/${doctorId}`);
};


export const fetchPatientChats = (patientId) => {
  return api.get(`/list-patient-chat/${patientId}`);
};


// Flag Status
export const getSignupStatus = () => {
  return api.get('/signup_status');
};

// createUser
export const createUser = (payload) => {
  return api.post('/create-user', payload);
};
// createDoctorRecord
export const createDoctorRecord = (payload) => {
  return api.post('/create_user_record', payload);
};

// getMessageCounter
export const getMessageCounter = (doctorId) => {
  return api.get(`/get_message_counter/${doctorId}`);
};


export const deviceToken = (payload) => {
  return api.post('/device-tokens/', payload);
};

export const device_token_delete = (payload) => {
    return api.delete('/device-tokens/delete-token/' , payload);
  };

