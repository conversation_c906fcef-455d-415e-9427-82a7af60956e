import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'https://dev-barber.stampasolutions.support';

const getHeaders = async () => {
  const userData = await AsyncStorage.getItem('userData');
  const token = userData ? JSON.parse(userData)?.token : null;
  console.log('token', token);

  return {
    'Content-Type': 'application/json',
    ...(token && {Authorization: `Token ${token}`}),
  };
};

const attachStatus = (data, status) => {
  if (data && typeof data === 'object') {
    Object.defineProperty(data, 'status', {
      value: status,
      enumerable: false, // Won’t show up in map/render loops
      writable: false,
    });
  }
  return data;
};

const api = {
  get: async path => {
    const url = `${BASE_URL}${path}`;
    console.log(`🟡 [GET] Request to: ${url}`);

    try {
      const headers = await getHeaders();
      const response = await fetch(url, {method: 'GET', headers});

      const text = await response.text();
      let data;

      try {
        data = JSON.parse(text);
      } catch (e) {
        console.warn('⚠️ GET response is not JSON. Raw:', text);
        data = text;
      }

      if (!response.ok) {
        console.error(`🔴 [GET] ${url} failed:`, data);
        throw {response: {status: response.status, data}};
      }

      console.log(`🟢 [GET] ${url} success:`, data);
      return attachStatus(data, response.status);
    } catch (error) {
      console.error(`❌ [GET] Error calling ${url}:`, error);
      throw error;
    }
  },

  post: async (path, body = {}) => {
    const url = `${BASE_URL}${path}`;
    const isFormData = body instanceof FormData;
    console.log(
      `🟡 [POST] Request to: ${url}`,
      isFormData ? '[FormData]' : body,
    );

    try {
      const headers = await getHeaders();
      const sessionCookie = await AsyncStorage.getItem('sessionCookie');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...(isFormData ? {Authorization: headers.Authorization} : headers),
          ...(sessionCookie ? {Cookie: sessionCookie} : {}),
        },
        body: isFormData ? body : JSON.stringify(body),
      });

      const setCookie = response.headers.get('set-cookie');
      if (setCookie) {
        const sessionOnly = setCookie.split(';')[0];
        console.log('🍪 Cleaned Session Cookie:', sessionOnly);
        await AsyncStorage.setItem('sessionCookie', sessionOnly);
      }

      const text = await response.text();
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.warn('⚠️ POST response is not JSON. Raw:', text);
        data = text;
      }

      if (!response.ok) {
        console.error(`🔴 [POST] ${url} failed:`, data);
        throw {response: {status: response.status, data}};
      }

      console.log(`🟢 [POST] ${url} success:`, data);
      return attachStatus(data, response.status);
    } catch (error) {
      console.error(`❌ [POST] Error calling ${url}:`, error);
      throw error;
    }
  },

  put: async (path, body = {}) => {
    const url = `${BASE_URL}${path}`;
    const isFormData = body instanceof FormData;
    console.log(
      `🟡 [PUT] Request to: ${url}`,
      isFormData ? '[FormData]' : body,
    );

    try {
      const headers = await getHeaders();
      const sessionCookie = await AsyncStorage.getItem('sessionCookie');

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          ...(isFormData ? {Authorization: headers.Authorization} : headers),
          ...(sessionCookie ? {Cookie: sessionCookie} : {}),
        },
        body: isFormData ? body : JSON.stringify(body),
      });

      const setCookie = response.headers.get('set-cookie');
      if (setCookie) {
        const sessionOnly = setCookie.split(';')[0];
        console.log('🍪 Updated Session Cookie:', sessionOnly);
        await AsyncStorage.setItem('sessionCookie', sessionOnly);
      }

      const text = await response.text();
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.warn('⚠️ PUT response is not JSON. Raw:', text);
        data = text;
      }

      if (!response.ok) {
        console.error(`🔴 [PUT] ${url} failed:`, data);
        throw {response: {status: response.status, data}};
      }

      console.log(`🟢 [PUT] ${url} success:`, data);
      return attachStatus(data, response.status);
    } catch (error) {
      console.error(`❌ [PUT] Error calling ${url}:`, error);
      throw error;
    }
  },

  patch: async (path, body = {}) => {
    const url = `${BASE_URL}${path}`;
    const isFormData = body instanceof FormData;
    console.log(
      `🟡 [PATCH] Request to: ${url}`,
      isFormData ? '[FormData]' : body,
    );
  
    try {
      const headers = await getHeaders();
      const sessionCookie = await AsyncStorage.getItem('sessionCookie');
  
      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          ...(isFormData ? { Authorization: headers.Authorization } : headers),
          ...(sessionCookie ? { Cookie: sessionCookie } : {}),
        },
        body: isFormData ? body : JSON.stringify(body),
      });
  
      const setCookie = response.headers.get('set-cookie');
      if (setCookie) {
        const sessionOnly = setCookie.split(';')[0];
        console.log('🍪 Updated Session Cookie:', sessionOnly);
        await AsyncStorage.setItem('sessionCookie', sessionOnly);
      }
  
      const text = await response.text();
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.warn('⚠️ PATCH response is not JSON. Raw:', text);
        data = text;
      }
  
      if (!response.ok) {
        console.error(`🔴 [PATCH] ${url} failed:`, data);
        throw { response: { status: response.status, data } };
      }
  
      console.log(`🟢 [PATCH] ${url} success:`, data);
      return attachStatus(data, response.status);
    } catch (error) {
      console.error(`❌ [PATCH] Error calling ${url}:`, error);
      throw error;
    }
  },

  delete: async (path, payload = null) => {
  const url = `${BASE_URL}${path}`;
  console.log(`🟡 [DELETE] Request to: ${url}`, payload ? '[With Payload]' : '[No Payload]');

  try {
    const headers = await getHeaders();
    const response = await fetch(url, {
      method: 'DELETE',
      headers,
      ...(payload && { body: JSON.stringify(payload) }), // Attach body only if payload exists
    });

    const text = await response.text();
    let data;

    try {
      data = JSON.parse(text);
    } catch (e) {
      console.warn('⚠️ DELETE response is not JSON. Raw:', text);
      data = text;
    }

    if (!response.ok) {
      console.error(`🔴 [DELETE] ${url} failed:`, data);
      throw { response: { status: response.status, data } };
    }

    console.log(`🟢 [DELETE] ${url} success:`, data);
    return attachStatus(data, response.status);
  } catch (error) {
    console.error(`❌ [DELETE] Error calling ${url}:`, error);
    throw error;
  }
},

};

export default api;
