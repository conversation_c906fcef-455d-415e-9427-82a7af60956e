import { Alert, Platform, PermissionsAndroid } from 'react-native';
import messaging, {AuthorizationStatus} from '@react-native-firebase/messaging';
import notifee, {EventType, AndroidImportance} from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';





export const requestIosPermission = async () => {
  try {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('Authorization status:', authStatus);
      getFCMToken();
    }
  } catch (error) {
    console.error('Error requesting iOS notification permission:', error);
  }
};
export const requestPermissionAndroid = async () => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
    );
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('Notification Permission Granted');
      getFCMToken();
    } else {
      console.log('Notification Permission Denied');
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error);
  }
};

export const getFCMToken = async () => {
  try {
    await messaging().registerDeviceForRemoteMessages(); // Required for iOS
    const token = await messaging().getToken();
    await AsyncStorage.setItem('FCMToken', token);
    console.log('FCM Token:', token);
    return token;
  } catch (error) {
    console.error('Error getting FCM token:', error);
  }
};

export const setupNotificationListeners = () => {
  // Handle foreground FCM messages
  const unsubscribeMessaging = messaging().onMessage(async remoteMessage => {
    displayNotification(remoteMessage);
    console.log('Received FCM message:', remoteMessage);
  });

  // Handle notification press
  const unsubscribeNotifee = notifee.onForegroundEvent(({ type, detail }) => {
    if (type === EventType.PRESS) {
      console.log('User pressed the notification:', detail.notification);
      // You can also navigate or do something specific here
    }
  });
  notifee.onBackgroundEvent(async ({ type, detail }) => {
    if (type === EventType.PRESS) {
      console.log('Notification pressed in background:', detail.notification);
    }
  })
  return () => {
    unsubscribeMessaging();
    unsubscribeNotifee();
  };
};

export const initializeNotifications = async () => {
  if (Platform.OS === 'android') {
    await requestPermissionAndroid();
  } else {
    await requestIosPermission();
  }
};
const displayNotification = async data => {
  console.log('WHAT IS IN ----- >', data);
  const channelId = await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
  });
  await notifee.displayNotification({
    title: data.notification.title,
    body: data.notification.body,
    android: {
      channelId,
      
    },

  });
  
};