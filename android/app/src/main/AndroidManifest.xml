<manifest xmlns:android="http://schemas.android.com/apk/res/android">
 
    <uses-permission android:name="android.permission.INTERNET" />
<!-- For Android 12+ -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
 
 
 
 
    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
     <service
    android:name="com.agontuk.RNFusedLocation.RNFusedLocationService"
    android:foregroundServiceType="location|dataSync"
    android:exported="false" />
     
    <meta-data
  android:name="com.google.android.geo.API_KEY"
  android:value="AIzaSyCbN4OulpWB6EOe-LFdPwkpGALL2rAiN0o" />
 
      <!-- Add this meta-data tag for Google Maps API key -->
     <!-- <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyCbN4OulpWB6EOe-LFdPwkpGALL2rAiN0o"/> -->
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
    </application>
</manifest>